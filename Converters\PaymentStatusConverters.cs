using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FinancialTracker.Converters
{
    /// <summary>
    /// Converter to determine payment status color based on paid amount vs total amount
    /// </summary>
    public class PaymentStatusToColorConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return Colors.Gray;

            if (values[0] is decimal paidAmount && values[1] is decimal totalAmount)
            {
                if (totalAmount <= 0)
                    return Colors.Gray;

                if (paidAmount >= totalAmount)
                    return Colors.Green;      // Fully paid
                else if (paidAmount > 0)
                    return Colors.Orange;     // Partially paid
                else
                    return Colors.Red;        // Unpaid
            }

            return Colors.Gray;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to determine payment status text based on paid amount vs total amount
    /// </summary>
    public class PaymentStatusConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null || values[1] == null)
                return "Unknown";

            if (values[0] is decimal paidAmount && values[1] is decimal totalAmount)
            {
                if (totalAmount <= 0)
                    return "Invalid";

                if (paidAmount >= totalAmount)
                    return "Fully Paid";
                else if (paidAmount > 0)
                    return "Partially Paid";
                else
                    return "Unpaid";
            }

            return "Unknown";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to show/hide elements based on null values
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
