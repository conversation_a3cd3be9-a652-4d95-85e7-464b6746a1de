#nullable enable
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    public class ProjectPaymentTerm
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Category { get; set; } = string.Empty; // "Services" or "Tasks"
        
        [Required]
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty; // e.g., "After Kick-off & Fault MGMT Module installation"
        
        [Column(TypeName = "decimal(5,2)")]
        public decimal Percentage { get; set; } // e.g., 15.00
        
        [MaxLength(300)]
        public string? TriggerCondition { get; set; } // e.g., "On the date of installation certificate for Fault MGMT Module"
        
        public int TermOrder { get; set; } // للترتيب

        public bool IsActive { get; set; } = true;

        /// <summary>
        /// إذا كان true، فإن هذا الشرط يطبق على إجمالي المشروع بغض النظر عن عدد المواقع
        /// إذا كان false، فإن النسبة تقسم على عدد المواقع
        /// </summary>
        public bool IgnoreSiteDivision { get; set; } = false;

        // Calculated property for displaying the amount
        [NotMapped]
        public decimal CalculatedAmount
        {
            get
            {
                if (Project == null) return 0;

                decimal baseAmount = Category switch
                {
                    "Services" => Project.ServicesAmount,
                    "Equipment" => Project.DistributableTasksAmount,
                    "Software Equipment" => Project.SoftwareTasksAmount,
                    "Hardware Equipment" => Project.HardwareTasksAmount,
                    "Spare Parts" => Project.SparePartsAmount,
                    "Extra" => Project.ExtraAmount,
                    // Check if it matches the custom extra category name
                    _ when !string.IsNullOrWhiteSpace(Project.ExtraCategoryName) &&
                           Category.Equals(Project.ExtraCategoryName, System.StringComparison.OrdinalIgnoreCase) => Project.ExtraAmount,
                    _ => Project.DistributableTasksAmount
                };

                return baseAmount * Percentage / 100;
            }
        }

        // Navigation property
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
    }
}
