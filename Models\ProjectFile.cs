#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    public class ProjectFile
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [MaxLength(50)]
        public string Category { get; set; } = "General"; // General, Document, Image, etc.
        
        public long FileSize { get; set; }
        
        [MaxLength(10)]
        public string FileExtension { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        [MaxLength(100)]
        public string UploadedBy { get; set; } = "System";
        
        public bool IsActive { get; set; } = true;
        
        // Navigation property
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
        
        // Computed properties
        [NotMapped]
        public string FileSizeFormatted => FileSize > 0 ? $"{FileSize / 1024.0:F1} KB" : "0 KB";
        
        [NotMapped]
        public string FileType => FileExtension.ToUpper().TrimStart('.');
        
        [NotMapped]
        public string FileTypeColor => FileType switch
        {
            "PDF" => "#D32F2F",
            "DOC" or "DOCX" => "#1976D2",
            "XLS" or "XLSX" => "#388E3C",
            "TXT" => "#757575",
            "JPG" or "JPEG" or "PNG" => "#FF9800",
            _ => "#424242"
        };
    }
}
