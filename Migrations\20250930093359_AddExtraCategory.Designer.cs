﻿// <auto-generated />
using System;
using FinancialTracker.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FinancialTracker.Migrations
{
    [DbContext(typeof(FinancialContext))]
    [Migration("20250930093359_AddExtraCategory")]
    partial class AddExtraCategory
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "6.0.25");

            modelBuilder.Entity("FinancialTracker.Models.Activity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CommitmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Details")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("IconColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int?>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CommitmentId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountUSD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(10,4)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("Commitments");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AmountUSD")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("AppliesAllSites")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ArrivalDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CommitmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(10,4)");

                    b.Property<string>("FixedAmountDetails")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPaid")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPercentageBased")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LetterFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("LetterFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PaidAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("PaidDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PaymentPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProjectSiteId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("SignatureDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("SiteName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("");

                    b.Property<string>("Type")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Task");

                    b.HasKey("Id");

                    b.HasIndex("CommitmentId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProjectSiteId");

                    b.ToTable("Invoices");
                });

            modelBuilder.Entity("FinancialTracker.Models.InvoicePaymentTerm", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AppliedPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<decimal>("CalculatedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<int>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectPaymentTermId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProjectSiteId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SiteName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("ProjectPaymentTermId");

                    b.HasIndex("ProjectSiteId");

                    b.ToTable("InvoicePaymentTerms");
                });

            modelBuilder.Entity("FinancialTracker.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DownPaymentPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<bool>("HasDownPayment")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasExtra")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasSpareParts")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ManualExtraAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ManualHardwareTasksAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ManualServicesAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ManualSoftwareTasksAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ManualSparePartsAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ManualTasksAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("NumberOfSites")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(1);

                    b.Property<decimal>("POAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PODate")
                        .HasColumnType("TEXT");

                    b.Property<string>("POFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("POFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("SplitTasksIntoHardwareAndSoftware")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("Active");

                    b.HasKey("Id");

                    b.ToTable("Projects");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileExtension")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectFiles");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectInvoiceType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DefaultPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("TypeOrder")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectInvoiceTypes");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectPaymentTerm", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IgnoreSiteDivision")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Percentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("TermOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TriggerCondition")
                        .HasMaxLength(300)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectPaymentTerms");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectSite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SiteName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("SiteOrder")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectSites");
                });

            modelBuilder.Entity("FinancialTracker.Models.Reply", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachedFileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttachedFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("CommitmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("InvoiceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CommitmentId");

                    b.HasIndex("InvoiceId");

                    b.ToTable("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Activity", b =>
                {
                    b.HasOne("FinancialTracker.Models.Commitment", "Commitment")
                        .WithMany()
                        .HasForeignKey("CommitmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("FinancialTracker.Models.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Commitment");

                    b.Navigation("Invoice");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("Commitments")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.HasOne("FinancialTracker.Models.Commitment", "Commitment")
                        .WithMany("Invoices")
                        .HasForeignKey("CommitmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("Invoices")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.ProjectSite", "ProjectSite")
                        .WithMany("Invoices")
                        .HasForeignKey("ProjectSiteId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Commitment");

                    b.Navigation("Project");

                    b.Navigation("ProjectSite");
                });

            modelBuilder.Entity("FinancialTracker.Models.InvoicePaymentTerm", b =>
                {
                    b.HasOne("FinancialTracker.Models.Invoice", "Invoice")
                        .WithMany("InvoicePaymentTerms")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.ProjectPaymentTerm", "ProjectPaymentTerm")
                        .WithMany()
                        .HasForeignKey("ProjectPaymentTermId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.ProjectSite", "ProjectSite")
                        .WithMany()
                        .HasForeignKey("ProjectSiteId");

                    b.Navigation("Invoice");

                    b.Navigation("ProjectPaymentTerm");

                    b.Navigation("ProjectSite");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectFile", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectInvoiceType", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("InvoiceTypes")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectPaymentTerm", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("PaymentTerms")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectSite", b =>
                {
                    b.HasOne("FinancialTracker.Models.Project", "Project")
                        .WithMany("Sites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("FinancialTracker.Models.Reply", b =>
                {
                    b.HasOne("FinancialTracker.Models.Commitment", "Commitment")
                        .WithMany("Replies")
                        .HasForeignKey("CommitmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("FinancialTracker.Models.Invoice", "Invoice")
                        .WithMany("Replies")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Commitment");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("FinancialTracker.Models.Commitment", b =>
                {
                    b.Navigation("Invoices");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Invoice", b =>
                {
                    b.Navigation("InvoicePaymentTerms");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("FinancialTracker.Models.Project", b =>
                {
                    b.Navigation("Commitments");

                    b.Navigation("InvoiceTypes");

                    b.Navigation("Invoices");

                    b.Navigation("PaymentTerms");

                    b.Navigation("ProjectFiles");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("FinancialTracker.Models.ProjectSite", b =>
                {
                    b.Navigation("Invoices");
                });
#pragma warning restore 612, 618
        }
    }
}
