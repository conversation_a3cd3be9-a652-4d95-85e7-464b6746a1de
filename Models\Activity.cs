#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    public class Activity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Type { get; set; } = string.Empty; // "Project", "Invoice", "Commitment", "Edit"

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string Details { get; set; } = string.Empty;

        [Required]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [MaxLength(50)]
        public string Icon { get; set; } = "Information";

        [MaxLength(20)]
        public string IconColor { get; set; } = "#673AB7";

        // New fields for enhanced tracking
        [MaxLength(100)]
        public string Action { get; set; } = string.Empty; // "Created", "Updated", "Deleted"

        [MaxLength(200)]
        public string Location { get; set; } = string.Empty; // "Dashboard", "Project Details", "Invoice Dialog"

        [MaxLength(500)]
        public string Reason { get; set; } = string.Empty; // Why this action was performed

        public int SequenceNumber { get; set; } // Auto-incrementing sequence number

        // Foreign keys for related entities
        public int? ProjectId { get; set; }
        public int? InvoiceId { get; set; }
        public int? CommitmentId { get; set; }

        // Navigation properties
        public virtual Project? Project { get; set; }
        public virtual Invoice? Invoice { get; set; }
        public virtual Commitment? Commitment { get; set; }
        
        // Computed properties for display
        [NotMapped]
        public string FormattedDate => Timestamp.ToString("MMM dd, yyyy");

        [NotMapped]
        public string FormattedTime => Timestamp.ToString("HH:mm");

        [NotMapped]
        public string FormattedSequence => $"#{SequenceNumber:D4}";

        [NotMapped]
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - Timestamp;

                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                else if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes}m ago";
                else if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours}h ago";
                else if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays}d ago";
                else if (timeSpan.TotalDays < 30)
                    return $"{(int)(timeSpan.TotalDays / 7)}w ago";
                else if (timeSpan.TotalDays < 365)
                    return $"{(int)(timeSpan.TotalDays / 30)}mo ago";
                else
                    return $"{(int)(timeSpan.TotalDays / 365)}y ago";
            }
        }

        [NotMapped]
        public string NavigationTarget
        {
            get
            {
                return Type switch
                {
                    "Project" => $"Project:{ProjectId}",
                    "Invoice" => $"Invoice:{InvoiceId}:{ProjectId}",
                    "Commitment" => $"Commitment:{CommitmentId}:{ProjectId}",
                    _ => "Dashboard"
                };
            }
        }
    }
    
    // Helper class for activity creation
    public static class ActivityHelper
    {
        public static Activity CreateProjectActivity(string type, Project project, string description = "", string location = "Dashboard", string reason = "")
        {
            var (icon, color, title) = type switch
            {
                "Created" => ("Plus", "#4CAF50", $"New project created: {project.Name}"),
                "Updated" => ("Pencil", "#FF9800", $"Project updated: {project.Name}"),
                "Deleted" => ("Delete", "#F44336", $"Project deleted: {project.Name}"),
                _ => ("FolderOpen", "#2196F3", $"Project action: {project.Name}")
            };

            return new Activity
            {
                Type = "Project",
                Action = type,
                Title = title,
                Description = description.IsNullOrEmpty() ? $"Project: {project.Name}" : description,
                Details = $"Status: {project.Status} | Budget: {project.POAmount:C}",
                Location = location,
                Reason = reason.IsNullOrEmpty() ? GetDefaultReason("Project", type) : reason,
                Icon = icon,
                IconColor = color,
                ProjectId = project.Id,
                Timestamp = DateTime.Now
            };
        }
        
        public static Activity CreateInvoiceActivity(string type, Invoice invoice, string description = "", string location = "Invoice Dialog", string reason = "")
        {
            var (icon, color, title) = type switch
            {
                "Created" => ("Plus", "#4CAF50", $"New invoice created: {invoice.InvoiceNumber}"),
                "Updated" => ("Pencil", "#FF9800", $"Invoice updated: {invoice.InvoiceNumber}"),
                "Deleted" => ("Delete", "#F44336", $"Invoice deleted: {invoice.InvoiceNumber}"),
                _ => ("Receipt", "#2196F3", $"Invoice action: {invoice.InvoiceNumber}")
            };

            return new Activity
            {
                Type = "Invoice",
                Action = type,
                Title = title,
                Description = description.IsNullOrEmpty() ? $"Invoice: {invoice.InvoiceNumber}" : description,
                Details = $"Amount: {invoice.AmountUSD:C} | Date: {invoice.InvoiceDate:MMM dd, yyyy}",
                Location = location,
                Reason = reason.IsNullOrEmpty() ? GetDefaultReason("Invoice", type) : reason,
                Icon = icon,
                IconColor = color,
                InvoiceId = invoice.Id,
                ProjectId = invoice.ProjectId,
                Timestamp = DateTime.Now
            };
        }
        
        public static Activity CreateCommitmentActivity(string type, Commitment commitment, string description = "", string location = "Commitment Dialog", string reason = "")
        {
            var (icon, color, title) = type switch
            {
                "Created" => ("Plus", "#4CAF50", $"New commitment created: {commitment.Title}"),
                "Updated" => ("Pencil", "#FF9800", $"Commitment updated: {commitment.Title}"),
                "Deleted" => ("Delete", "#F44336", $"Commitment deleted: {commitment.Title}"),
                _ => ("Link", "#9C27B0", $"Commitment action: {commitment.Title}")
            };

            return new Activity
            {
                Type = "Commitment",
                Action = type,
                Title = title,
                Description = description.IsNullOrEmpty() ? $"Commitment: {commitment.Title}" : description,
                Details = $"Amount: {commitment.AmountUSD:C} | Type: {commitment.Type}",
                Location = location,
                Reason = reason.IsNullOrEmpty() ? GetDefaultReason("Commitment", type) : reason,
                Icon = icon,
                IconColor = color,
                CommitmentId = commitment.Id,
                ProjectId = commitment.ProjectId,
                Timestamp = DateTime.Now
            };
        }
        
        public static Activity CreateGeneralActivity(string type, string title, string description = "", string details = "", string location = "System", string reason = "")
        {
            var (icon, color) = type switch
            {
                "Login" => ("Login", "#4CAF50"),
                "Logout" => ("Logout", "#FF9800"),
                "Export" => ("Download", "#2196F3"),
                "Import" => ("Upload", "#9C27B0"),
                "Backup" => ("Backup", "#607D8B"),
                _ => ("Information", "#673AB7")
            };

            return new Activity
            {
                Type = type,
                Action = type,
                Title = title,
                Description = description,
                Details = details,
                Location = location,
                Reason = reason.IsNullOrEmpty() ? GetDefaultReason(type, type) : reason,
                Icon = icon,
                IconColor = color,
                Timestamp = DateTime.Now
            };
        }

        private static string GetDefaultReason(string entityType, string actionType)
        {
            return (entityType, actionType) switch
            {
                ("Project", "Created") => "New project setup for business operations",
                ("Project", "Updated") => "Project information or status modification",
                ("Project", "Deleted") => "Project removal from system",
                ("Invoice", "Created") => "New invoice entry for billing purposes",
                ("Invoice", "Updated") => "Invoice details or payment status update",
                ("Invoice", "Deleted") => "Invoice removal or cancellation",
                ("Commitment", "Created") => "New commitment established for project",
                ("Commitment", "Updated") => "Commitment terms or amount modification",
                ("Commitment", "Deleted") => "Commitment cancellation or removal",
                ("Login", "Login") => "User session started",
                ("Export", "Export") => "Data export operation",
                ("Import", "Import") => "Data import operation",
                _ => "System operation performed"
            };
        }
    }
}

// Extension method for string null/empty check
public static class StringExtensions
{
    public static bool IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }
}
