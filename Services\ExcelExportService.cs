using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using FinancialTracker.Models;

namespace FinancialTracker.Services
{
    public class ExcelExportService
    {
        static ExcelExportService()
        {
            // Set EPPlus license for version 5.x
            try
            {
                ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            }
            catch (Exception ex)
            {
                // Log the error but don't crash
                System.Diagnostics.Debug.WriteLine($"EPPlus license setting failed: {ex.Message}");
            }
        }
        public void ExportInvoicesToExcel(List<Invoice> invoices, Dictionary<string, bool> selectedColumns,
            string projectName, string filePath, Project? project = null)
        {

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Invoices");

                // Get headers count for dynamic column spanning
                var headers = GetSelectedHeaders(selectedColumns);
                int lastCol = Math.Max(headers.Count, 8); // Minimum 8 columns for layout

                // ===== PROJECT INFO SECTION (Rows 1-3) =====
                // Calculate totals for summary
                var totalInvoicesAmount = invoices.Sum(i => i.AmountUSD);
                var totalPaid = invoices.Sum(i => i.PaidAmount);

                // Calculate remaining from project total (if project is provided)
                decimal projectTotalAmount = project?.POAmount ?? totalInvoicesAmount;
                decimal totalRemaining = projectTotalAmount - totalInvoicesAmount;

                // Info Box Header - Using app's Indigo color
                worksheet.Cells[1, 1].Value = "📋 PROJECT INFORMATION";
                using (var infoHeaderRange = worksheet.Cells[1, 1, 1, lastCol])
                {
                    infoHeaderRange.Merge = true;
                    infoHeaderRange.Style.Font.Bold = true;
                    infoHeaderRange.Style.Font.Size = 12;
                    infoHeaderRange.Style.Font.Color.SetColor(Color.White);
                    infoHeaderRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    infoHeaderRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1
                    infoHeaderRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    infoHeaderRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    worksheet.Row(1).Height = 28;
                }

                // Project Details (Row 2)
                int infoRow = 2;
                worksheet.Cells[infoRow, 1].Value = "Project:";
                worksheet.Cells[infoRow, 2].Value = projectName;
                worksheet.Cells[infoRow, 3].Value = "Export Date:";
                worksheet.Cells[infoRow, 4].Value = DateTime.Now;
                worksheet.Cells[infoRow, 4].Style.Numberformat.Format = "dd-mmm-yyyy hh:mm AM/PM";

                worksheet.Cells[infoRow, 5].Value = "Total Invoices:";
                worksheet.Cells[infoRow, 6].Value = invoices.Count;

                worksheet.Cells[infoRow, 7].Value = "Period:";
                var minDate = invoices.Any() ? invoices.Min(i => i.InvoiceDate) : DateTime.Now;
                var maxDate = invoices.Any() ? invoices.Max(i => i.InvoiceDate) : DateTime.Now;
                worksheet.Cells[infoRow, 8].Value = $"{minDate:MMM yyyy} - {maxDate:MMM yyyy}";

                // Financial Summary (Row 6)
                infoRow++;

                // Show project total if available
                if (project != null)
                {
                    worksheet.Cells[infoRow, 1].Value = "Project Total:";
                    worksheet.Cells[infoRow, 2].Value = projectTotalAmount;
                    worksheet.Cells[infoRow, 2].Style.Numberformat.Format = "$#,##0.00";
                    worksheet.Cells[infoRow, 2].Style.Font.Bold = true;
                    worksheet.Cells[infoRow, 2].Style.Font.Color.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1
                }
                else
                {
                    worksheet.Cells[infoRow, 1].Value = "Total Invoices:";
                    worksheet.Cells[infoRow, 2].Value = totalInvoicesAmount;
                    worksheet.Cells[infoRow, 2].Style.Numberformat.Format = "$#,##0.00";
                    worksheet.Cells[infoRow, 2].Style.Font.Bold = true;
                    worksheet.Cells[infoRow, 2].Style.Font.Color.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1
                }

                worksheet.Cells[infoRow, 3].Value = "Spent:";
                worksheet.Cells[infoRow, 4].Value = totalInvoicesAmount;
                worksheet.Cells[infoRow, 4].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 4].Style.Font.Bold = true;
                worksheet.Cells[infoRow, 4].Style.Font.Color.SetColor(Color.FromArgb(217, 119, 6)); // Orange #D97706

                worksheet.Cells[infoRow, 5].Value = "Remaining:";
                worksheet.Cells[infoRow, 6].Value = totalRemaining;
                worksheet.Cells[infoRow, 6].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 6].Style.Font.Bold = true;
                worksheet.Cells[infoRow, 6].Style.Font.Color.SetColor(totalRemaining >= 0 ? Color.FromArgb(4, 120, 87) : Color.FromArgb(185, 28, 28)); // Green if positive, Red if negative

                worksheet.Cells[infoRow, 7].Value = "Spent %:";
                var spentPercent = projectTotalAmount > 0 ? (totalInvoicesAmount / projectTotalAmount) : 0;
                worksheet.Cells[infoRow, 8].Value = spentPercent;
                worksheet.Cells[infoRow, 8].Style.Numberformat.Format = "0.0%";
                worksheet.Cells[infoRow, 8].Style.Font.Bold = true;

                // Color code progress (using app colors)
                if (spentPercent >= 0.9m)
                    worksheet.Cells[infoRow, 8].Style.Font.Color.SetColor(Color.FromArgb(185, 28, 28)); // Dark Red #B91C1C (over budget warning)
                else if (spentPercent >= 0.7m)
                    worksheet.Cells[infoRow, 8].Style.Font.Color.SetColor(Color.FromArgb(217, 119, 6)); // Dark Orange #D97706
                else
                    worksheet.Cells[infoRow, 8].Style.Font.Color.SetColor(Color.FromArgb(4, 120, 87)); // Dark Green #047857

                // Breakdown by Type (Row 7) - Spent amounts
                infoRow++;
                var equipmentSpent = invoices.Where(i => i.TypeDisplay == "Equipment" || i.TypeDisplay == "Hardware" || i.TypeDisplay == "Software").Sum(i => i.AmountUSD);
                var servicesSpent = invoices.Where(i => i.TypeDisplay == "SERVICES" || i.TypeDisplay == "Services").Sum(i => i.AmountUSD);
                var otherSpent = invoices.Where(i => i.TypeDisplay != "Equipment" && i.TypeDisplay != "Hardware" && i.TypeDisplay != "Software" && i.TypeDisplay != "SERVICES" && i.TypeDisplay != "Services").Sum(i => i.AmountUSD);

                // Get commitment totals by grouping invoices by their commitment
                decimal equipmentCommitment = 0;
                decimal servicesCommitment = 0;

                // Get unique commitments from invoices
                var commitmentIds = invoices.Where(i => i.CommitmentId.HasValue).Select(i => i.CommitmentId!.Value).Distinct();
                foreach (var commitmentId in commitmentIds)
                {
                    var invoicesInCommitment = invoices.Where(i => i.CommitmentId == commitmentId).ToList();
                    if (invoicesInCommitment.Any())
                    {
                        var firstInvoice = invoicesInCommitment.First();
                        if (firstInvoice.Commitment != null)
                        {
                            var commitmentTypeDisplay = firstInvoice.Commitment.TypeDisplay;
                            if (commitmentTypeDisplay == "Equipment" || commitmentTypeDisplay == "Hardware" || commitmentTypeDisplay == "Software")
                                equipmentCommitment += firstInvoice.Commitment.AmountUSD;
                            else if (commitmentTypeDisplay == "SERVICES" || commitmentTypeDisplay == "Services")
                                servicesCommitment += firstInvoice.Commitment.AmountUSD;
                        }
                    }
                }

                // Calculate remaining for each type
                decimal equipmentRemaining = equipmentCommitment - equipmentSpent;
                decimal servicesRemaining = servicesCommitment - servicesSpent;

                worksheet.Cells[infoRow, 1].Value = "Equipment Spent:";
                worksheet.Cells[infoRow, 2].Value = equipmentSpent;
                worksheet.Cells[infoRow, 2].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 2].Style.Font.Color.SetColor(Color.FromArgb(99, 102, 241)); // Indigo

                worksheet.Cells[infoRow, 3].Value = "Equipment Remaining:";
                worksheet.Cells[infoRow, 4].Value = equipmentRemaining;
                worksheet.Cells[infoRow, 4].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 4].Style.Font.Color.SetColor(equipmentRemaining >= 0 ? Color.FromArgb(4, 120, 87) : Color.FromArgb(185, 28, 28));

                worksheet.Cells[infoRow, 5].Value = "SERVICES Spent:";
                worksheet.Cells[infoRow, 6].Value = servicesSpent;
                worksheet.Cells[infoRow, 6].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 6].Style.Font.Color.SetColor(Color.FromArgb(16, 185, 129)); // Green

                worksheet.Cells[infoRow, 7].Value = "SERVICES Remaining:";
                worksheet.Cells[infoRow, 8].Value = servicesRemaining;
                worksheet.Cells[infoRow, 8].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[infoRow, 8].Style.Font.Color.SetColor(servicesRemaining >= 0 ? Color.FromArgb(4, 120, 87) : Color.FromArgb(185, 28, 28));

                // Style info section labels (odd columns)
                for (int infoCol = 1; infoCol <= lastCol; infoCol += 2)
                {
                    using (var labelRange = worksheet.Cells[2, infoCol, 4, infoCol])
                    {
                        labelRange.Style.Font.Bold = true;
                        labelRange.Style.Font.Size = 10;
                        labelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        labelRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(243, 244, 246)); // Light gray
                        labelRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        labelRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }
                }

                // Style info section values (even columns)
                for (int infoCol = 2; infoCol <= lastCol; infoCol += 2)
                {
                    using (var valueRange = worksheet.Cells[2, infoCol, 4, infoCol])
                    {
                        valueRange.Style.Font.Size = 10;
                        valueRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        valueRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }
                }

                // Add border to entire info section
                using (var infoRange = worksheet.Cells[1, 1, 4, lastCol])
                {
                    infoRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                    infoRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                    infoRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                    infoRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                    infoRange.Style.Border.Top.Color.SetColor(Color.Black);
                    infoRange.Style.Border.Left.Color.SetColor(Color.Black);
                    infoRange.Style.Border.Right.Color.SetColor(Color.Black);
                    infoRange.Style.Border.Bottom.Color.SetColor(Color.Black);
                }

                worksheet.Row(2).Height = 22;
                worksheet.Row(3).Height = 22;
                worksheet.Row(4).Height = 22;

                // ===== DATA TABLE SECTION =====
                // Set up headers starting from row 6
                int headerRow = 6;
                int col = 1;

                // Add row number column if InvoiceNumber is not selected
                bool addRowNumberColumn = !headers.ContainsKey("InvoiceNumber");
                int totalColumns = headers.Count;

                if (addRowNumberColumn)
                {
                    worksheet.Cells[headerRow, col].Value = "#";
                    col++;
                    totalColumns++;
                }

                foreach (var header in headers)
                {
                    worksheet.Cells[headerRow, col].Value = header.Value;
                    col++;
                }

                // Style data table headers (using app's Indigo color)
                using (var range = worksheet.Cells[headerRow, 1, headerRow, totalColumns])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Font.Size = 11;
                    range.Style.Font.Color.SetColor(Color.White);
                    range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1 (app primary)
                    range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    range.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    range.Style.WrapText = true;
                    range.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                    range.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                    range.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                    range.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                    range.Style.Border.Top.Color.SetColor(Color.Black);
                    range.Style.Border.Left.Color.SetColor(Color.Black);
                    range.Style.Border.Right.Color.SetColor(Color.Black);
                    range.Style.Border.Bottom.Color.SetColor(Color.Black);
                    worksheet.Row(headerRow).Height = 35;
                }

                // Add data starting from row 13
                int row = headerRow + 1;
                int rowNumber = 1;
                foreach (var invoice in invoices.OrderBy(i => i.InvoiceDate))
                {
                    col = 1;

                    // Add row number in first column if needed
                    if (addRowNumberColumn)
                    {
                        worksheet.Cells[row, col].Value = $"#{rowNumber}";
                        worksheet.Cells[row, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        worksheet.Cells[row, col].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        worksheet.Cells[row, col].Style.Font.Bold = true;
                        col++;
                    }

                    foreach (var header in headers)
                    {
                        var value = GetInvoiceValue(invoice, header.Key);
                        worksheet.Cells[row, col].Value = value;

                        // Format specific columns
                        FormatCell(worksheet.Cells[row, col], header.Key, value);
                        col++;
                    }

                    rowNumber++;
                    row++;
                }

                // Add borders to entire data table
                if (invoices.Any())
                {
                    using (var range = worksheet.Cells[headerRow, 1, row - 1, totalColumns])
                    {
                        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Top.Color.SetColor(Color.FromArgb(200, 200, 200));
                        range.Style.Border.Left.Color.SetColor(Color.FromArgb(200, 200, 200));
                        range.Style.Border.Right.Color.SetColor(Color.FromArgb(200, 200, 200));
                        range.Style.Border.Bottom.Color.SetColor(Color.FromArgb(200, 200, 200));
                    }

                    // Add alternating row colors for better readability
                    for (int i = headerRow + 1; i < row; i += 2)
                    {
                        using (var range = worksheet.Cells[i, 1, i, totalColumns])
                        {
                            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(248, 250, 252)); // Very light blue-gray
                        }
                    }

                    // Add outer border to data table
                    using (var outerRange = worksheet.Cells[headerRow, 1, row - 1, totalColumns])
                    {
                        outerRange.Style.Border.Top.Style = ExcelBorderStyle.Medium;
                        outerRange.Style.Border.Left.Style = ExcelBorderStyle.Medium;
                        outerRange.Style.Border.Right.Style = ExcelBorderStyle.Medium;
                        outerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
                        outerRange.Style.Border.Top.Color.SetColor(Color.Black);
                        outerRange.Style.Border.Left.Color.SetColor(Color.Black);
                        outerRange.Style.Border.Right.Color.SetColor(Color.Black);
                        outerRange.Style.Border.Bottom.Color.SetColor(Color.Black);
                    }
                }

                // ===== SUMMARY SECTION AT BOTTOM =====
                if (invoices.Any())
                {
                    row += 2;

                    // Summary header (using app's Indigo color)
                    worksheet.Cells[row, 1].Value = "💰 FINANCIAL SUMMARY";
                    using (var headerRange = worksheet.Cells[row, 1, row, 4])
                    {
                        headerRange.Merge = true;
                        headerRange.Style.Font.Bold = true;
                        headerRange.Style.Font.Size = 13;
                        headerRange.Style.Font.Color.SetColor(Color.White);
                        headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        headerRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1 (app primary)
                        headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        headerRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        headerRange.Style.Border.BorderAround(ExcelBorderStyle.Medium, Color.Black);
                        worksheet.Row(row).Height = 25;
                    }

                    row++;

                    // Summary data with professional formatting
                    worksheet.Cells[row, 1].Value = "Total Invoices:";
                    worksheet.Cells[row, 2].Value = totalInvoicesAmount;
                    worksheet.Cells[row, 2].Style.Numberformat.Format = "$#,##0.00";

                    worksheet.Cells[row, 3].Value = "Total Paid:";
                    worksheet.Cells[row, 4].Value = totalPaid;
                    worksheet.Cells[row, 4].Style.Numberformat.Format = "$#,##0.00";

                    row++;

                    worksheet.Cells[row, 1].Value = "Total Remaining:";
                    worksheet.Cells[row, 2].Value = totalRemaining;
                    worksheet.Cells[row, 2].Style.Numberformat.Format = "$#,##0.00";

                    worksheet.Cells[row, 3].Value = "Spent %:";
                    var spentPercentBottom = projectTotalAmount > 0 ? (totalInvoicesAmount / projectTotalAmount) * 100 : 0;
                    worksheet.Cells[row, 4].Value = spentPercentBottom / 100;
                    worksheet.Cells[row, 4].Style.Numberformat.Format = "0.00%";

                    // Style summary labels
                    using (var labelRange = worksheet.Cells[row - 1, 1, row, 1])
                    {
                        labelRange.Style.Font.Bold = true;
                        labelRange.Style.Font.Size = 11;
                        labelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        labelRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(243, 244, 246));
                        labelRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        labelRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    using (var labelRange = worksheet.Cells[row - 1, 3, row, 3])
                    {
                        labelRange.Style.Font.Bold = true;
                        labelRange.Style.Font.Size = 11;
                        labelRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        labelRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(243, 244, 246));
                        labelRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        labelRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    // Style summary values (using app colors)
                    using (var valueRange = worksheet.Cells[row - 1, 2, row, 2])
                    {
                        valueRange.Style.Font.Bold = true;
                        valueRange.Style.Font.Size = 11;
                        valueRange.Style.Font.Color.SetColor(Color.FromArgb(99, 102, 241)); // Indigo #6366F1
                        valueRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        valueRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    using (var valueRange = worksheet.Cells[row - 1, 4, row, 4])
                    {
                        valueRange.Style.Font.Bold = true;
                        valueRange.Style.Font.Size = 11;
                        valueRange.Style.Font.Color.SetColor(Color.FromArgb(4, 120, 87)); // Dark Green #047857
                        valueRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        valueRange.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                    }

                    // Add border to summary section
                    using (var summaryRange = worksheet.Cells[row - 1, 1, row, 4])
                    {
                        summaryRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        summaryRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        summaryRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        summaryRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    }
                }

                // ===== FOOTER SECTION =====
                row += 3;
                worksheet.Cells[row, 1].Value = $"Generated by Financial Tracker";
                using (var footerRange = worksheet.Cells[row, 1, row, lastCol])
                {
                    footerRange.Merge = true;
                    footerRange.Style.Font.Size = 9;
                    footerRange.Style.Font.Italic = true;
                    footerRange.Style.Font.Color.SetColor(Color.Gray);
                    footerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                }

                row++;
                worksheet.Cells[row, 1].Value = $"Report Date: {DateTime.Now:dddd, MMMM dd, yyyy - hh:mm tt}";
                using (var dateRange = worksheet.Cells[row, 1, row, lastCol])
                {
                    dateRange.Merge = true;
                    dateRange.Style.Font.Size = 8;
                    dateRange.Style.Font.Color.SetColor(Color.Gray);
                    dateRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                }

                // Auto-fit columns after all content is added
                worksheet.Cells.AutoFitColumns();

                // Set minimum and maximum column widths for readability
                for (int i = 1; i <= Math.Max(headers.Count, lastCol); i++)
                {
                    if (worksheet.Column(i).Width < 15)
                        worksheet.Column(i).Width = 15;
                    if (worksheet.Column(i).Width > 50)
                        worksheet.Column(i).Width = 50;
                }

                // Freeze header row for easy scrolling
                worksheet.View.FreezePanes(headerRow + 1, 1);

                // Set print options for professional printing
                worksheet.PrinterSettings.Orientation = eOrientation.Landscape;
                worksheet.PrinterSettings.FitToPage = true;
                worksheet.PrinterSettings.FitToWidth = 1;
                worksheet.PrinterSettings.FitToHeight = 0; // Allow multiple pages vertically
                worksheet.PrinterSettings.RepeatRows = worksheet.Cells[$"{headerRow}:{headerRow}"];
                worksheet.PrinterSettings.PrintArea = worksheet.Cells[1, 1, row, Math.Max(headers.Count, lastCol)];

                // Set page margins
                worksheet.PrinterSettings.TopMargin = 0.5m;
                worksheet.PrinterSettings.BottomMargin = 0.5m;
                worksheet.PrinterSettings.LeftMargin = 0.5m;
                worksheet.PrinterSettings.RightMargin = 0.5m;

                // Add worksheet properties
                package.Workbook.Properties.Title = $"Invoice Report - {projectName}";
                package.Workbook.Properties.Author = "Financial Tracker";
                package.Workbook.Properties.Company = "Financial Tracker System";
                package.Workbook.Properties.Subject = "Invoice Financial Report";
                package.Workbook.Properties.Created = DateTime.Now;

                // Save the file
                var fileInfo = new FileInfo(filePath);
                package.SaveAs(fileInfo);
            }
        }

        private Dictionary<string, string> GetSelectedHeaders(Dictionary<string, bool> selectedColumns)
        {
            var allHeaders = new Dictionary<string, string>
            {
                {"InvoiceNumber", "Invoice Number"},
                {"Type", "Invoice Type"},
                {"PaymentTerms", "Payment Terms"},
                {"Site", "Site Location"},
                {"Amount", "Total Amount (USD)"},
                {"ExchangeRate", "Exchange Rate"},
                {"Paid", "Paid Amount (USD)"},
                {"Remaining", "Remaining Amount (USD)"},
                {"DateStatus", "Invoice Date"},
                {"Commitment", "Commitment Number"},
                {"Description", "Invoice Description"}
            };

            return allHeaders.Where(h => selectedColumns.ContainsKey(h.Key) && selectedColumns[h.Key])
                           .ToDictionary(h => h.Key, h => h.Value);
        }

        private object GetInvoiceValue(Invoice invoice, string columnKey)
        {
            return columnKey switch
            {
                "InvoiceNumber" => invoice.InvoiceNumber ?? "",
                "Type" => invoice.TypeDisplay ?? "", // Use TypeDisplay instead of Type
                "PaymentTerms" => invoice.PaymentTermsInfo ?? "",
                "Site" => invoice.SiteName ?? "",
                "Amount" => invoice.AmountUSD,
                "ExchangeRate" => invoice.ExchangeRate,
                "Paid" => invoice.PaidAmount,
                "Remaining" => invoice.RemainingAmount, // Use RemainingAmount property
                "DateStatus" => invoice.ArrivalDate ?? invoice.SignatureDate ?? invoice.InvoiceDate, // Use ArrivalDate first, then SignatureDate, then InvoiceDate
                "Commitment" => invoice.Commitment?.Title ?? "", // Use Commitment Title
                "Description" => invoice.Description ?? "",
                _ => ""
            };
        }

        private void FormatCell(ExcelRange cell, string columnKey, object value)
        {
            // Center align all cells by default (both horizontal and vertical)
            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            cell.Style.VerticalAlignment = ExcelVerticalAlignment.Center;

            switch (columnKey)
            {
                case "Amount":
                case "Paid":
                case "Remaining":
                    cell.Style.Numberformat.Format = "$#,##0.00";
                    cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Keep centered
                    cell.Style.Font.Bold = true;

                    // Color code amounts (using app colors)
                    if (columnKey == "Paid" && value is decimal paid && paid > 0)
                        cell.Style.Font.Color.SetColor(Color.FromArgb(4, 120, 87)); // Dark Green #047857
                    else if (columnKey == "Remaining" && value is decimal remaining && remaining > 0)
                        cell.Style.Font.Color.SetColor(Color.FromArgb(185, 28, 28)); // Dark Red #B91C1C
                    break;

                case "ExchangeRate":
                    cell.Style.Numberformat.Format = "0.0000";
                    // Already centered by default
                    break;

                case "DateStatus":
                    // Format as date only (invoice date)
                    if (value is DateTime)
                    {
                        cell.Style.Numberformat.Format = "yyyy-mm-dd";
                        // Already centered by default
                    }
                    break;

                case "InvoiceNumber":
                    cell.Style.Font.Bold = true;
                    // Already centered by default
                    break;

                case "Type":
                case "Site":
                case "Commitment":
                case "Description":
                case "PaymentTerms":
                    // Already centered by default
                    break;

                default:
                    // All other fields remain centered
                    break;
            }
        }
    }
}
