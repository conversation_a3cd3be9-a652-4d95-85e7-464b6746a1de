#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    /// <summary>
    /// نموذج لربط الفواتير بشروط الدفع المتعددة
    /// يسمح للفاتورة الواحدة أن تحتوي على أكثر من شرط دفع
    /// </summary>
    public class InvoicePaymentTerm
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int InvoiceId { get; set; }
        
        [Required]
        public int ProjectPaymentTermId { get; set; }
        
        /// <summary>
        /// النسبة المئوية المطبقة من شرط الدفع الأصلي
        /// يمكن أن تكون أقل من النسبة الأصلية إذا كان الشرط مقسم على عدة فواتير
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal AppliedPercentage { get; set; }

        /// <summary>
        /// معرف الموقع المحدد لهذا الشرط (اختياري - للفواتير متعددة المواقع)
        /// </summary>
        public int? ProjectSiteId { get; set; }

        /// <summary>
        /// اسم الموقع للعرض (اختياري)
        /// </summary>
        [MaxLength(100)]
        public string? SiteName { get; set; }
        
        /// <summary>
        /// المبلغ المحسوب بناءً على النسبة المطبقة
        /// </summary>
        private decimal _calculatedAmount;
        [Column(TypeName = "decimal(18,2)")]
        public decimal CalculatedAmount
        {
            get => _calculatedAmount;
            set => _calculatedAmount = value;
        }
        
        /// <summary>
        /// ملاحظات إضافية خاصة بهذا الشرط في هذه الفاتورة
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }
        
        /// <summary>
        /// ترتيب عرض شرط الدفع في الفاتورة
        /// </summary>
        public int DisplayOrder { get; set; } = 1;
        
        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// هل هذا الشرط نشط أم لا
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;

        [ForeignKey("ProjectPaymentTermId")]
        public virtual ProjectPaymentTerm ProjectPaymentTerm { get; set; } = null!;

        [ForeignKey("ProjectSiteId")]
        public virtual ProjectSite? ProjectSite { get; set; }
        
        // Computed properties
        [NotMapped]
        public string PaymentTermDescription => ProjectPaymentTerm?.Description ?? "";
        
        [NotMapped]
        public string PaymentTermCategory => ProjectPaymentTerm?.Category ?? "";
        
        [NotMapped]
        public decimal OriginalPercentage => ProjectPaymentTerm?.Percentage ?? 0;
        
        [NotMapped]
        public string TriggerCondition => ProjectPaymentTerm?.TriggerCondition ?? "";
        
        /// <summary>
        /// Detailed display of payment term with applied percentage
        /// </summary>
        [NotMapped]
        public string DetailedDescription
        {
            get
            {
                var desc = PaymentTermDescription;
                var percentageInfo = AppliedPercentage != OriginalPercentage
                    ? $"({AppliedPercentage}% of {OriginalPercentage}% original)"
                    : $"({AppliedPercentage}%)";

                // Site information is not included here as it has its own column
                return $"{desc} {percentageInfo}";
            }
        }

        /// <summary>
        /// النسبة المئوية المعروضة للمستخدم (مقسمة على عدد المواقع إذا لزم الأمر)
        /// </summary>
        [NotMapped]
        public decimal DisplayPercentage { get; set; }

        /// <summary>
        /// تحديث النسبة المعروضة بناءً على عدد المواقع
        /// </summary>
        public void UpdateDisplayPercentage(int numberOfSites)
        {
            if (numberOfSites > 1)
            {
                DisplayPercentage = AppliedPercentage / numberOfSites;
            }
            else
            {
                DisplayPercentage = AppliedPercentage;
            }
        }

        /// <summary>
        /// وصف مع النسبة المعروضة للمستخدم
        /// </summary>
        [NotMapped]
        public string DisplayDescription
        {
            get
            {
                var desc = PaymentTermDescription;
                return $"{desc} ({DisplayPercentage}%)";
            }
        }
        
        /// <summary>
        /// حساب المبلغ بناءً على مبلغ أساسي ونسبة مئوية
        /// </summary>
        /// <param name="baseAmount">المبلغ الأساسي</param>
        /// <param name="numberOfSites">عدد المواقع للتقسيم</param>
        public void CalculateAmount(decimal baseAmount, int numberOfSites = 1)
        {
            var amount = baseAmount * AppliedPercentage / 100;

            // تقسيم على عدد المواقع إذا كان أكثر من موقع واحد
            // ولكن فقط إذا لم يكن شرط الدفع مضبوط لتجاهل تقسيم المواقع
            if (numberOfSites > 1 && !(ProjectPaymentTerm?.IgnoreSiteDivision == true))
            {
                amount = amount / numberOfSites;
            }

            CalculatedAmount = amount;
        }
    }
}
