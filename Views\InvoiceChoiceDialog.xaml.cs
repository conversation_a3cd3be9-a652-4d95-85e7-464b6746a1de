#nullable enable
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    // Wrapper class for Invoice with selection support
    public class SelectableInvoice : INotifyPropertyChanged
    {
        private bool _isSelected;

        public Invoice Invoice { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        // Expose Invoice properties for binding
        public string InvoiceNumber => Invoice.InvoiceNumber;
        public decimal AmountUSD => Invoice.AmountUSD;
        public string Description => Invoice.Description;
        public DateTime InvoiceDate => Invoice.InvoiceDate;

        public SelectableInvoice(Invoice invoice)
        {
            Invoice = invoice;
            _isSelected = false;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public partial class InvoiceChoiceDialog : Window
    {
        private int _projectId;
        private int _commitmentId;
        private ObservableCollection<SelectableInvoice> _selectableInvoices = new ObservableCollection<SelectableInvoice>();

        public InvoiceChoiceDialog(int projectId, int commitmentId)
        {
            InitializeComponent();
            _projectId = projectId;
            _commitmentId = commitmentId;
            LoadAvailableInvoices();
            UpdateSelectionCount();
        }

        private void ExistingInvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Not used anymore - using checkboxes instead
        }

        private void InvoiceCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            UpdateSelectionCount();
        }

        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (var item in _selectableInvoices)
            {
                item.IsSelected = true;
            }
            UpdateSelectionCount();
        }

        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            foreach (var item in _selectableInvoices)
            {
                item.IsSelected = false;
            }
            UpdateSelectionCount();
        }

        private void UpdateSelectionCount()
        {
            var selectedCount = _selectableInvoices.Count(i => i.IsSelected);

            if (selectedCount == 0)
            {
                SelectedCountText.Text = "0 selected";
                SelectionInfoText.Text = "Select invoices by clicking on the checkboxes";
            }
            else if (selectedCount == 1)
            {
                SelectedCountText.Text = "1 selected";
                SelectionInfoText.Text = "1 invoice will be linked to this commitment";
            }
            else
            {
                SelectedCountText.Text = $"{selectedCount} selected";
                SelectionInfoText.Text = $"{selectedCount} invoices will be linked to this commitment";
            }
        }

        private async void LoadAvailableInvoices()
        {
            try
            {
                // Get commitment details to filter by type
                var commitment = await App.DataService.GetCommitmentByIdAsync(_commitmentId);
                var commitmentType = commitment?.TypeDisplay ?? "";

                var allInvoices = await App.DataService.GetInvoicesAsync();
                var availableInvoices = allInvoices.Where(i =>
                    i.ProjectId == _projectId &&
                    i.CommitmentId == null &&
                    IsTypeMatch(i.TypeDisplay, commitmentType)).ToList(); // Filter by matching type

                // Convert to SelectableInvoice
                _selectableInvoices.Clear();
                foreach (var invoice in availableInvoices)
                {
                    _selectableInvoices.Add(new SelectableInvoice(invoice));
                }

                ExistingInvoicesDataGrid.ItemsSource = _selectableInvoices;

                // Update UI based on available invoices
                if (_selectableInvoices.Count == 0)
                {
                    // No available invoices, show message
                    var noInvoicesMessage = new TextBlock
                    {
                        Text = "No available invoices in this project (all invoices are already linked)",
                        FontSize = 14,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Opacity = 0.7,
                        TextWrapping = TextWrapping.Wrap,
                        TextAlignment = TextAlignment.Center
                    };

                    var grid = ExistingInvoicesDataGrid.Parent as Grid;
                    if (grid != null)
                    {
                        grid.Children.Remove(ExistingInvoicesDataGrid);
                        grid.Children.Add(noInvoicesMessage);
                        Grid.SetRow(noInvoicesMessage, 2);
                    }
                }

                UpdateSelectionCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            // First, check if the commitment has any amount
            try
            {
                var commitment = await App.DataService.GetCommitmentByIdAsync(_commitmentId);
                if (commitment == null)
                {
                    MessageBox.Show("Commitment not found.", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (commitment.AmountUSD <= 0)
                {
                    MessageBox.Show("Cannot add an invoice to this commitment because it does not contain a financial amount.", "Warning",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error verifying commitment: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Find TabControl
            var tabControl = FindVisualChild<TabControl>(this);

            if (tabControl != null && tabControl.SelectedIndex == 0)
            {
                // Existing invoices tab is selected
                var selectedInvoices = _selectableInvoices.Where(si => si.IsSelected).Select(si => si.Invoice).ToList();

                if (!selectedInvoices.Any())
                {
                    MessageBox.Show("Please select at least one invoice from the list by checking the checkboxes.", "Warning",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Confirm linking
                var confirmMessage = selectedInvoices.Count == 1
                    ? $"Do you want to link invoice '{selectedInvoices[0].InvoiceNumber}' to this commitment?"
                    : $"Do you want to link {selectedInvoices.Count} invoices to this commitment?";

                var confirmResult = MessageBox.Show(confirmMessage, "Confirm Link",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (confirmResult != MessageBoxResult.Yes)
                    return;

                try
                {
                    int successCount = 0;
                    int failCount = 0;
                    string errorMessages = "";

                    // Link all selected invoices
                    foreach (var invoice in selectedInvoices)
                    {
                        try
                        {
                            invoice.CommitmentId = _commitmentId;
                            await App.DataService.UpdateInvoiceAsync(invoice);
                            successCount++;
                        }
                        catch (Exception ex)
                        {
                            failCount++;
                            errorMessages += $"\n• {invoice.InvoiceNumber}: {ex.Message}";
                        }
                    }

                    // Show result
                    if (failCount == 0)
                    {
                        var successMessage = successCount == 1
                            ? "Invoice linked to commitment successfully!"
                            : $"{successCount} invoices linked to commitment successfully!";

                        MessageBox.Show(successMessage, "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        var mixedMessage = $"Linked: {successCount}\nFailed: {failCount}\n\nErrors:{errorMessages}";
                        MessageBox.Show(mixedMessage, "Partial Success",
                            MessageBoxButton.OK, MessageBoxImage.Warning);

                        // Refresh the list to show remaining invoices
                        LoadAvailableInvoices();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error linking invoices: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                // Create new invoice tab is selected
                try
                {
                    var dialog = new InvoiceDialog(null, _projectId, _commitmentId);
                    if (dialog.ShowDialog() == true)
                    {
                        DialogResult = true;
                        Close();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating invoice: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Helper method to match invoice type with commitment type
        private bool IsTypeMatch(string invoiceType, string commitmentType)
        {
            // Direct match
            if (invoiceType.Equals(commitmentType, StringComparison.OrdinalIgnoreCase))
                return true;

            // Normalize types for comparison
            var normalizedInvoiceType = invoiceType.ToLower();
            var normalizedCommitmentType = commitmentType.ToLower();

            // Services match
            if ((normalizedInvoiceType.Contains("service") && normalizedCommitmentType.Contains("service")))
                return true;

            // Equipment/Hardware/Software match
            if ((normalizedInvoiceType.Contains("equipment") || normalizedInvoiceType.Contains("hardware") || normalizedInvoiceType.Contains("software")) &&
                (normalizedCommitmentType.Contains("equipment") || normalizedCommitmentType.Contains("hardware") || normalizedCommitmentType.Contains("software")))
                return true;

            // Spare Parts match
            if ((normalizedInvoiceType.Contains("spare") || normalizedInvoiceType.Contains("part")) &&
                (normalizedCommitmentType.Contains("spare") || normalizedCommitmentType.Contains("part")))
                return true;

            return false;
        }

        // Helper method to find child controls
        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }
    }
}
