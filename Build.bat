@echo off
title Financial Tracker - Build
echo.
echo ========================================
echo    Financial Tracker - Quick Build
echo ========================================
echo.

:: Delete old exe
if exist "FinancialTracker_Updated.exe" (
    echo Deleting old exe...
    del /f /q "FinancialTracker_Updated.exe"
)

:: Build new version
echo Building new version...
dotnet publish FinancialTracker.csproj --configuration Release --runtime win-x64 --self-contained true --output "publish" --verbosity minimal

:: Copy new exe
if exist "publish\FinancialTracker.exe" (
    copy /y "publish\FinancialTracker.exe" "FinancialTracker_Updated.exe"
    echo.
    echo  Build completed successfully!
    echo New exe: FinancialTracker_Updated.exe
    
    :: Clean up
    rmdir /s /q "publish"
) else (
    echo  Build failed!
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
