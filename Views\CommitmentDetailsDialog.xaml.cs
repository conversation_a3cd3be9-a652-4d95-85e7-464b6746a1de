using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace FinancialTracker.Views
{
    public partial class CommitmentDetailsDialog : Window
    {
        private Commitment _commitment;

        public CommitmentDetailsDialog(Commitment commitment)
        {
            InitializeComponent();
            _commitment = commitment;
            DataContext = _commitment;
            
            Title = $"Commitment Details - {_commitment.Title}";
            LoadCommitmentDetails();
        }

        private async void LoadCommitmentDetails()
        {
            try
            {
                // Reload commitment with all related data
                var commitments = await App.DataService.GetCommitmentsByProjectAsync(_commitment.ProjectId);
                var updatedCommitment = commitments.FirstOrDefault(c => c.Id == _commitment.Id);
                if (updatedCommitment != null)
                {
                    _commitment = updatedCommitment;
                }
                
                if (_commitment != null)
                {
                    DataContext = _commitment;
                    InvoicesDataGrid.ItemsSource = _commitment.Invoices?.OrderByDescending(i => i.CreatedDate);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitment details: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|Text files (*.txt)|*.txt",
                    DefaultExt = "csv",
                    FileName = $"Commitment_{_commitment.Id}_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    ExportToCSV(saveDialog.FileName);
                    MessageBox.Show("Report exported successfully!", "Export Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting report: {ex.Message}", "Export Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToCSV(string filePath)
        {
            var csv = new StringBuilder();
            
            // Header information
            csv.AppendLine("COMMITMENT DETAILS REPORT");
            csv.AppendLine($"Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            csv.AppendLine();
            
            // Commitment summary
            csv.AppendLine("COMMITMENT SUMMARY");
            csv.AppendLine($"Title,{_commitment.Title}");
            csv.AppendLine($"Type,{_commitment.Type}");
            csv.AppendLine($"Total Amount,${_commitment.AmountUSD:N2}");
            csv.AppendLine($"Total Amount (EGP),{_commitment.AmountEGP:N0}");
            csv.AppendLine($"Exchange Rate,{_commitment.ExchangeRate:F2}");
            csv.AppendLine($"Total Invoiced,${_commitment.TotalInvoicedAmount:N2}");
            csv.AppendLine($"Total Invoiced (EGP),{_commitment.TotalInvoicedAmountEGP:N0}");
            csv.AppendLine($"Total Paid,${_commitment.TotalPaidAmount:N2}");
            csv.AppendLine($"Total Paid (EGP),{_commitment.TotalPaidAmountEGP:N0}");
            csv.AppendLine($"Remaining Amount (Old Method),${_commitment.RemainingCommitmentAmount:N2}");
            csv.AppendLine($"Remaining Amount (Corrected),${_commitment.RemainingCommitmentAmountCorrected:N2}");
            csv.AppendLine($"Remaining Amount (EGP),{(_commitment.AmountEGP - _commitment.TotalInvoicedAmountEGP):N0}");
            csv.AppendLine($"Completion Percentage,{_commitment.CompletionPercentage:F1}%");
            csv.AppendLine($"Number of Invoices,{_commitment.InvoicesCount}");
            csv.AppendLine($"Paid Invoices,{_commitment.PaidInvoicesCount}");
            csv.AppendLine($"Unpaid Invoices,{_commitment.UnpaidInvoicesCount}");
            csv.AppendLine();
            
            // Invoices details
            csv.AppendLine("INVOICES DETAILS");
            csv.AppendLine("Invoice Number,Type,Amount USD,Paid Amount,Remaining Amount,Site,Signature Date,Status,Description");
            
            if (_commitment.Invoices?.Any() == true)
            {
                foreach (var invoice in _commitment.Invoices.OrderBy(i => i.InvoiceNumber))
                {
                    var status = invoice.PaidAmount >= invoice.AmountUSD ? "Fully Paid" :
                                invoice.PaidAmount > 0 ? "Partially Paid" : "Unpaid";
                    
                    csv.AppendLine($"{invoice.InvoiceNumber}," +
                                  $"{invoice.TypeDisplay}," +
                                  $"${invoice.AmountUSD:N2}," +
                                  $"${invoice.PaidAmount:N2}," +
                                  $"${invoice.RemainingAmount:N2}," +
                                  $"{invoice.SiteName ?? "N/A"}," +
                                  $"{invoice.SignatureDate?.ToString("yyyy-MM-dd") ?? "Not Signed"}," +
                                  $"{status}," +
                                  $"\"{invoice.Description?.Replace("\"", "\"\"") ?? ""}\"");
                }
            }
            else
            {
                csv.AppendLine("No invoices found for this commitment");
            }
            
            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_commitment != null && !string.IsNullOrEmpty(_commitment.AttachedFilePath))
                {
                    string fullPath = App.FileService.GetFullPath(_commitment.AttachedFilePath);
                    if (File.Exists(fullPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("File not found. The file may have been moved or deleted.", "File Not Found",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("No file attached to this commitment.", "No File",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening file: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
