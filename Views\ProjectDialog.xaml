<Window x:Class="FinancialTracker.ProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Project Details"
        WindowState="Maximized"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Padding="24,20" Margin="0,0,0,24" Background="#4A5568" CornerRadius="4">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="FolderPlus" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock Text="Project Information" FontSize="24" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock Text="Create and manage project details with comprehensive settings"
                          FontSize="13" Foreground="#E5E7EB" HorizontalAlignment="Center" Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="12">
            <StackPanel Margin="0">

                <!-- Step 1: Basic Project Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="1" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Basic Project Information" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Enter project name, status and description" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Project Name -->
                            <TextBox x:Name="NameTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Project Name *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     MaxLength="200" Height="56" Margin="0,0,8,0"
                                     FontSize="14" VerticalContentAlignment="Center"
                                     TextChanged="NameTextBox_TextChanged"/>

                            <!-- Project Status -->
                            <ComboBox x:Name="StatusComboBox" Grid.Column="1"
                                      materialDesign:HintAssist.Hint="Project Status *"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      Height="56" Margin="8,0,0,0" FontSize="14"
                                      SelectionChanged="StatusComboBox_SelectionChanged">
                                <ComboBoxItem Content="Active"/>
                                <ComboBoxItem Content="On Hold"/>
                                <ComboBoxItem Content="Completed"/>
                                <ComboBoxItem Content="Cancelled"/>
                            </ComboBox>
                        </Grid>

                        <!-- Project Description -->
                        <TextBox x:Name="DescriptionTextBox"
                                 materialDesign:HintAssist.Hint="Project Description (Optional)"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 MaxLength="1000" Height="100" Margin="0,16,0,0"
                                 FontSize="14" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step1CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 1 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step1CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 2: Site Settings -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step2Card" Opacity="1.0">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="2" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Project Sites" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Configure project sites and locations" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left Column: Number of Sites -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="NumberOfSitesTextBox"
                                         materialDesign:HintAssist.Hint="Number of Sites *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Text="1" MaxLength="2" Height="56" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="NumberOfSitesTextBox_TextChanged"/>

                                <TextBlock Text="Enter the total number of project sites"
                                          FontSize="11" Foreground="#6B7280" Margin="0,4,0,0" TextWrapping="Wrap"/>
                            </StackPanel>

                            <!-- Right Column: Auto Creation Info -->
                            <Border Grid.Column="1" Background="#FEF7F0" BorderBrush="#FB923C" BorderThickness="1" CornerRadius="6" Padding="16" Margin="8,0,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <materialDesign:PackIcon Kind="AutoFix" Width="18" Height="18" Foreground="#EA580C" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Auto Site Creation" FontWeight="Medium" FontSize="13" Foreground="#9A3412"/>
                                    </StackPanel>
                                    <TextBlock Text="Sites will be created automatically as:"
                                               FontSize="12" Foreground="#C2410C" Margin="0,0,0,4"/>
                                    <TextBlock Text="Site 1, Site 2, Site 3..."
                                               FontSize="11" Foreground="#EA580C" FontWeight="Medium" Margin="0,0,0,4"/>
                                    <TextBlock Text="You can rename them later in Project Details"
                                               FontSize="11" Foreground="#C2410C"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step2CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 2 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step2CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 3: Purchase Order Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step3Card" Opacity="1.0">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="3" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Financial Details" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Enter PO date, amount and attach PO file" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left Column: PO Date & Amount -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <!-- PO Date -->
                                <DatePicker x:Name="PODatePicker"
                                           materialDesign:HintAssist.Hint="PO Date (Project Start) *"
                                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                           Height="56" FontSize="14" Margin="0,0,0,8"
                                           SelectedDateChanged="PODatePicker_SelectedDateChanged"/>

                                <!-- PO Amount -->
                                <TextBox x:Name="POAmountTextBox"
                                         materialDesign:HintAssist.Hint="Full PO Value (USD) *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" FontSize="14" VerticalContentAlignment="Center"
                                         TextChanged="POAmountTextBox_TextChanged"/>
                            </StackPanel>

                            <!-- Right Column: PO File Attachment -->
                            <Border Grid.Column="1" Background="#F0FDF4" BorderBrush="#22C55E" BorderThickness="1" CornerRadius="6" Padding="16" Margin="8,0,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                        <materialDesign:PackIcon Kind="Attachment" Width="18" Height="18" Foreground="#15803D" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="PO File Attachment" FontWeight="Medium" FontSize="14" Foreground="#15803D"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBox x:Name="POFileTextBox" Grid.Column="0"
                                                 materialDesign:HintAssist.Hint="No PO file selected"
                                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                 IsReadOnly="True" Height="48" FontSize="14"
                                                 VerticalContentAlignment="Center"/>

                                        <Button x:Name="SelectPOFileButton" Grid.Column="1"
                                                Style="{StaticResource MaterialDesignRaisedButton}"
                                                Margin="8,0,0,0" Height="48" Padding="12,0" FontSize="14"
                                                Click="SelectPOFileButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Upload" Width="16" Height="16"
                                                                       VerticalAlignment="Center" Margin="0,0,6,0"/>
                                                <TextBlock Text="Upload" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step3CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 3 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step3CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 4: Tasks Settings -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step4Card" Opacity="1.0">
                    <StackPanel>
                        <!-- Step Header with Split Tasks Option -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Number -->
                            <Border Grid.Column="0" Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="4" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <!-- Step Title -->
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Project Amounts" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Configure equipment and services amounts" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>

                            <!-- Split Tasks CheckBox -->
                            <CheckBox x:Name="SplitTasksCheckBox" Grid.Column="2"
                                      Content="Split into Software &amp; Hardware"
                                      FontWeight="Medium" FontSize="12" VerticalAlignment="Center"
                                      Checked="SplitTasksCheckBox_Changed" Unchecked="SplitTasksCheckBox_Changed"/>
                        </Grid>

                        <!-- Combined Tasks Panel -->
                        <StackPanel x:Name="CombinedTasksPanel">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Total Equipment Amount -->
                                <TextBox x:Name="ManualTasksAmountTextBox" Grid.Column="0"
                                         materialDesign:HintAssist.Hint="Total Equipment Amount - USD *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="0,0,8,0" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>

                                <!-- Services Amount -->
                                <TextBox x:Name="ManualServicesAmountTextBox" Grid.Column="1"
                                         materialDesign:HintAssist.Hint="Services Amount - USD *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="8,0,0,0" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>
                            </Grid>
                        </StackPanel>

                        <!-- Split Tasks Panel -->
                        <StackPanel x:Name="SplitTasksPanel" Visibility="Collapsed">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Software Equipment Amount -->
                                <TextBox x:Name="ManualSoftwareTasksAmountTextBox" Grid.Column="0"
                                         materialDesign:HintAssist.Hint="Software Equipment Amount - USD *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="0,0,8,0" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>

                                <!-- Hardware Equipment Amount -->
                                <TextBox x:Name="ManualHardwareTasksAmountTextBox" Grid.Column="1"
                                         materialDesign:HintAssist.Hint="Hardware Equipment Amount - USD *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="8,0,0,0" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>
                            </Grid>

                            <!-- Services Amount for Split Mode -->
                            <TextBox x:Name="ManualServicesAmountSplitTextBox"
                                     materialDesign:HintAssist.Hint="Services Amount - USD *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     Height="56" Margin="0,8,0,0" FontSize="14"
                                     VerticalContentAlignment="Center"
                                     TextChanged="AmountTextBox_TextChanged"/>


                        </StackPanel>

                        <!-- Calculation Note -->
                        <Border Background="#F0F9FF" BorderBrush="#0EA5E9" BorderThickness="1" CornerRadius="6" Padding="12" Margin="0,16,0,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Calculator" Width="16" Height="16" Foreground="#0EA5E9" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="Note: PO Amount should equal Equipment + Services + Spare Parts amounts"
                                           FontSize="12" Foreground="#0369A1" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step4CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 4 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step4CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 4.5: Additional Options -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="AdditionalOptionsCard" Opacity="1.0">
                    <StackPanel>
                        <!-- Step Header -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Number -->
                            <Border Grid.Column="0" Width="32" Height="32" Background="#8B5CF6" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="+" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <!-- Step Title -->
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Additional Options" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Optional spare parts and down payment settings" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>

                        <!-- Spare Parts Section -->
                        <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="Wrench" Width="18" Height="18" Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Spare Parts Settings" FontWeight="Medium" FontSize="14" Foreground="#374151"/>
                                </StackPanel>

                                <TextBox x:Name="SparePartsAmountTextBox"
                                         materialDesign:HintAssist.Hint="Spare Parts Amount - USD (Leave empty if no spare parts)"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>
                            </StackPanel>
                        </Border>

                        <!-- Extra Section -->
                        <Border Background="#FEF3C7" BorderBrush="#FDE68A" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="StarCircle" Width="18" Height="18" Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Extra Settings (Optional)" FontWeight="Medium" FontSize="14" Foreground="#374151"/>
                                </StackPanel>

                                <TextBox x:Name="ExtraNameTextBox"
                                         materialDesign:HintAssist.Hint="Extra Category Name (e.g., 'Training', 'Consulting', 'Support')"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,8"/>

                                <TextBox x:Name="ExtraAmountTextBox"
                                         materialDesign:HintAssist.Hint="Extra Amount - USD (Leave empty if no extra)"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>
                            </StackPanel>
                        </Border>

                        <!-- Down Payment Section -->
                        <Border Background="#F0FDF4" BorderBrush="#D1FAE5" BorderThickness="1" CornerRadius="6" Padding="16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="18" Height="18" Foreground="#059669" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Down Payment Settings" FontWeight="Medium" FontSize="14" Foreground="#374151"/>
                                </StackPanel>

                                <CheckBox x:Name="HasDownPaymentCheckBox" Content="Include Down Payment for Equipment"
                                          Margin="0,0,0,12" FontSize="13" FontWeight="Medium"
                                          Checked="HasDownPaymentCheckBox_Checked" Unchecked="HasDownPaymentCheckBox_Unchecked"/>

                                <TextBox x:Name="DownPaymentPercentageTextBox"
                                         materialDesign:HintAssist.Hint="Down Payment Percentage (0-100)"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"
                                         IsEnabled="False"/>

                                <TextBlock Text="Note: Down payment will be automatically deducted from equipment total and marked as paid"
                                           FontSize="11" Foreground="#6B7280" Margin="0,8,0,0" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Step 5: Payment Terms Settings -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step5Card" Opacity="1.0">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="5" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Payment Terms Settings" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Add payment terms and configure percentages" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Add Payment Term Form -->
                        <Border Background="#FEF2F2" BorderBrush="#F87171" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Foreground="#DC2626" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Add New Payment Term" FontWeight="Medium" FontSize="14" Foreground="#991B1B"/>
                                </StackPanel>

                                <!-- Single Row Layout for Better Distribution -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="1.5*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Payment Term Description -->
                                    <TextBox x:Name="PaymentTermDescriptionTextBox" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="Payment Term Description"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Height="56" Margin="0,0,8,0" FontSize="14"
                                             VerticalContentAlignment="Center"/>

                                    <!-- Percentage -->
                                    <TextBox x:Name="PaymentTermPercentageTextBox" Grid.Column="1"
                                             materialDesign:HintAssist.Hint="Percentage %"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Height="56" Margin="0,0,8,0" FontSize="14"
                                             VerticalContentAlignment="Center"/>

                                    <!-- Category -->
                                    <ComboBox x:Name="PaymentCategoryComboBox" Grid.Column="2"
                                              materialDesign:HintAssist.Hint="Category"
                                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              Height="56" Margin="0,0,8,0" FontSize="14"/>

                                    <!-- Add Button -->
                                    <Button Grid.Column="3" Style="{StaticResource MaterialDesignRaisedButton}"
                                            Height="56" Padding="16,0" FontSize="14" Background="#DC2626"
                                            Click="AddPaymentTermButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,6,0"/>
                                            <TextBlock Text="Add Term" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>

                                <!-- Additional Details Row -->
                                <TextBox x:Name="PaymentTermDetailsTextBox"
                                         materialDesign:HintAssist.Hint="Additional Details (Optional)"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="0,8,0,0" FontSize="14"
                                         VerticalContentAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Payment Terms Summary -->
                        <Border Background="#F0FDF4" BorderBrush="#22C55E" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <materialDesign:PackIcon Kind="Calculator" Width="18" Height="18" Foreground="#15803D" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Payment Terms Summary" FontWeight="Medium" FontSize="14" Foreground="#15803D"/>
                                </StackPanel>
                                <TextBlock x:Name="PaymentTermsTotalsTextBlock" FontSize="12" FontWeight="Medium"
                                           Text="Totals: Equipment 0% | Services 0% | Software Equipment 0% | Hardware Equipment 0% | Spare Parts 0% (Each category must be ≤ 100%)"
                                           TextWrapping="Wrap" Foreground="#166534"/>
                            </StackPanel>
                        </Border>

                        <!-- Payment Terms List -->
                        <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="12">
                            <ListView x:Name="PaymentTermsListView" BorderThickness="0" Background="Transparent"
                                      ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                      ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                      MinHeight="200" MaxHeight="400"
                                      SizeChanged="PaymentTermsListView_SizeChanged">
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                        <Setter Property="MinHeight" Value="60"/>
                                        <Setter Property="Padding" Value="0,4"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                                <ListView.View>
                                    <GridView>
                                        <!-- Category - 20% -->
                                        <GridViewColumn Header="Category" Width="200">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" Margin="8,4">
                                                        <Border CornerRadius="8" Padding="6,2" HorizontalAlignment="Left">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding Category}" Value="Services">
                                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Category}" Value="Equipment">
                                                                            <Setter Property="Background" Value="#E3F2FD"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Category}" Value="Software Equipment">
                                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Category}" Value="Hardware Equipment">
                                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Category}" Value="Spare Parts">
                                                                            <Setter Property="Background" Value="#F0F4FF"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>

                                                            <StackPanel Orientation="Horizontal">
                                                                <materialDesign:PackIcon Width="12" Height="12" VerticalAlignment="Center" Margin="0,0,4,0">
                                                                    <materialDesign:PackIcon.Style>
                                                                        <Style TargetType="materialDesign:PackIcon">
                                                                            <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Services">
                                                                                    <Setter Property="Kind" Value="Cog"/>
                                                                                    <Setter Property="Foreground" Value="#4CAF50"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Equipment">
                                                                                    <Setter Property="Kind" Value="CheckboxMarkedCircle"/>
                                                                                    <Setter Property="Foreground" Value="#2196F3"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Software Equipment">
                                                                                    <Setter Property="Kind" Value="CodeBraces"/>
                                                                                    <Setter Property="Foreground" Value="#9C27B0"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Hardware Equipment">
                                                                                    <Setter Property="Kind" Value="Memory"/>
                                                                                    <Setter Property="Foreground" Value="#FF9800"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Spare Parts">
                                                                                    <Setter Property="Kind" Value="Wrench"/>
                                                                                    <Setter Property="Foreground" Value="#6366F1"/>
                                                                                </DataTrigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </materialDesign:PackIcon.Style>
                                                                </materialDesign:PackIcon>

                                                                <TextBlock Text="{Binding Category}" FontSize="11" FontWeight="Medium" VerticalAlignment="Center">
                                                                    <TextBlock.Style>
                                                                        <Style TargetType="TextBlock">
                                                                            <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Services">
                                                                                    <Setter Property="Foreground" Value="#2E7D32"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Equipment">
                                                                                    <Setter Property="Foreground" Value="#1565C0"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Software Equipment">
                                                                                    <Setter Property="Foreground" Value="#7B1FA2"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Hardware Equipment">
                                                                                    <Setter Property="Foreground" Value="#E65100"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Category}" Value="Spare Parts">
                                                                                    <Setter Property="Foreground" Value="#4F46E5"/>
                                                                                </DataTrigger>
                                                                            </Style.Triggers>
                                                                        </Style>
                                                                    </TextBlock.Style>
                                                                </TextBlock>
                                                            </StackPanel>
                                                        </Border>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>

                                        <!-- Description - 35% -->
                                        <GridViewColumn Header="Description" Width="350">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Margin="8,6">
                                                        <TextBlock Text="{Binding Description}" FontSize="13" FontWeight="Medium"
                                                                 TextWrapping="Wrap" MaxWidth="330" VerticalAlignment="Center"
                                                                 LineHeight="18"/>
                                                        <TextBlock Text="{Binding TriggerCondition}" FontSize="11"
                                                                 Foreground="#666" FontStyle="Italic" TextWrapping="Wrap"
                                                                 MaxWidth="330" Margin="0,4,0,0" LineHeight="16">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding TriggerCondition}" Value="">
                                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding TriggerCondition}" Value="{x:Null}">
                                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>

                                        <!-- Percentage - 15% -->
                                        <GridViewColumn Header="Percentage" Width="150">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="8,4">
                                                        <Border Background="#E8F5E8" CornerRadius="12" Padding="8,4">
                                                            <StackPanel Orientation="Horizontal">
                                                                <materialDesign:PackIcon Kind="Percent" Width="12" Height="12"
                                                                                       Foreground="#2E7D32" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                                <TextBlock Text="{Binding Percentage}" FontWeight="Bold"
                                                                         Foreground="#2E7D32" VerticalAlignment="Center" FontSize="12"/>
                                                            </StackPanel>
                                                        </Border>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>

                                        <!-- Amount - 15% -->
                                        <GridViewColumn Header="Amount" Width="150">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="8,4">
                                                        <Border Background="#FFF3E0" CornerRadius="12" Padding="8,4">
                                                            <StackPanel Orientation="Horizontal">
                                                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="12" Height="12"
                                                                                       Foreground="#FF8F00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                                <TextBlock Text="{Binding CalculatedAmount, StringFormat='{}{0:C0}'}" FontWeight="Bold"
                                                                         Foreground="#FF8F00" VerticalAlignment="Center" FontSize="12"/>
                                                            </StackPanel>
                                                        </Border>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>

                                        <!-- Actions - 15% -->
                                        <GridViewColumn Header="Actions" Width="150">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel HorizontalAlignment="Center" Margin="8,4">
                                                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                                                Background="#F44336" Foreground="White"
                                                                Padding="12,6" FontSize="10" Height="32" MinWidth="80"
                                                                Click="RemovePaymentTermButton_Click">
                                                            <StackPanel Orientation="Horizontal">
                                                                <materialDesign:PackIcon Kind="Delete" Width="12" Height="12"
                                                                                       VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                                <TextBlock Text="Remove" VerticalAlignment="Center"/>
                                                            </StackPanel>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </Border>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step5CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 5 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step5CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons - SAP Style -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="24,16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,12,0" Padding="24,12" FontSize="14" Height="44"
                        Click="CancelButton_Click"/>
                <Button Content="Save Project" Style="{StaticResource MaterialDesignRaisedButton}"
                        Padding="24,12" FontSize="14" Height="44" Background="#4A5568"
                        Click="SaveButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
