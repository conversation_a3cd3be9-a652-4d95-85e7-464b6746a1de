#nullable enable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using FinancialTracker.Models;
using FinancialTracker.Helpers;
using FinancialTracker.Views;
using FinancialTracker.Services;
using FinancialTracker.Data;
using Microsoft.Win32;

namespace FinancialTracker
{

    public partial class ProjectDetailsWindow : Window
    {
        private Project? _project;
        private int _projectId;

        private bool _suppressFilterEvents = false;

        public ProjectDetailsWindow(int projectId)
        {
            // Avoid firing filters until data is ready even during InitializeComponent
            _suppressFilterEvents = true;
            _projectId = projectId;
            InitializeComponent();
            LoadProjectData();
        }

        private async void LoadProjectData()
        {
            try
            {
                StatusText.Text = "Loading project data...";

                _project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (_project == null)
                {
                    MessageBox.Show("Project not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // Disable filter events during initial data load
                _suppressFilterEvents = true;

                // Update header and project info
                ProjectNameText.Text = $"Project: {_project.Name}";
                Title = $"Project Details - {_project.Name}";

                ProjectNameDetail.Text = _project.Name;
                ProjectDescriptionDetail.Text = string.IsNullOrEmpty(_project.Description) ? "No description" : _project.Description;
                ProjectPODateDetail.Text = _project.PODate?.ToString("yyyy-MM-dd") ?? "Not specified";
                ProjectStatusDetail.Text = _project.Status;

                // Financial Summary will be updated later in the method

                // Tasks breakdown removed - now shown in sites progress



                // Show PO file button if file exists
                if (!string.IsNullOrEmpty(_project.POFileName))
                {
                    ViewPOButton.Visibility = Visibility.Visible;
                }
                else
                {
                    ViewPOButton.Visibility = Visibility.Collapsed;
                }

                // Load invoices and commitments
                await LoadInvoicesData();
                await LoadCommitmentsData();
                UpdateStatistics();

                StatusText.Text = "Ready";
                _suppressFilterEvents = false; // re-enable after load
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading project data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "Error loading data";
            }
        }

        // Cache for project invoices to avoid repeated database calls
        private List<Invoice>? _cachedProjectInvoices = null;

        // Timer for search debouncing
        private System.Windows.Threading.DispatcherTimer? _searchTimer;

        // Data loading functions
        private async System.Threading.Tasks.Task LoadInvoicesData()
        {
            try
            {
                // Use project-specific query instead of loading all invoices
                _cachedProjectInvoices = await App.DataService.GetInvoicesByProjectAsync(_projectId);

                // Populate filters
                await PopulateInvoiceFilters(_cachedProjectInvoices);

                InvoicesDataGrid.ItemsSource = _cachedProjectInvoices;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadCommitmentsData()
        {
            try
            {
                // Use the method that includes invoices
                var projectCommitments = await App.DataService.GetCommitmentsByProjectAsync(_projectId);
                CommitmentsDataGrid.ItemsSource = projectCommitments;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitments: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Navigation between tabs
        private void SummaryTabBtn_Click(object sender, RoutedEventArgs e)
        {
            ShowFinancialSummary();
        }

        private void InvoicesTabBtn_Click(object sender, RoutedEventArgs e)
        {
            ShowInvoices();
        }

        private void CommitmentsTabBtn_Click(object sender, RoutedEventArgs e)
        {
            ShowCommitments();
        }

        private void ShowFinancialSummary()
        {
            FinancialSummaryContent.Visibility = Visibility.Visible;
            InvoicesContent.Visibility = Visibility.Collapsed;
            CommitmentsContent.Visibility = Visibility.Collapsed;

            // Update button styles
            UpdateTabButtonStyles("Summary");
        }

        private void ShowInvoices()
        {
            FinancialSummaryContent.Visibility = Visibility.Collapsed;
            InvoicesContent.Visibility = Visibility.Visible;
            CommitmentsContent.Visibility = Visibility.Collapsed;

            // Update button styles
            UpdateTabButtonStyles("Invoices");
        }

        private void ShowCommitments()
        {
            FinancialSummaryContent.Visibility = Visibility.Collapsed;
            InvoicesContent.Visibility = Visibility.Collapsed;
            CommitmentsContent.Visibility = Visibility.Visible;

            // Update button styles
            UpdateTabButtonStyles("Commitments");
        }

        private void UpdateTabButtonStyles(string activeTab)
        {
            // Reset all buttons to outlined style
            SummaryTabBtn.Style = (Style)FindResource("MaterialDesignOutlinedButton");
            InvoicesTabBtn.Style = (Style)FindResource("MaterialDesignOutlinedButton");
            CommitmentsTabBtn.Style = (Style)FindResource("MaterialDesignOutlinedButton");

            SummaryTabBtn.Background = Brushes.Transparent;
            InvoicesTabBtn.Background = Brushes.Transparent;
            CommitmentsTabBtn.Background = Brushes.Transparent;

            SummaryTabBtn.Foreground = Brushes.White;
            InvoicesTabBtn.Foreground = Brushes.White;
            CommitmentsTabBtn.Foreground = Brushes.White;

            // Set active button to raised style
            switch (activeTab)
            {
                case "Summary":
                    SummaryTabBtn.Style = (Style)FindResource("MaterialDesignRaisedButton");
                    SummaryTabBtn.Background = Brushes.White;
                    SummaryTabBtn.Foreground = new SolidColorBrush(Color.FromRgb(0x19, 0x76, 0xD2));
                    break;
                case "Invoices":
                    InvoicesTabBtn.Style = (Style)FindResource("MaterialDesignRaisedButton");
                    InvoicesTabBtn.Background = Brushes.White;
                    InvoicesTabBtn.Foreground = new SolidColorBrush(Color.FromRgb(0x19, 0x76, 0xD2));
                    break;
                case "Commitments":
                    CommitmentsTabBtn.Style = (Style)FindResource("MaterialDesignRaisedButton");
                    CommitmentsTabBtn.Background = Brushes.White;
                    CommitmentsTabBtn.Foreground = new SolidColorBrush(Color.FromRgb(0x19, 0x76, 0xD2));
                    break;
            }
        }



        private async void UpdateStatistics()
        {
            try
            {
                // Reload project data to get latest values
                _project = await App.DataService.GetProjectByIdAsync(_projectId);
                if (_project == null) return;

                // Load all data
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _projectId).ToList();

                var projectCommitments = await App.DataService.GetCommitmentsByProjectAsync(_projectId);

                // Statistics cards removed

                // Calculate category amounts - use manual values from project
                // IMPORTANT: Use BaseTasksAmount (excludes spare parts) for equipment calculations
                decimal tasksAmount = 0;
                decimal hardwareAmount = 0;
                decimal softwareAmount = 0;
                decimal servicesAmount = 0;

                if (_project.SplitTasksIntoHardwareAndSoftware)
                {
                    // Use split amounts (these are already base amounts, no spare parts)
                    hardwareAmount = _project.ManualHardwareTasksAmount;
                    softwareAmount = _project.ManualSoftwareTasksAmount;
                    tasksAmount = hardwareAmount + softwareAmount;
                }
                else
                {
                    // Use combined amount (base amount only, no spare parts)
                    tasksAmount = _project.ManualTasksAmount;
                }

                servicesAmount = _project.ManualServicesAmount;

                // Spare parts amount (separate from equipment)
                decimal sparePartsAmount = _project.ManualSparePartsAmount;

                // Down Payment is handled by another department - don't include in our calculations
                decimal downPaymentAmount = _project.HasDownPayment ? _project.DownPaymentAmount : 0;

                // Calculate spent amounts by category - use invoice type and description for better classification
                var equipmentInvoices = projectInvoices.Where(i =>
                {
                    var invoiceType = i.Type ?? "";
                    return invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) ||
                           invoiceType.ToLower().Contains("equipment") ||
                           invoiceType.ToLower().Contains("task") ||
                           invoiceType.ToLower().Contains("software") ||
                           invoiceType.ToLower().Contains("hardware");
                }).ToList();

                var hardwareInvoices = projectInvoices.Where(i =>
                {
                    var invoiceType = i.Type ?? "";
                    var description = i.Description ?? "";

                    // Check if it's specifically a hardware invoice type
                    if (invoiceType.ToLower().Contains("dell") || invoiceType.ToLower().Contains("vmware") ||
                        invoiceType.ToLower().Contains("hw sizing") || invoiceType.ToLower().Contains("hardware"))
                    {
                        return true;
                    }

                    // Check if it's a combined type with hardware description
                    if (invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase))
                    {
                        return description.ToLower().Contains("hardware") || description.ToLower().Contains("hw");
                    }

                    return false;
                }).ToList();

                var softwareInvoices = projectInvoices.Where(i =>
                {
                    var invoiceType = i.Type ?? "";
                    var description = i.Description ?? "";

                    // Check if it's specifically a software invoice type
                    if (invoiceType.ToLower().Contains("license") || invoiceType.ToLower().Contains("operating") ||
                        invoiceType.ToLower().Contains("sw license") || invoiceType.ToLower().Contains("software"))
                    {
                        return true;
                    }

                    // Check if it's a combined type with software description
                    if (invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase))
                    {
                        return description.ToLower().Contains("software") || description.ToLower().Contains("sw");
                    }

                    return false;
                }).ToList();

                var serviceInvoices = projectInvoices.Where(i =>
                {
                    var invoiceType = i.Type ?? "";
                    return invoiceType.Equals("SERVICES", StringComparison.OrdinalIgnoreCase) ||
                           invoiceType.ToLower().Contains("service") ||
                           invoiceType.ToLower().Contains("professional") ||
                           invoiceType.ToLower().Contains("maintenance") ||
                           invoiceType.ToLower().Contains("support");
                }).ToList();

                var sparePartsInvoices = projectInvoices.Where(i =>
                {
                    var invoiceType = i.Type ?? "";
                    var description = i.Description ?? "";
                    return invoiceType.ToLower().Contains("spare") ||
                           invoiceType.ToLower().Contains("part") ||
                           description.ToLower().Contains("spare") ||
                           description.ToLower().Contains("part") ||
                           description.ToLower().Contains("replacement");
                }).ToList();

                // Handle invoices that don't fall into specific categories
                // DO NOT add uncategorized invoices to equipment to match Dashboard calculation
                var uncategorizedInvoices = projectInvoices.Where(i =>
                    !equipmentInvoices.Contains(i) &&
                    !hardwareInvoices.Contains(i) &&
                    !softwareInvoices.Contains(i) &&
                    !serviceInvoices.Contains(i) &&
                    !sparePartsInvoices.Contains(i)).ToList();

                // Calculate invoiced amounts (total invoice amounts)
                var equipmentInvoiced = equipmentInvoices.Sum(i => i.AmountUSD);
                var hardwareInvoiced = hardwareInvoices.Sum(i => i.AmountUSD);
                var softwareInvoiced = softwareInvoices.Sum(i => i.AmountUSD);
                var servicesInvoiced = serviceInvoices.Sum(i => i.AmountUSD);
                var sparePartsInvoiced = sparePartsInvoices.Sum(i => i.AmountUSD);
                var totalInvoiced = projectInvoices.Sum(i => i.AmountUSD);

                // Calculate paid amounts (what's actually been paid)
                var equipmentSpent = equipmentInvoices.Sum(i => i.PaidAmount);
                var hardwareSpent = hardwareInvoices.Sum(i => i.PaidAmount);
                var softwareSpent = softwareInvoices.Sum(i => i.PaidAmount);
                var servicesSpent = serviceInvoices.Sum(i => i.PaidAmount);
                var sparePartsSpent = sparePartsInvoices.Sum(i => i.PaidAmount);
                var totalSpent = projectInvoices.Sum(i => i.PaidAmount);



                // Calculate remaining amounts (budget - spent amounts)
                var equipmentRemaining = tasksAmount - equipmentSpent;
                var hardwareRemaining = hardwareAmount - hardwareSpent;
                var softwareRemaining = softwareAmount - softwareSpent;
                var servicesRemaining = servicesAmount - servicesSpent;
                var sparePartsRemaining = sparePartsAmount - sparePartsSpent;
                var poRemaining = (_project?.POAmount ?? 0) - totalSpent;

                // Update Financial Summary in Project Overview
                POAmountDetail.Text = $"${_project?.POAmount ?? 0:N2}";
                SpentFromPODetail.Text = $"${totalSpent:N2}";
                RemainingFromPODetail.Text = $"${poRemaining:N2}";

                // Update display based on project settings
                if (_project?.SplitTasksIntoHardwareAndSoftware == true)
                {
                    // Show split view
                    CombinedTasksCard.Visibility = Visibility.Collapsed;
                    HardwareTasksCard.Visibility = Visibility.Visible;
                    SoftwareTasksCard.Visibility = Visibility.Visible;

                    // Update Hardware Summary
                    HardwareTotalValueText.Text = $"${hardwareAmount:N0}";
                    HardwareInvoicedText.Text = $"${hardwareSpent:N0}";
                    HardwareRemainingText.Text = $"${hardwareRemaining:N0}";

                    // Update Software Summary
                    SoftwareTotalValueText.Text = $"${softwareAmount:N0}";
                    SoftwareInvoicedText.Text = $"${softwareSpent:N0}";
                    SoftwareRemainingText.Text = $"${softwareRemaining:N0}";
                }
                else
                {
                    // Show combined view
                    CombinedTasksCard.Visibility = Visibility.Visible;
                    HardwareTasksCard.Visibility = Visibility.Collapsed;
                    SoftwareTasksCard.Visibility = Visibility.Collapsed;

                    // Update Combined Equipment Summary
                    TasksTotalValueText.Text = $"${tasksAmount:N0}";
                    TasksInvoicedText.Text = $"${equipmentSpent:N0}";
                    TasksRemainingText.Text = $"${equipmentRemaining:N0}";
                }

                // Update Services Summary (always shown)
                ServicesTotalValueText.Text = $"${servicesAmount:N0}";
                ServicesInvoicedText.Text = $"${servicesSpent:N0}";
                ServicesRemainingText.Text = $"${servicesRemaining:N0}";

                // Update Spare Parts (show only if project has spare parts amount)
                if (_project?.ManualSparePartsAmount > 0)
                {
                    SparePartsCard.Visibility = Visibility.Visible;

                    // Update spare parts values
                    SparePartsTotalText.Text = $"${sparePartsAmount:N0}";
                    SparePartsInvoicedText.Text = $"${sparePartsSpent:N0}";
                    SparePartsRemainingText.Text = $"${sparePartsRemaining:N0}";
                }
                else
                {
                    // Hide spare parts card if no spare parts amount
                    SparePartsCard.Visibility = Visibility.Collapsed;
                }

                // Update Extra (show only if project has extra amount)
                if (_project?.ManualExtraAmount > 0)
                {
                    ExtraCard.Visibility = Visibility.Visible;

                    var extraAmount = _project.ManualExtraAmount;
                    string extraName = !string.IsNullOrWhiteSpace(_project.ExtraCategoryName)
                        ? _project.ExtraCategoryName
                        : "Extra";

                    var extraInvoices = projectInvoices.Where(i =>
                        i.Type != null && (i.Type.Equals("Extra", StringComparison.OrdinalIgnoreCase) ||
                                          i.Type.Equals(extraName, StringComparison.OrdinalIgnoreCase))).ToList();
                    var extraSpent = extraInvoices.Sum(i => i.PaidAmount);
                    var extraRemaining = extraAmount - extraSpent;

                    // Update extra labels with custom name
                    ExtraTotalLabel.Text = $"Total {extraName}:";
                    ExtraInvoicedLabel.Text = $"{extraName} Invoiced:";
                    ExtraRemainingLabel.Text = $"{extraName} Left:";

                    // Update extra values
                    ExtraTotalText.Text = $"${extraAmount:N0}";
                    ExtraInvoicedText.Text = $"${extraSpent:N0}";
                    ExtraRemainingText.Text = $"${extraRemaining:N0}";
                }
                else
                {
                    // Hide extra card if no extra amount
                    ExtraCard.Visibility = Visibility.Collapsed;
                }

                // Update Down Payment information
                if (_project?.HasDownPayment == true && _project.DownPaymentPercentage > 0)
                {
                    var downPaymentValue = _project.DownPaymentAmount;
                    DownPaymentAmountText.Text = $"${downPaymentValue:N0}";
                    DownPaymentPercentageText.Text = $"{_project.DownPaymentPercentage:F1}%";
                }
                else
                {
                    DownPaymentAmountText.Text = "$0";
                    DownPaymentPercentageText.Text = "0%";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating statistics: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task<bool> IsPaymentTermFullyPaid(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_project == null) return false;

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _project.Id).ToList();

                decimal totalUsedAmount = 0;
                decimal totalPaidAmount = 0;

                foreach (var invoice in projectInvoices)
                {
                    var matchingPaymentTerms = invoice.InvoicePaymentTerms
                        .Where(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    foreach (var ipt in matchingPaymentTerms)
                    {
                        totalUsedAmount += ipt.CalculatedAmount;
                        // Calculate paid amount proportionally
                        var invoicePaidRatio = invoice.AmountUSD > 0 ? invoice.PaidAmount / invoice.AmountUSD : 0;
                        totalPaidAmount += ipt.CalculatedAmount * invoicePaidRatio;
                    }
                }

                // Payment term is fully paid if paid amount equals or exceeds used amount
                var unpaidAmount = totalUsedAmount - totalPaidAmount;
                return unpaidAmount <= 0.01m; // Small tolerance for rounding
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking if payment term is fully paid: {ex.Message}");
                return false;
            }
        }

        // Project management
        private void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog(_project);
            if (dialog.ShowDialog() == true)
            {
                LoadProjectData(); // Refresh data
            }
        }

        // Invoice management
        private async void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new InvoiceDialog(null, _projectId, null);
            if (dialog.ShowDialog() == true)
            {
                // Clear cache to force refresh
                _cachedProjectInvoices = null;
                await LoadInvoicesData();
                UpdateStatistics();
            }
        }

        private async void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var dialog = new InvoiceDialog(invoice);
                if (dialog.ShowDialog() == true)
                {
                    // Clear cache to force refresh
                    _cachedProjectInvoices = null;
                    await LoadInvoicesData();
                    UpdateStatistics();
                }
            }
        }

        private async void CopyInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice originalInvoice)
            {
                var newInvoice = new Invoice
                {
                    InvoiceNumber = $"{originalInvoice.InvoiceNumber}_Copy_{DateTime.Now:yyyyMMddHHmmss}",
                    ProjectId = originalInvoice.ProjectId,
                    CommitmentId = originalInvoice.CommitmentId,
                    AmountUSD = originalInvoice.AmountUSD,
                    PaidAmount = 0,
                    Description = originalInvoice.Description,
                    Type = originalInvoice.Type,
                    InvoiceDate = DateTime.Now,
                    CreatedDate = DateTime.Now
                };

                var dialog = new InvoiceDialog(newInvoice);
                if (dialog.ShowDialog() == true)
                {
                    await LoadInvoicesData();
                    UpdateStatistics();
                }
            }
        }

        private async void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var result = MessageBox.Show($"Are you sure you want to delete invoice '{invoice.InvoiceNumber}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await App.DataService.DeleteInvoiceAsync(invoice.Id);
                        // Clear cache to force refresh
                        _cachedProjectInvoices = null;
                        await LoadInvoicesData();
                        UpdateStatistics();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting invoice: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void ViewCommitmentDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int commitmentId && _project != null)
                {
                    var commitments = await App.DataService.GetCommitmentsByProjectAsync(_project.Id);
                    var commitment = commitments.FirstOrDefault(c => c.Id == commitmentId);

                    if (commitment != null)
                    {
                        var detailsDialog = new CommitmentDetailsDialog(commitment);
                        detailsDialog.ShowDialog();
                    }
                    else
                    {
                        MessageBox.Show("Commitment not found.", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening commitment details: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // ===== دوال الملفات الجديدة =====

        private void InvoiceLetterButton_Click(object sender, RoutedEventArgs e)
        {
            // Get the invoice from the button's DataContext
            if (sender is Button button && button.DataContext is Invoice invoice)
            {
                try
                {
                    if (!string.IsNullOrEmpty(invoice.LetterFilePath))
                    {
                        // Try to get the full path using FileService
                        string fullPath = App.FileService.GetFullPath(invoice.LetterFilePath);

                        if (File.Exists(fullPath))
                        {
                            // Open letter file
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = fullPath,
                                UseShellExecute = true
                            });
                        }
                        else
                        {
                            MessageBox.Show($"Cannot find the letter file.\nPath: {fullPath}", "File Not Found",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("No letter file attached to this invoice.", "File Not Found",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening letter file: {ex.Message}", "Error",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void InvoiceFilesButton_Click(object sender, RoutedEventArgs e)
        {
            // Get the invoice from the button's DataContext
            if (sender is Button button && button.DataContext is Invoice invoice)
            {
                try
                {
                    if (!string.IsNullOrEmpty(invoice.AttachedFilePath))
                    {
                        // Try to get the full path using FileService
                        string fullPath = App.FileService.GetFullPath(invoice.AttachedFilePath);

                        if (File.Exists(fullPath))
                        {
                            // Open invoice file
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = fullPath,
                                UseShellExecute = true
                            });
                        }
                        else
                        {
                            MessageBox.Show($"Cannot find the invoice file.\nPath: {fullPath}", "File Not Found",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("No invoice file attached to this invoice.", "File Not Found",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening invoice file: {ex.Message}", "Error",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Commitment management
        private async void AddCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CommitmentDialog(null, _projectId);
            if (dialog.ShowDialog() == true)
            {
                await LoadCommitmentsData();
                UpdateStatistics();
            }
        }

        private async void EditCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var dialog = new CommitmentDialog(commitment);
                if (dialog.ShowDialog() == true)
                {
                    await LoadCommitmentsData();
                    UpdateStatistics();
                }
            }
        }

        private async void OpenCommitmentFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
                {
                    if (!string.IsNullOrEmpty(commitment.AttachedFilePath))
                    {
                        await App.FileService.OpenFileAsync(commitment.AttachedFilePath);
                    }
                    else
                    {
                        MessageBox.Show("No file attached to this commitment.", "File Not Found",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening commitment file: {ex.Message}", "Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CopyCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment originalCommitment)
            {
                var newCommitment = new Commitment
                {
                    Title = $"{originalCommitment.Title}_Copy_{DateTime.Now:yyyyMMddHHmmss}",
                    Type = originalCommitment.Type,
                    ProjectId = originalCommitment.ProjectId,
                    AmountUSD = originalCommitment.AmountUSD,
                    ExchangeRate = originalCommitment.ExchangeRate,
                    Description = originalCommitment.Description,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var dialog = new CommitmentDialog(newCommitment, _projectId);
                if (dialog.ShowDialog() == true)
                {
                    await LoadCommitmentsData();
                    UpdateStatistics();
                }
            }
        }

        private void ViewCommitmentInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var invoicesWindow = new CommitmentInvoicesWindow(commitment.Id);
                invoicesWindow.Show();
            }
        }



        private Project? ShowProjectSelectionDialog(List<string> projectNames, List<Project> projects)
        {
            // Create a simple selection window
            var selectionWindow = new Window
            {
                Title = "Select Project to Copy Payment Terms From",
                Width = 400,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var listBox = new ListBox
            {
                ItemsSource = projectNames,
                Margin = new Thickness(10)
            };

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(10)
            };

            var okButton = new Button { Content = "OK", Width = 75, Margin = new Thickness(5) };
            var cancelButton = new Button { Content = "Cancel", Width = 75, Margin = new Thickness(5) };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);

            var mainPanel = new DockPanel();
            DockPanel.SetDock(buttonPanel, Dock.Bottom);
            mainPanel.Children.Add(buttonPanel);
            mainPanel.Children.Add(listBox);

            selectionWindow.Content = mainPanel;

            Project? selectedProject = null;

            okButton.Click += (s, e) =>
            {
                if (listBox.SelectedIndex >= 0)
                {
                    selectedProject = projects[listBox.SelectedIndex];
                    selectionWindow.DialogResult = true;
                }
            };

            cancelButton.Click += (s, e) => selectionWindow.DialogResult = false;

            if (selectionWindow.ShowDialog() == true)
                return selectedProject;

            return null;
        }

        private async void DeleteCommitmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (CommitmentsDataGrid.SelectedItem is Commitment commitment)
            {
                var result = MessageBox.Show($"Are you sure you want to delete commitment '{commitment.Title}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await App.DataService.DeleteCommitmentAsync(commitment.Id);
                        await LoadCommitmentsData();
                        UpdateStatistics();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting commitment: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        // Filter functionality with debouncing
        private void InvoiceSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_suppressFilterEvents) return;

            // Stop existing timer
            _searchTimer?.Stop();

            // Create new timer for debouncing (300ms delay)
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300)
            };

            _searchTimer.Tick += async (s, args) =>
            {
                _searchTimer.Stop();
                await FilterInvoices();
            };

            _searchTimer.Start();
        }

        private async void InvoiceFilters_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_suppressFilterEvents) return;
            await FilterInvoices();
        }

        private async void ClearInvoiceFilterButton_Click(object sender, RoutedEventArgs e)
        {
            InvoiceSearchTextBox.Text = string.Empty;
            TypeFilterCombo.SelectedIndex = 0;
            SiteFilterCombo.SelectedIndex = 0;
            await FilterInvoices();
        }

        private async void CommitmentSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            await FilterCommitments();
        }

        private async void ClearCommitmentFilterButton_Click(object sender, RoutedEventArgs e)
        {
            CommitmentSearchTextBox.Text = "";
            await FilterCommitments();
        }

        private async System.Threading.Tasks.Task FilterInvoices()
        {
            try
            {
                // Use cached data instead of querying database every time
                if (_cachedProjectInvoices == null)
                {
                    _cachedProjectInvoices = await App.DataService.GetInvoicesByProjectAsync(_projectId);
                }

                if (_cachedProjectInvoices == null)
                {
                    InvoicesDataGrid.ItemsSource = Array.Empty<Invoice>();
                    return;
                }

                // Work with cached data - no more async needed
                var projectInvoices = _cachedProjectInvoices.AsEnumerable();

                // Text search
                var searchText = (InvoiceSearchTextBox?.Text ?? string.Empty).Trim().ToLower();
                if (!string.IsNullOrEmpty(searchText))
                {
                    projectInvoices = projectInvoices.Where(i =>
                        ((i.InvoiceNumber ?? string.Empty).ToLower().Contains(searchText)));
                }

                // Type filter
                if (TypeFilterCombo?.SelectedItem is ComboBoxItem typeItem && typeItem.Content != null)
                {
                    var typeVal = typeItem.Content.ToString()!;



                    if (typeVal != "All Types")
                    {
                        if (typeVal == "Services")
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                return t.Equals("Services", StringComparison.OrdinalIgnoreCase) ||
                                       t.Equals("SERVICES", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Service", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Professional Services", StringComparison.OrdinalIgnoreCase);
                            });
                        }
                        else if (typeVal == "Hardware")
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                return t.Contains("Hardware", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("HW", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Dell", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("VMware", StringComparison.OrdinalIgnoreCase);
                            });
                        }
                        else if (typeVal == "Software")
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                return t.Contains("Software", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("SW", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("License", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Operating", StringComparison.OrdinalIgnoreCase);
                            });
                        }
                        else if (typeVal == "Equipment" || typeVal == "Tasks")
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                // Match all equipment-related types (including old task types for compatibility)
                                // Exclude Spare Parts from Equipment filter
                                return (t.Contains("Equipment", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Task", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Software", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Hardware", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("SW & HW", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("Dell HW", StringComparison.OrdinalIgnoreCase) ||
                                       t.Contains("SW License", StringComparison.OrdinalIgnoreCase)) &&
                                       !t.Contains("Spare", StringComparison.OrdinalIgnoreCase);
                            });
                        }
                        else if (typeVal == "Spare Parts")
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                return t.Contains("Spare", StringComparison.OrdinalIgnoreCase) ||
                                       t.Equals("Spare Parts", StringComparison.OrdinalIgnoreCase);
                            });
                        }
                        else if (typeVal == "Extra" ||
                                (!string.IsNullOrWhiteSpace(_project?.ExtraCategoryName) &&
                                 typeVal.Equals(_project.ExtraCategoryName, StringComparison.OrdinalIgnoreCase)))
                        {
                            projectInvoices = projectInvoices.Where(i => {
                                var t = i.Type;
                                if (string.IsNullOrWhiteSpace(t)) return false;
                                // Match both "Extra" and custom name
                                return t.Equals("Extra", StringComparison.OrdinalIgnoreCase) ||
                                       (!string.IsNullOrWhiteSpace(_project?.ExtraCategoryName) &&
                                        t.Equals(_project.ExtraCategoryName, StringComparison.OrdinalIgnoreCase));
                            });
                        }
                    }


                }

                // Site filter
                if (SiteFilterCombo?.SelectedItem is ComboBoxItem siteItem)
                {
                    if (siteItem.Tag is int siteId)
                    {
                        projectInvoices = projectInvoices.Where(i => i.ProjectSiteId == siteId);
                    }
                    else
                    {
                        var siteName = siteItem.Content?.ToString();
                        if (!string.IsNullOrWhiteSpace(siteName) && siteName != "All Sites")
                        {
                            projectInvoices = projectInvoices.Where(i => (i.SiteName ?? string.Empty) == siteName);
                        }
                    }
                }

                if (InvoicesDataGrid != null)
                    InvoicesDataGrid.ItemsSource = projectInvoices.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task FilterCommitments()
        {
            try
            {
                var allCommitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = allCommitments.Where(c => c.ProjectId == _projectId);

                var searchText = CommitmentSearchTextBox.Text?.Trim().ToLower();
                if (!string.IsNullOrEmpty(searchText))
                {
                    projectCommitments = projectCommitments.Where(c =>
                        c.Title.ToLower().Contains(searchText) ||
                        c.Description.ToLower().Contains(searchText));
                }

                CommitmentsDataGrid.ItemsSource = projectCommitments.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error filtering commitments: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task PopulateInvoiceFilters(List<Invoice> projectInvoices)
        {
            try
            {
                // Site filter
                var sites = await App.DataService.GetProjectSitesAsync(_projectId);
                SiteFilterCombo.Items.Clear();
                SiteFilterCombo.Items.Add(new ComboBoxItem { Content = "All Sites", IsSelected = true });
                foreach (var s in sites)
                    SiteFilterCombo.Items.Add(new ComboBoxItem { Content = s.SiteName, Tag = s.Id });

                // Type filter - rebuild based on project configuration
                var currentSelection = TypeFilterCombo.SelectedItem as ComboBoxItem;
                var currentSelectionContent = currentSelection?.Content?.ToString();

                TypeFilterCombo.Items.Clear();
                TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "All Types", IsSelected = true });
                TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "Services" });

                // If project splits hardware and software, add separate filters
                if (_project?.SplitTasksIntoHardwareAndSoftware == true)
                {
                    TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "Hardware" });
                    TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "Software" });
                }
                else
                {
                    // Otherwise, add generic Equipment filter
                    TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "Equipment" });
                }

                // Add Spare Parts filter only if the project has spare parts amount
                if (_project?.ManualSparePartsAmount > 0)
                {
                    TypeFilterCombo.Items.Add(new ComboBoxItem { Content = "Spare Parts" });
                }

                // Add Extra filter only if the project has extra amount
                if (_project?.ManualExtraAmount > 0)
                {
                    string extraName = !string.IsNullOrWhiteSpace(_project.ExtraCategoryName)
                        ? _project.ExtraCategoryName
                        : "Extra";
                    TypeFilterCombo.Items.Add(new ComboBoxItem { Content = extraName });
                }

                // Try to restore previous selection
                if (!string.IsNullOrEmpty(currentSelectionContent))
                {
                    foreach (ComboBoxItem item in TypeFilterCombo.Items)
                    {
                        if (item.Content?.ToString() == currentSelectionContent)
                        {
                            TypeFilterCombo.SelectedItem = item;
                            return;
                        }
                    }
                }

                TypeFilterCombo.SelectedIndex = 0;
            }
            catch { }
        }



        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectData(); // Refresh all project data
        }

        private async void ViewPOFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_project != null && !string.IsNullOrEmpty(_project.POFilePath))
                {
                    string fullPath = App.FileService.GetFullPath(_project.POFilePath);
                    if (System.IO.File.Exists(fullPath))
                    {
                        await App.FileService.OpenFileAsync(_project.POFilePath);
                    }
                    else
                    {
                        MessageBox.Show("PO file not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("No PO file available for this project.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening PO file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }




        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ManageSitesButton_Click(object sender, RoutedEventArgs e)
        {
            if (_project == null) return;

            var dialog = new ProjectSitesDialog(_project);
            if (dialog.ShowDialog() == true)
            {
                // Refresh data after site changes
                _ = LoadInvoicesData();
                _ = LoadCommitmentsData();
                UpdateStatistics();
            }
        }

        private void ProjectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Open the project files window
                var filesWindow = new ProjectFilesWindow(_projectId);
                filesWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening project files: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }




        private void ExportToExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show column selection dialog
                var exportDialog = new ExportColumnsDialog();
                exportDialog.Owner = this;

                if (exportDialog.ShowDialog() != true)
                    return;

                // Get selected columns
                var selectedColumns = exportDialog.SelectedColumns;

                // Show save file dialog
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"{_project?.Name ?? "Project"}_Invoices_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() != true)
                    return;

                // Get current invoices (filtered)
                var invoicesToExport = GetFilteredInvoices();

                if (!invoicesToExport.Any())
                {
                    MessageBox.Show("No invoices to export.", "No Data",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // Export to Excel
                var excelService = new ExcelExportService();
                excelService.ExportInvoicesToExcel(invoicesToExport, selectedColumns,
                    _project?.Name ?? "Unknown Project", saveFileDialog.FileName, _project);

                // Show success message
                var result = MessageBox.Show($"Excel file exported successfully!\n\nFile: {saveFileDialog.FileName}\n\nWould you like to open the file?",
                    "Export Successful", MessageBoxButton.YesNo, MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = saveFileDialog.FileName,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting to Excel: {ex.Message}", "Export Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Invoice> GetFilteredInvoices()
        {
            // Get all invoices for the project
            using var context = new FinancialContext();
            var allInvoices = context.Invoices
                .Where(i => i.ProjectId == _project!.Id)
                .ToList();

            // Apply current filters
            var filteredInvoices = allInvoices.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(InvoiceSearchTextBox?.Text))
            {
                var searchTerm = InvoiceSearchTextBox.Text.ToLower();
                filteredInvoices = filteredInvoices.Where(i =>
                    (i.InvoiceNumber?.ToLower().Contains(searchTerm) ?? false) ||
                    (i.Description?.ToLower().Contains(searchTerm) ?? false) ||
                    (i.Type?.ToLower().Contains(searchTerm) ?? false));
            }

            // Apply type filter
            if (TypeFilterCombo?.SelectedItem is ComboBoxItem typeItem &&
                typeItem.Content.ToString() != "All Types")
            {
                var selectedType = typeItem.Content.ToString();
                filteredInvoices = filteredInvoices.Where(i => i.Type == selectedType);
            }

            // Apply site filter
            if (SiteFilterCombo?.SelectedItem is ComboBoxItem siteItem &&
                siteItem.Content.ToString() != "All Sites")
            {
                var selectedSite = siteItem.Content.ToString();
                filteredInvoices = filteredInvoices.Where(i => i.SiteName == selectedSite);
            }

            return filteredInvoices.ToList();
        }

        // Fix for mouse wheel scrolling in DataGrid
        private void DataGrid_PreviewMouseWheel(object sender, System.Windows.Input.MouseWheelEventArgs e)
        {
            if (sender is DataGrid dataGrid)
            {
                // Find the parent ScrollViewer
                var scrollViewer = FindParent<ScrollViewer>(dataGrid);
                if (scrollViewer != null)
                {
                    // Scroll the parent ScrollViewer instead of the DataGrid
                    if (e.Delta > 0)
                    {
                        scrollViewer.LineUp();
                        scrollViewer.LineUp();
                        scrollViewer.LineUp();
                    }
                    else
                    {
                        scrollViewer.LineDown();
                        scrollViewer.LineDown();
                        scrollViewer.LineDown();
                    }

                    // Mark the event as handled
                    e.Handled = true;
                }
            }
        }

        // Helper method to find parent control
        private T? FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            DependencyObject parentObject = VisualTreeHelper.GetParent(child);

            if (parentObject == null)
                return null;

            if (parentObject is T parent)
                return parent;

            return FindParent<T>(parentObject);
        }
    }
}
