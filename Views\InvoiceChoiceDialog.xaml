<Window x:Class="FinancialTracker.Views.InvoiceChoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Add Invoice to Commitment" Height="600" Width="850"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Text="Add Invoice to Commitment" FontSize="18" FontWeight="Bold" Margin="0,0,0,8"/>
            <TextBlock Text="You can choose an existing invoice in the project (unlinked) or create a new invoice"
                       FontSize="12" Opacity="0.7"/>
        </StackPanel>

        <!-- Content -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
            
            <!-- Existing Invoices Tab -->
            <TabItem Header="Available Invoices">
                <Grid Margin="0,16,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Choose one or more existing invoices in the project (not linked to any commitment):"
                               FontWeight="Medium" Margin="0,0,0,8"/>

                    <!-- Selection Info Bar -->
                    <Border Grid.Row="1" Background="#E8F4FD" CornerRadius="4" Padding="12,8" Margin="0,0,0,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="InformationOutline" Width="16" Height="16"
                                                         Foreground="#0070F2" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock x:Name="SelectionInfoText" Text="Select invoices by clicking on the checkboxes"
                                           FontSize="12" Foreground="#0070F2" VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock Grid.Column="1" x:Name="SelectedCountText" Text="0 selected"
                                       FontSize="12" FontWeight="Medium" Foreground="#0070F2" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <DataGrid Grid.Row="2" x:Name="ExistingInvoicesDataGrid"
                              AutoGenerateColumns="False" CanUserAddRows="False"
                              IsReadOnly="False" SelectionMode="Extended"
                              GridLinesVisibility="Horizontal"
                              SelectionChanged="ExistingInvoicesDataGrid_SelectionChanged"
                              CanUserSortColumns="True">
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="☑" Width="50" IsReadOnly="False">
                                <DataGridTemplateColumn.HeaderTemplate>
                                    <DataTemplate>
                                        <CheckBox x:Name="SelectAllCheckBox"
                                                  Checked="SelectAllCheckBox_Checked"
                                                  Unchecked="SelectAllCheckBox_Unchecked"
                                                  ToolTip="Select/Deselect All"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.HeaderTemplate>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Checked="InvoiceCheckBox_Changed"
                                                  Unchecked="InvoiceCheckBox_Changed"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="Invoice Number" Binding="{Binding InvoiceNumber}" Width="*" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat='{}{0:C0}'}" Width="*" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="2*" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:dd/MM/yyyy}'}" Width="*" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- Create New Tab -->
            <TabItem Header="Create New Invoice">
                <Grid Margin="0,16,0,0">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="Plus" Width="64" Height="64" Opacity="0.5" HorizontalAlignment="Center"/>
                        <TextBlock Text="Create New Invoice" FontSize="16" FontWeight="Medium"
                                   HorizontalAlignment="Center" Margin="0,8,0,4"/>
                        <TextBlock Text="A new invoice creation window will open and link it to this commitment" FontSize="12" Opacity="0.7"
                                   HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="SelectButton" Content="Add Selected Invoices"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,8,0" Click="SelectButton_Click" Padding="20,8">
                <Button.ToolTip>
                    <ToolTip Content="Link selected invoices to this commitment"/>
                </Button.ToolTip>
            </Button>
            <Button x:Name="CancelButton" Content="Cancel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click" Padding="20,8"/>
        </StackPanel>
    </Grid>
</Window>
