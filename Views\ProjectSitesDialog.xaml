<Window x:Class="FinancialTracker.ProjectSitesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Manage Project Sites"
        Height="500"
        Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Manage Project Sites" FontSize="20" FontWeight="Bold" Margin="0,0,0,16"/>

        <!-- Sites List -->
        <materialDesign:Card Grid.Row="1" Padding="20">
            <StackPanel>
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="MapMarker" Width="20" Height="20" Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="Project Sites Settings" FontSize="16" FontWeight="Medium" VerticalAlignment="Center"/>
                    </StackPanel>
                    <Button Grid.Column="1" x:Name="AddSiteButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Padding="16,8" Click="AddSiteButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Add Site" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <Border BorderThickness="1" BorderBrush="#E0E0E0" CornerRadius="4">
                    <ListView x:Name="SitesList" Height="280" BorderThickness="0">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Order" Width="80" DisplayMemberBinding="{Binding SiteOrder}"/>
                                <GridViewColumn Header="Site Name" Width="250">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding SiteName, UpdateSourceTrigger=PropertyChanged}"
                                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                     Margin="2" Padding="8,6" FontSize="13"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="Status" Width="100">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox IsChecked="{Binding IsActive}" Content="Active"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="Actions" Width="120">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                                    Padding="8,4" FontSize="11" Click="RemoveSiteButton_Click">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Delete" Width="14" Height="14" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                    <TextBlock Text="Remove" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Button>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Border>

                <materialDesign:Card Background="#F5F5F5" Padding="12" Margin="0,16,0,0">
                    <StackPanel>
                        <TextBlock Text="💡 Tips:" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                        <TextBlock Text="• Edit site names directly in the table above" FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• Uncheck 'Active' to disable a site without removing it" FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• Changes are saved when you click the Save button" FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </materialDesign:Card>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,12,0"
                    Padding="20,10"
                    Click="CancelButton_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,6,0"/>
                    <TextBlock Text="Cancel" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="20,10"
                    Click="SaveButton_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,6,0"/>
                    <TextBlock Text="Save Changes" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
