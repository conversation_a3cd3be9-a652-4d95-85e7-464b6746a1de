# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio cache/options
.vs/
.vscode/

# Build artifacts
*.exe
*.dll
*.pdb
*.cache
publish/

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Database backups (keep only in backups folder)
*.db-shm
*.db-wal

# NuGet Packages
*.nupkg
*.snupkg
**/packages/*
!**/packages/build/

# Test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JetBrains Rider
*.sln.iml
.idea/

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin
$RECYCLE.BIN/

# Mac
.DS_Store

# Keep these
!backups/*.db
!FinancialTracker.db

