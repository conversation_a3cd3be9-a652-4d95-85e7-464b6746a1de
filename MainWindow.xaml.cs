#nullable enable
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Shapes;
using FinancialTracker.Data;
using FinancialTracker.Models;
using FinancialTracker.Views;
using FinancialTracker.Services;
using Microsoft.EntityFrameworkCore;

namespace FinancialTracker
{
    public partial class MainWindow : Window
    {
        private List<ProjectSummary> _allProjects = new List<ProjectSummary>();
        private List<ProjectSummary> _filteredProjects = new List<ProjectSummary>();

        // Navigation stack to track opened windows
        private Stack<Window> _navigationStack = new Stack<Window>();
        private bool _isNavigating = false;

        public MainWindow()
        {
            InitializeComponent();
            LoadDashboardData();

            // Check HPBX data
            CheckHPBXData();
        }

        private async void CheckHPBXData()
        {
            try
            {
                Console.WriteLine("=== Checking HPBX Project Data ===");

                using var context = new FinancialContext();

                // Find HPBX project
                var hpbxProject = await context.Projects
                    .Include(p => p.Invoices)
                    .Include(p => p.Commitments)
                    .FirstOrDefaultAsync(p => p.Name.Contains("HPBX"));

                if (hpbxProject == null)
                {
                    Console.WriteLine("HPBX project not found!");
                    return;
                }

                Console.WriteLine($"Project: {hpbxProject.Name}");
                Console.WriteLine($"PO Amount: ${hpbxProject.POAmount:N2}");

                Console.WriteLine($"\n=== Invoices ({hpbxProject.Invoices.Count}) ===");

                decimal totalPaid = 0;
                decimal equipmentPaid = 0;

                foreach (var invoice in hpbxProject.Invoices.OrderBy(i => i.InvoiceNumber))
                {
                    Console.WriteLine($"Invoice: {invoice.InvoiceNumber} - Type: '{invoice.Type}' - Amount: ${invoice.AmountUSD:N2} - Paid: ${invoice.PaidAmount:N2}");

                    totalPaid += invoice.PaidAmount;

                    // Check if it's equipment type
                    var invoiceType = invoice.Type ?? "";
                    bool isEquipment = invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) ||
                                     invoiceType.ToLower().Contains("equipment") ||
                                     invoiceType.ToLower().Contains("task") ||
                                     invoiceType.ToLower().Contains("software") ||
                                     invoiceType.ToLower().Contains("hardware");

                    if (isEquipment)
                    {
                        equipmentPaid += invoice.PaidAmount;
                        Console.WriteLine($"  -> EQUIPMENT");
                    }
                }

                Console.WriteLine($"\n=== Summary ===");
                Console.WriteLine($"Total Paid: ${totalPaid:N2}");
                Console.WriteLine($"Equipment Paid: ${equipmentPaid:N2}");
                Console.WriteLine($"Expected Equipment Spent: $7,971,277");
                Console.WriteLine($"Difference: ${7971277 - equipmentPaid:N2}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking HPBX data: {ex.Message}");
            }
        }

        #region Sidebar Event Handlers

        // Dashboard Section
        private void SidebarOverview_Click(object sender, RoutedEventArgs e)
        {
            // Show financial overview only
            ShowOverview();
        }

        private void SidebarProjectsTable_Click(object sender, RoutedEventArgs e)
        {
            // Show projects table only
            ShowProjectsTable();
        }

        private void SidebarAnalytics_Click(object sender, RoutedEventArgs e)
        {
            // Show analytics view
            ShowAnalytics();
        }

        // Projects Section
        private void SidebarAddProject_Click(object sender, RoutedEventArgs e)
        {
            // Add new project
            AddProjectButton_Click(sender, e);
        }

        private void SidebarProjectsList_Click(object sender, RoutedEventArgs e)
        {
            // Focus on projects table
            if (ProjectsSummaryDataGrid.Items.Count > 0)
            {
                ProjectsSummaryDataGrid.Focus();
                ProjectsSummaryDataGrid.SelectedIndex = 0;
            }
        }

        private void SidebarProjectFiles_Click(object sender, RoutedEventArgs e)
        {
            // Show project files view
            ShowProjectFiles();
        }

        // Financial Section
        private void SidebarInvoices_Click(object sender, RoutedEventArgs e)
        {
            // Open invoice management for selected project
            if (ProjectsSummaryDataGrid.SelectedItem is ProjectSummary selectedProject)
            {
                var invoicesWindow = new CommitmentInvoicesWindow(selectedProject.Id);
                invoicesWindow.ShowDialog();
                LoadDashboardData(); // Refresh after changes
            }
            else
            {
                MessageBox.Show("Please select a project first to manage its invoices.", "Information",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SidebarCommitments_Click(object sender, RoutedEventArgs e)
        {
            // Open commitment dialog for selected project
            if (ProjectsSummaryDataGrid.SelectedItem is ProjectSummary selectedProject)
            {
                var commitmentDialog = new CommitmentDialog(null, selectedProject.Id);
                if (commitmentDialog.ShowDialog() == true)
                {
                    LoadDashboardData(); // Refresh after changes
                }
            }
            else
            {
                MessageBox.Show("Please select a project first to manage its commitments.", "Information",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SidebarPaymentTerms_Click(object sender, RoutedEventArgs e)
        {
            // Open payment terms for selected project
            if (ProjectsSummaryDataGrid.SelectedItem is ProjectSummary selectedProject)
            {
                var paymentTermDialog = new AddPaymentTermDialog(selectedProject.Id);
                if (paymentTermDialog.ShowDialog() == true)
                {
                    LoadDashboardData(); // Refresh after changes
                }
            }
            else
            {
                MessageBox.Show("Please select a project first to manage its payment terms.", "Information",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // Reports Section
        private void SidebarExport_Click(object sender, RoutedEventArgs e)
        {
            // Open export dialog
            var exportDialog = new ExportColumnsDialog();
            exportDialog.ShowDialog();
        }

        private void SidebarActivities_Click(object sender, RoutedEventArgs e)
        {
            // Show recent activities
            ActivitiesButton_Click(sender, e);
        }

        // Settings Section
        private async void SidebarBackup_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var backupService = new FinancialTracker.Services.BackupService();
                var backupPath = await backupService.CreateBackupAsync();

                MessageBox.Show($"Backup created successfully at:\n{backupPath}", "Backup Complete",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating backup: {ex.Message}", "Backup Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SidebarRefresh_Click(object sender, RoutedEventArgs e)
        {
            // Refresh all data
            RefreshButton_Click(sender, e);
        }

        #endregion

        private async void LoadDashboardData()
        {
            try
            {

                Console.WriteLine("=== Loading Dashboard Data ===");
                var dashboardData = await App.DataService.GetDashboardDataAsync();
                var projectsSummary = await App.DataService.GetProjectsSummaryAsync();
                Console.WriteLine($"Loaded {projectsSummary?.Count ?? 0} projects");

                // Check if data exists
                if (dashboardData == null)
                {
                    MessageBox.Show("Cannot load dashboard data", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Calculate PO financial summary - use POAmount (the actual PO value)
                var totalPOValue = projectsSummary?.Sum(p => p.POAmount) ?? 0;
                var totalSpent = projectsSummary?.Sum(p => p.TotalSpent) ?? 0;
                var totalRemaining = totalPOValue - totalSpent;

                // Update PO summary cards
                if (TotalProjectsText != null) TotalProjectsText.Text = dashboardData.TotalProjects.ToString();
                if (TotalPOValueText != null) TotalPOValueText.Text = $"${totalPOValue:N0}";
                if (TotalSpentText != null) TotalSpentText.Text = $"${totalSpent:N0}";
                if (TotalRemainingText != null) TotalRemainingText.Text = $"${totalRemaining:N0}";

                // Update Overview cards
                if (OverviewTotalProjectsText != null) OverviewTotalProjectsText.Text = dashboardData.TotalProjects.ToString();
                if (OverviewTotalPOValueText != null) OverviewTotalPOValueText.Text = $"${totalPOValue:N0}";
                if (OverviewTotalSpentText != null) OverviewTotalSpentText.Text = $"${totalSpent:N0}";
                if (OverviewTotalRemainingText != null) OverviewTotalRemainingText.Text = $"${totalRemaining:N0}";

                // Save data for filtering
                _allProjects = projectsSummary?.ToList() ?? new List<ProjectSummary>();

                // Apply current filters (this will update both table and analytics)
                ApplyFilters();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading dashboard data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // Initialize empty data to avoid errors
                _allProjects = new List<ProjectSummary>();
                _filteredProjects = new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                if (ProjectsTableDataGrid != null)
                    ProjectsTableDataGrid.ItemsSource = _filteredProjects;
            }
        }

        private Task LoadProjectAnalytics()
        {
            try
            {
                // Find the ProjectAnalyticsContainer in the XAML
                var container = this.FindName("ProjectAnalyticsContainer") as StackPanel;
                if (container == null)
                {
                    System.Diagnostics.Debug.WriteLine("ProjectAnalyticsContainer not found");
                    return Task.CompletedTask;
                }

                // Clear existing analytics
                container.Children.Clear();

                // Don't load any analytics by default - user must click analysis button for each project
                // Add instruction message for user
                var instructionText = new TextBlock
                {
                    Text = "📊 Click the analysis button (📊) next to any project above to display its analytics here.\n\nYou can display multiple projects at once for comparison.",
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 40, 0, 0),
                    TextWrapping = TextWrapping.Wrap
                };
                container.Children.Add(instructionText);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading project analytics: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        private async System.Threading.Tasks.Task<Border> CreateSimpleProjectAnalytics(Project project)
        {
            var border = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 16),
                BorderBrush = new SolidColorBrush(Color.FromRgb(225, 229, 233)),
                BorderThickness = new Thickness(1)
            };

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Project header
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var icon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.FolderMultiple,
                Width = 20,
                Height = 20,
                Foreground = new SolidColorBrush(Color.FromRgb(103, 58, 183)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 12, 0)
            };

            var nameText = new TextBlock
            {
                Text = $"📁 {project.Name}",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                VerticalAlignment = VerticalAlignment.Center
            };

            headerPanel.Children.Add(icon);
            headerPanel.Children.Add(nameText);
            Grid.SetRow(headerPanel, 0);
            mainGrid.Children.Add(headerPanel);

            // Analytics content grid - Combined layout with sites
            var contentGrid = new Grid();
            contentGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) }); // Wider for combined charts
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Sites section

            // Left section: Combined Bar Charts and Pie Chart
            var combinedSection = CreateCombinedChartsSection(project);
            Grid.SetRow(combinedSection, 0);
            Grid.SetColumn(combinedSection, 0);
            contentGrid.Children.Add(combinedSection);

            // Right section: Sites information (2 sites)
            var sitesSection = await CreateSitesSection(project);
            Grid.SetRow(sitesSection, 0);
            Grid.SetColumn(sitesSection, 1);
            contentGrid.Children.Add(sitesSection);

            Grid.SetRow(contentGrid, 1);
            mainGrid.Children.Add(contentGrid);

            border.Child = mainGrid;
            return border;
        }

        private Border CreateMetricCard(string title, string value, string color)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(16),
                Margin = new Thickness(8, 0, 8, 0)
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            stackPanel.Children.Add(titleText);
            stackPanel.Children.Add(valueText);
            border.Child = stackPanel;

            return border;
        }

        private Border CreateCombinedChartsSection(Project project)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(0, 0, 8, 0)
            };

            var stackPanel = new StackPanel();

            // Title
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var titleIcon = new TextBlock
            {
                Text = "📊",
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var titleText = new TextBlock
            {
                Text = "Spent & Overview",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                VerticalAlignment = VerticalAlignment.Center
            };

            titlePanel.Children.Add(titleIcon);
            titlePanel.Children.Add(titleText);
            stackPanel.Children.Add(titlePanel);

            // Combined content grid
            var combinedGrid = new Grid();
            combinedGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) }); // Bar charts
            combinedGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Pie chart

            // Bar charts section
            var barChartsContainer = new Grid();
            barChartsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            barChartsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Equipment bar chart
            var equipmentChart = CreateBarChart("🔧 Equipment", project.TasksAmount, project.TasksSpentAmount, "#4CAF50");
            Grid.SetColumn(equipmentChart, 0);
            barChartsContainer.Children.Add(equipmentChart);

            // Services bar chart
            var servicesChart = CreateBarChart("🛠️ Services", project.ServicesAmount, project.ServicesSpentAmount, "#FF9800");
            Grid.SetColumn(servicesChart, 1);
            barChartsContainer.Children.Add(servicesChart);

            Grid.SetColumn(barChartsContainer, 0);
            combinedGrid.Children.Add(barChartsContainer);

            // Pie chart section
            var pieChartContainer = CreateCompactPieChart(project);
            Grid.SetColumn(pieChartContainer, 1);
            combinedGrid.Children.Add(pieChartContainer);

            stackPanel.Children.Add(combinedGrid);

            border.Child = stackPanel;
            return border;
        }

        private Border CreateBarChartsSection(Project project)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(0, 0, 8, 16)
            };

            var stackPanel = new StackPanel();

            // Section title
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var titleIcon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.ChartBar,
                Width = 16,
                Height = 16,
                Foreground = new SolidColorBrush(Color.FromRgb(103, 58, 183)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var titleText = new TextBlock
            {
                Text = "📊 Spent",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                VerticalAlignment = VerticalAlignment.Center
            };

            titlePanel.Children.Add(titleIcon);
            titlePanel.Children.Add(titleText);
            stackPanel.Children.Add(titlePanel);

            // Charts container - horizontal layout
            var chartsContainer = new Grid();
            chartsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            chartsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Equipment bar chart
            var equipmentChart = CreateBarChart("🔧 Equipment", project.TasksAmount, project.TasksSpentAmount, "#4CAF50");
            Grid.SetColumn(equipmentChart, 0);
            chartsContainer.Children.Add(equipmentChart);

            // Services bar chart
            var servicesChart = CreateBarChart("🛠️ Services", project.ServicesAmount, project.ServicesSpentAmount, "#FF9800");
            Grid.SetColumn(servicesChart, 1);
            chartsContainer.Children.Add(servicesChart);

            stackPanel.Children.Add(chartsContainer);

            border.Child = stackPanel;
            return border;
        }

        private StackPanel CreateCompactPieChart(Project project)
        {
            var stackPanel = new StackPanel
            {
                Margin = new Thickness(8, 0, 0, 0)
            };

            // Compact pie chart with financial details
            var pieChart = CreateProjectPieChart(project);
            stackPanel.Children.Add(pieChart);

            return stackPanel;
        }

        private async System.Threading.Tasks.Task<Border> CreateSitesSection(Project project)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8, 0, 0, 0)
            };

            var stackPanel = new StackPanel();

            // Title
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var titleIcon = new TextBlock
            {
                Text = "🏗️",
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var titleText = new TextBlock
            {
                Text = "Project Sites",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                VerticalAlignment = VerticalAlignment.Center
            };

            titlePanel.Children.Add(titleIcon);
            titlePanel.Children.Add(titleText);
            stackPanel.Children.Add(titlePanel);

            // Get project sites
            var sites = await App.DataService.GetProjectSitesAsync(project.Id);
            var sitesToShow = sites.Take(2).ToList(); // Show only first 2 sites

            if (sitesToShow.Any())
            {
                foreach (var site in sitesToShow)
                {
                    var siteCard = await CreateSiteCard(site, project);
                    stackPanel.Children.Add(siteCard);
                }

                // If there are more sites, show count
                if (sites.Count() > 2)
                {
                    var moreText = new TextBlock
                    {
                        Text = $"+ {sites.Count() - 2} more sites",
                        FontSize = 11,
                        FontStyle = FontStyles.Italic,
                        Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                        Margin = new Thickness(0, 8, 0, 0),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    stackPanel.Children.Add(moreText);
                }
            }
            else
            {
                var noSitesText = new TextBlock
                {
                    Text = "No sites configured",
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 0)
                };
                stackPanel.Children.Add(noSitesText);
            }

            border.Child = stackPanel;
            return border;
        }

        private async System.Threading.Tasks.Task<Border> CreateSiteCard(ProjectSite site, Project project)
        {
            var border = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(12),
                Margin = new Thickness(0, 0, 0, 8),
                BorderBrush = new SolidColorBrush(Color.FromRgb(225, 229, 233)),
                BorderThickness = new Thickness(1)
            };

            var stackPanel = new StackPanel();

            // Site name
            var nameText = new TextBlock
            {
                Text = site.SiteName,
                FontSize = 12,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                Margin = new Thickness(0, 0, 0, 4)
            };
            stackPanel.Children.Add(nameText);

            // Get site invoices for progress calculation
            var allInvoices = await App.DataService.GetInvoicesAsync();
            var siteInvoices = allInvoices.Where(i => i.ProjectId == project.Id && i.ProjectSiteId == site.Id).ToList();

            var totalInvoiced = siteInvoices.Sum(i => i.AmountUSD);
            var totalPaid = siteInvoices.Sum(i => i.PaidAmount);
            var siteProgress = project.POAmount > 0 ? (totalInvoiced / project.POAmount) * 100 : 0;

            // Progress info
            var progressText = new TextBlock
            {
                Text = $"Progress: {siteProgress:F1}%",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                Margin = new Thickness(0, 0, 0, 2)
            };
            stackPanel.Children.Add(progressText);

            // Financial info
            var financialText = new TextBlock
            {
                Text = $"Invoiced: ${totalInvoiced:N0}",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128))
            };
            stackPanel.Children.Add(financialText);

            border.Child = stackPanel;
            return border;
        }

        private Border CreatePieChartSection(Project project)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8, 0, 0, 16)
            };

            var stackPanel = new StackPanel();

            // Section title
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var titleIcon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.ChartPie,
                Width = 16,
                Height = 16,
                Foreground = new SolidColorBrush(Color.FromRgb(103, 58, 183)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var titleText = new TextBlock
            {
                Text = "🥧 Project Overview",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(50, 54, 58)),
                VerticalAlignment = VerticalAlignment.Center
            };

            titlePanel.Children.Add(titleIcon);
            titlePanel.Children.Add(titleText);
            stackPanel.Children.Add(titlePanel);

            // Pie chart with integrated financial details
            var pieChart = CreateProjectPieChart(project);
            stackPanel.Children.Add(pieChart);

            border.Child = stackPanel;
            return border;
        }

        private Border CreateTotalsSection(Project project)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(0, 0, 0, 0),
                BorderBrush = new SolidColorBrush(Color.FromRgb(225, 229, 233)),
                BorderThickness = new Thickness(1)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Budget - include Equipment + Services + Spare Parts
            var totalBudget = project.TasksAmount + project.ServicesAmount;
            var budgetCard = CreateMetricCard("💰 Total Budget", $"${totalBudget:N0}", "#4CAF50");
            Grid.SetColumn(budgetCard, 0);
            grid.Children.Add(budgetCard);

            // Spent
            var spentCard = CreateMetricCard("💸 Total Spent", $"${project.PaidAmount:N0}", "#FF5722");
            Grid.SetColumn(spentCard, 1);
            grid.Children.Add(spentCard);

            // Remaining
            var remaining = project.POAmount - project.PaidAmount;
            var remainingCard = CreateMetricCard("💵 Remaining", $"${remaining:N0}", "#2196F3");
            Grid.SetColumn(remainingCard, 2);
            grid.Children.Add(remainingCard);

            // Progress percentage
            var progressPercentage = project.POAmount > 0 ? (project.PaidAmount / project.POAmount) * 100 : 0;
            var progressCard = CreateMetricCard("📈 Progress", $"{progressPercentage:F1}%", "#9C27B0");
            Grid.SetColumn(progressCard, 3);
            grid.Children.Add(progressCard);

            border.Child = grid;
            return border;
        }

        private Grid CreateBarChart(string title, decimal budget, decimal spent, string color)
        {
            var remaining = budget - spent;
            var maxValue = Math.Max(budget, Math.Max(spent, remaining));
            
            // Ensure all values are valid and non-negative
            if (budget < 0) budget = 0;
            if (spent < 0) spent = 0;
            if (remaining < 0) remaining = 0;
            if (maxValue < 0) maxValue = 0;

            var grid = new Grid
            {
                Margin = new Thickness(8, 0, 8, 0),
                Width = 180
            };

            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Title
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Percentages on top
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(200) }); // Bars
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Labels at bottom

            // Title
            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 11,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 8)
            };
            Grid.SetRow(titleText, 0);
            grid.Children.Add(titleText);

            // Percentages container (on top of bars)
            var percentagesContainer = new Grid
            {
                Margin = new Thickness(0, 0, 0, 8)
            };
            Grid.SetRow(percentagesContainer, 1);

            percentagesContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            percentagesContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            percentagesContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Calculate percentages
            var budgetPercent = budget > 0 ? 100 : 0;
            var spentPercent = budget > 0 ? (spent / budget) * 100 : 0;
            var remainingPercent = budget > 0 ? (remaining / budget) * 100 : 0;

            // Budget percentage
            var budgetPercentText = new TextBlock
            {
                Text = $"{budgetPercent:F0}%",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(59, 130, 246)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };
            Grid.SetColumn(budgetPercentText, 0);
            percentagesContainer.Children.Add(budgetPercentText);

            // Spent percentage
            var spentPercentText = new TextBlock
            {
                Text = $"{spentPercent:F0}%",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };
            Grid.SetColumn(spentPercentText, 1);
            percentagesContainer.Children.Add(spentPercentText);

            // Remaining percentage
            var remainingPercentText = new TextBlock
            {
                Text = $"{remainingPercent:F0}%",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };
            Grid.SetColumn(remainingPercentText, 2);
            percentagesContainer.Children.Add(remainingPercentText);

            grid.Children.Add(percentagesContainer);

            // Bar container (3 vertical bars)
            var barContainer = new Grid
            {
                Height = 200,
                Margin = new Thickness(0, 0, 0, 8)
            };
            Grid.SetRow(barContainer, 2);

            barContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            barContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            barContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Total Budget bar (blue)
            var budgetHeight = maxValue > 0 ? Math.Max(0, Math.Min(200, (double)(budget / maxValue) * 200)) : 0;
            var budgetBar = new Border
            {
                Background = new LinearGradientBrush
                {
                    StartPoint = new Point(0, 0),
                    EndPoint = new Point(1, 1),
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop(Color.FromRgb(59, 130, 246), 0.0),   // Light blue
                        new GradientStop(Color.FromRgb(37, 99, 235), 1.0)     // Dark blue
                    }
                },
                CornerRadius = new CornerRadius(6),
                Width = 35,
                Height = budgetHeight,
                VerticalAlignment = VerticalAlignment.Bottom,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(3, 0, 3, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 3,
                    Opacity = 0.3
                }
            };
            Grid.SetColumn(budgetBar, 0);
            barContainer.Children.Add(budgetBar);

            // Spent bar (green - المنصرف)
            var spentHeight = maxValue > 0 ? Math.Max(0, Math.Min(200, (double)(spent / maxValue) * 200)) : 0;
            var spentBar = new Border
            {
                Background = new LinearGradientBrush
                {
                    StartPoint = new Point(0, 0),
                    EndPoint = new Point(1, 1),
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop(Color.FromRgb(76, 175, 80), 0.0),   // Light green
                        new GradientStop(Color.FromRgb(56, 142, 60), 1.0)    // Dark green
                    }
                },
                CornerRadius = new CornerRadius(6),
                Width = 35,
                Height = spentHeight,
                VerticalAlignment = VerticalAlignment.Bottom,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(3, 0, 3, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 3,
                    Opacity = 0.3
                }
            };
            Grid.SetColumn(spentBar, 1);
            barContainer.Children.Add(spentBar);

            // Remaining bar (orange - المتبقي)
            var remainingHeight = maxValue > 0 ? Math.Max(0, Math.Min(200, (double)(remaining / maxValue) * 200)) : 0;
            var remainingBar = new Border
            {
                Background = new LinearGradientBrush
                {
                    StartPoint = new Point(0, 0),
                    EndPoint = new Point(1, 1),
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop(Color.FromRgb(255, 152, 0), 0.0),   // Light orange
                        new GradientStop(Color.FromRgb(230, 126, 34), 1.0)   // Dark orange
                    }
                },
                CornerRadius = new CornerRadius(6),
                Width = 35,
                Height = remainingHeight,
                VerticalAlignment = VerticalAlignment.Bottom,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(3, 0, 3, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 3,
                    Opacity = 0.3
                }
            };
            Grid.SetColumn(remainingBar, 2);
            barContainer.Children.Add(remainingBar);

            grid.Children.Add(barContainer);

            // Labels container (at bottom)
            var labelsContainer = new Grid();
            labelsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            labelsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            labelsContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Budget label
            var budgetLabel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            var budgetTitle = new TextBlock
            {
                Text = "Total",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(59, 130, 246)),
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            var budgetValue = new TextBlock
            {
                Text = $"${budget:N0}",
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Medium,
                TextAlignment = TextAlignment.Center
            };
            budgetLabel.Children.Add(budgetTitle);
            budgetLabel.Children.Add(budgetValue);
            Grid.SetColumn(budgetLabel, 0);
            labelsContainer.Children.Add(budgetLabel);

            // Spent label
            var spentLabel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            var spentTitle = new TextBlock
            {
                Text = "Spent",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)), // Green to match bar
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            var spentValue = new TextBlock
            {
                Text = $"${spent:N0}",
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Medium,
                TextAlignment = TextAlignment.Center
            };
            spentLabel.Children.Add(spentTitle);
            spentLabel.Children.Add(spentValue);
            Grid.SetColumn(spentLabel, 1);
            labelsContainer.Children.Add(spentLabel);

            // Remaining label
            var remainingLabel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
            var remainingTitle = new TextBlock
            {
                Text = "Remaining",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)), // Orange to match bar
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            var remainingValue = new TextBlock
            {
                Text = $"${remaining:N0}",
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                HorizontalAlignment = HorizontalAlignment.Center,
                FontWeight = FontWeights.Medium,
                TextAlignment = TextAlignment.Center
            };
            remainingLabel.Children.Add(remainingTitle);
            remainingLabel.Children.Add(remainingValue);
            Grid.SetColumn(remainingLabel, 2);
            labelsContainer.Children.Add(remainingLabel);

            Grid.SetRow(labelsContainer, 3);
            grid.Children.Add(labelsContainer);

            return grid;
        }

        private Grid CreateProjectPieChart(Project project)
        {
            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Pie Chart Canvas
            var canvas = new Canvas
            {
                Width = 140,
                Height = 140,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var total = project.POAmount;
            var spent = project.PaidAmount;
            var remaining = total - spent;

            if (total > 0)
            {
                var spentPercentage = (double)(spent / total);
                var remainingPercentage = 1.0 - spentPercentage;

                var center = new Point(70, 70);
                var radius = 60;

                // Spent (green), Remaining (orange)
                if (spentPercentage > 0)
                {
                    var spentPath = CreatePieSlice(center, radius, 0, spentPercentage * 360, "#4CAF50");
                    canvas.Children.Add(spentPath);
                }

                if (remainingPercentage > 0)
                {
                    var remainingPath = CreatePieSlice(center, radius, spentPercentage * 360, 360, "#FF9800");
                    canvas.Children.Add(remainingPath);
                }

                // Center text with remaining percentage
                var centerText = new TextBlock
                {
                    Text = $"{remainingPercentage * 100:F0}%",
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)), // Dark gray for better visibility
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
                Canvas.SetLeft(centerText, 60);
                Canvas.SetTop(centerText, 62);
                canvas.Children.Add(centerText);
            }

            Grid.SetRow(canvas, 0);
            mainGrid.Children.Add(canvas);

            // Financial Details Grid
            var detailsGrid = new Grid();
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Total Budget
            var totalPanel = CreateFinancialDetailPanel("💰 Total Budget", $"${total:N0}", "#2196F3");
            Grid.SetRow(totalPanel, 0);
            detailsGrid.Children.Add(totalPanel);

            // Total Spent
            var spentPanel = CreateFinancialDetailPanel("💸 Total Spent", $"${spent:N0}", "#4CAF50");
            Grid.SetRow(spentPanel, 1);
            detailsGrid.Children.Add(spentPanel);

            // Remaining
            var remainingPanel = CreateFinancialDetailPanel("💵 Remaining", $"${remaining:N0}", "#FF9800");
            Grid.SetRow(remainingPanel, 2);
            detailsGrid.Children.Add(remainingPanel);

            Grid.SetRow(detailsGrid, 1);
            mainGrid.Children.Add(detailsGrid);

            return mainGrid;
        }

        private StackPanel CreateFinancialDetailPanel(string label, string value, string color)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 4, 0, 4),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // Icon/Label
            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 11,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(75, 85, 99)),
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            // Value
            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(labelText);
            panel.Children.Add(valueText);

            return panel;
        }

        private System.Windows.Shapes.Path CreatePieSlice(Point center, double radius, double startAngle, double endAngle, string color)
        {
            var startAngleRad = (startAngle - 90) * Math.PI / 180; // Start from top
            var endAngleRad = (endAngle - 90) * Math.PI / 180;

            var startPoint = new Point(
                center.X + radius * Math.Cos(startAngleRad),
                center.Y + radius * Math.Sin(startAngleRad)
            );

            var endPoint = new Point(
                center.X + radius * Math.Cos(endAngleRad),
                center.Y + radius * Math.Sin(endAngleRad)
            );

            var largeArc = (endAngle - startAngle) > 180;

            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure { StartPoint = center };

            pathFigure.Segments.Add(new LineSegment(startPoint, true));
            pathFigure.Segments.Add(new ArcSegment(endPoint, new Size(radius, radius), 0, largeArc, SweepDirection.Clockwise, true));
            pathFigure.Segments.Add(new LineSegment(center, true));

            pathGeometry.Figures.Add(pathFigure);

            return new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Stroke = Brushes.White,
                StrokeThickness = 2
            };
        }

        // Navigation event handlers
        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            ShowDashboard();
        }

        private void ActivitiesButton_Click(object sender, RoutedEventArgs e)
        {
            ShowActivities();
        }

        private void RefreshAnalyticsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadAnalyticsData();
        }

        private void RefreshActivitiesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadActivitiesData();
        }

        private void ShowOverview()
        {
            // Hide all other views and show Overview only
            OverviewScrollViewer.Visibility = Visibility.Visible;
            ProjectsTableScrollViewer.Visibility = Visibility.Collapsed;
            DashboardScrollViewer.Visibility = Visibility.Collapsed;
            AnalyticsScrollViewer.Visibility = Visibility.Collapsed;
            ActivitiesScrollViewer.Visibility = Visibility.Collapsed;
            ProjectFilesScrollViewer.Visibility = Visibility.Collapsed;

            LoadDashboardData();
        }

        private void ShowProjectsTable()
        {
            // Hide all other views and show Projects Table only
            OverviewScrollViewer.Visibility = Visibility.Collapsed;
            ProjectsTableScrollViewer.Visibility = Visibility.Visible;
            DashboardScrollViewer.Visibility = Visibility.Collapsed;
            AnalyticsScrollViewer.Visibility = Visibility.Collapsed;
            ActivitiesScrollViewer.Visibility = Visibility.Collapsed;
            ProjectFilesScrollViewer.Visibility = Visibility.Collapsed;

            LoadDashboardData();
        }

        private void ShowProjectFiles()
        {
            // Hide all other views and show Project Files only
            OverviewScrollViewer.Visibility = Visibility.Collapsed;
            ProjectsTableScrollViewer.Visibility = Visibility.Collapsed;
            DashboardScrollViewer.Visibility = Visibility.Collapsed;
            AnalyticsScrollViewer.Visibility = Visibility.Collapsed;
            ActivitiesScrollViewer.Visibility = Visibility.Collapsed;
            ProjectFilesScrollViewer.Visibility = Visibility.Visible;

            LoadProjectFilesData();
        }

        private void ShowDashboard()
        {
            // Hide other views and show Dashboard
            OverviewScrollViewer.Visibility = Visibility.Collapsed;
            ProjectsTableScrollViewer.Visibility = Visibility.Collapsed;
            DashboardScrollViewer.Visibility = Visibility.Visible;
            AnalyticsScrollViewer.Visibility = Visibility.Collapsed;
            ActivitiesScrollViewer.Visibility = Visibility.Collapsed;
            ProjectFilesScrollViewer.Visibility = Visibility.Collapsed;

            // Update button styles - SAP Colors
            DashboardButton.Background = new SolidColorBrush(Color.FromRgb(0, 112, 242)); // SAP Blue Active
            ActivitiesButton.Background = Brushes.Transparent; // Inactive

            LoadDashboardData();
        }



        private void ShowActivities()
        {
            // Hide all other views and show Activities only
            OverviewScrollViewer.Visibility = Visibility.Collapsed;
            ProjectsTableScrollViewer.Visibility = Visibility.Collapsed;
            DashboardScrollViewer.Visibility = Visibility.Collapsed;
            AnalyticsScrollViewer.Visibility = Visibility.Collapsed;
            ActivitiesScrollViewer.Visibility = Visibility.Visible;
            ProjectFilesScrollViewer.Visibility = Visibility.Collapsed;

            // Update button styles - SAP Colors
            ActivitiesButton.Background = new SolidColorBrush(Color.FromRgb(0, 112, 242)); // SAP Blue Active
            DashboardButton.Background = Brushes.Transparent; // Inactive

            LoadActivitiesData();
        }

        private void ShowAnalytics()
        {
            // Hide all other views and show Analytics only
            OverviewScrollViewer.Visibility = Visibility.Collapsed;
            ProjectsTableScrollViewer.Visibility = Visibility.Collapsed;
            DashboardScrollViewer.Visibility = Visibility.Collapsed;
            AnalyticsScrollViewer.Visibility = Visibility.Visible;
            ActivitiesScrollViewer.Visibility = Visibility.Collapsed;
            ProjectFilesScrollViewer.Visibility = Visibility.Collapsed;

            LoadAnalyticsData();
        }

        private async void LoadAnalyticsData()
        {
            try
            {
                using var context = new FinancialContext();

                // Load projects with their invoices
                var projects = await context.Projects
                    .Include(p => p.Invoices)
                    .ToListAsync();

                var allInvoices = await context.Invoices
                    .Include(i => i.InvoicePaymentTerms)
                    .ThenInclude(ipt => ipt.ProjectPaymentTerm)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"Analytics: Found {projects.Count()} projects and {allInvoices.Count()} invoices");

                // Calculate analytics metrics - include Equipment + Services + Spare Parts
                var totalBudget = projects.Sum(p => p.TasksAmount + p.ServicesAmount);
                var totalSpent = projects.Sum(p => p.PaidAmount);
                var remaining = totalBudget - totalSpent;
                var activeProjects = projects.Count(p => p.Status == "Active");
                var completedProjects = projects.Count(p => p.Status == "Completed");
                var onHoldProjects = projects.Count(p => p.Status == "On Hold");

                System.Diagnostics.Debug.WriteLine($"Analytics: Budget={totalBudget:C}, Spent={totalSpent:C}, Active={activeProjects}");

                // Calculate performance metrics
                var budgetUtilization = totalBudget > 0 ? (double)(totalSpent / totalBudget) * 100 : 0;
                var completionRate = projects.Count() > 0 ? (completedProjects / (double)projects.Count()) * 100 : 0;
                var efficiency = budgetUtilization > 0 ? Math.Min(100, (completionRate / budgetUtilization) * 100) : 0;

                // Payment Terms Analysis - Find projects close to completing payment terms
                var paymentTermsAnalysis = await AnalyzePaymentTermsCompletion(projects, allInvoices);

                // Update Analytics UI - Commented out for new design
                // AnalyticsTotalBudgetText.Text = totalBudget > 0 ? totalBudget.ToString("C", CultureInfo.CurrentCulture) : "$0";
                // AnalyticsTotalSpentText.Text = totalSpent.ToString("C", CultureInfo.CurrentCulture);
                // AnalyticsActiveProjectsText.Text = activeProjects.ToString();
                // AnalyticsEfficiencyText.Text = $"{efficiency:F0}%";

                // Draw Project Analytics (Financial Overview Style)
                DrawProjectAnalyticsFinancialStyle(projects, allInvoices);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading analytics data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadActivitiesData()
        {
            try
            {
                using var context = new FinancialContext();

                // Get filter values with null checks
                var selectedType = "All Activities";
                var selectedDateRange = "Today";

                if (ActivityTypeFilter?.SelectedItem is ComboBoxItem typeItem && typeItem.Content != null)
                {
                    selectedType = typeItem.Content.ToString() ?? "All Activities";
                }

                if (DateRangeFilter?.SelectedItem is ComboBoxItem dateItem && dateItem.Content != null)
                {
                    selectedDateRange = dateItem.Content.ToString() ?? "Today";
                }

                // Calculate date range
                DateTime startDate = selectedDateRange switch
                {
                    "Today" => DateTime.Today,
                    "Last 7 Days" => DateTime.Today.AddDays(-7),
                    "Last 30 Days" => DateTime.Today.AddDays(-30),
                    "Last 3 Months" => DateTime.Today.AddMonths(-3),
                    _ => DateTime.MinValue
                };

                // Build query
                var query = context.Activities
                    .Include(a => a.Project)
                    .Include(a => a.Invoice)
                    .Include(a => a.Commitment)
                    .AsQueryable();

                // Apply date filter
                if (startDate != DateTime.MinValue)
                {
                    query = query.Where(a => a.Timestamp >= startDate);
                }

                // Apply type filter
                if (selectedType != "All Activities")
                {
                    var filterType = selectedType.TrimEnd('s'); // Remove 's' from "Projects" -> "Project"
                    query = query.Where(a => a.Type == filterType);
                }

                // Get activities ordered by timestamp (newest first)
                var activities = await query
                    .OrderByDescending(a => a.Timestamp)
                    .Take(100) // Limit to last 100 activities
                    .ToListAsync();

                // Store all activities and setup pagination
                allActivities = activities;
                totalItems = allActivities.Count();
                currentPage = 1; // Reset to first page

                LoadCurrentPage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading activities data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Activities Filter Event Handlers
        private void ActivityTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ActivitiesScrollViewer?.Visibility == Visibility.Visible)
            {
                LoadActivitiesData();
            }
        }

        private void DateRangeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ActivitiesScrollViewer?.Visibility == Visibility.Visible)
            {
                LoadActivitiesData();
            }
        }

        // Excel-Style Activity Management Event Handlers
        private void ExportActivities_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                    DefaultExt = "xlsx",
                    FileName = $"Activities_Export_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportActivitiesToFile(saveFileDialog.FileName);
                    MessageBox.Show($"Activities exported successfully to:\n{saveFileDialog.FileName}",
                                    "Export Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting activities: {ex.Message}", "Export Error",
                                MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshActivities_Click(object sender, RoutedEventArgs e)
        {
            LoadActivitiesData();
        }

        private async void ClearActivities_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to clear all activity logs?\n\nThis action cannot be undone.",
                                         "Confirm Clear Activities", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    using var context = new FinancialContext();
                    context.Activities.RemoveRange(context.Activities);
                    await context.SaveChangesAsync();

                    LoadActivitiesData();
                    MessageBox.Show("All activities have been cleared successfully.", "Clear Complete",
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error clearing activities: {ex.Message}", "Clear Error",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }



        private void ExportActivitiesToFile(string filePath)
        {
            try
            {
                var extension = System.IO.Path.GetExtension(filePath).ToLower();

                if (extension == ".csv")
                {
                    ExportActivitiesToCSV(filePath);
                }
                else if (extension == ".xlsx")
                {
                    ExportActivitiesToExcel(filePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Export failed: {ex.Message}");
            }
        }

        private void ExportActivitiesToCSV(string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("№,Category,Action,Title,Description,Location,Date,Time");

            foreach (var activity in allActivities ?? new List<Activity>())
            {
                csv.AppendLine($"{activity.FormattedSequence}," +
                              $"\"{activity.Type}\"," +
                              $"\"{activity.Action}\"," +
                              $"\"{activity.Title}\"," +
                              $"\"{activity.Description}\"," +
                              $"\"{activity.Location}\"," +
                              $"\"{activity.FormattedDate}\"," +
                              $"\"{activity.FormattedTime}\"");
            }

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        private void ExportActivitiesToExcel(string filePath)
        {
            // For now, export as CSV with .xlsx extension
            // In a real application, you would use a library like EPPlus or ClosedXML
            ExportActivitiesToCSV(filePath);
        }

        private void ClearActivityFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            ActivityTypeFilter.SelectedIndex = 0; // All Activities
            DateRangeFilter.SelectedIndex = 0; // Today
            LoadActivitiesData();
        }

        private void LoadCurrentPage()
        {
            try
            {
                // Initialize allActivities if null
                if (allActivities == null)
                {
                    allActivities = new List<Activity>();
                    totalItems = 0;
                }

                var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

                // Ensure current page is valid
                if (currentPage < 1) currentPage = 1;
                if (currentPage > totalPages && totalPages > 0) currentPage = totalPages;

                var pagedActivities = allActivities
                    .Skip((currentPage - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                if (ActivitiesDataGrid != null)
                {
                    ActivitiesDataGrid.ItemsSource = pagedActivities;
                }

                // Update pagination info
                UpdatePaginationInfo();

                // Show/hide empty state
                if (allActivities.Any())
                {
                    if (ActivitiesDataGrid != null) ActivitiesDataGrid.Visibility = Visibility.Visible;
                    if (EmptyStatePanel != null) EmptyStatePanel.Visibility = Visibility.Collapsed;
                }
                else
                {
                    if (ActivitiesDataGrid != null) ActivitiesDataGrid.Visibility = Visibility.Collapsed;
                    if (EmptyStatePanel != null) EmptyStatePanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading page: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdatePaginationInfo()
        {
            try
            {
                var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
                var itemsFrom = totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0;
                var itemsTo = Math.Min(currentPage * pageSize, totalItems);

                // Update text elements with null checks
                if (ItemsFromText != null) ItemsFromText.Text = itemsFrom.ToString();
                if (ItemsToText != null) ItemsToText.Text = itemsTo.ToString();
                if (TotalItemsText != null) TotalItemsText.Text = totalItems.ToString();
                if (CurrentPageText != null) CurrentPageText.Text = $"Page {currentPage} of {Math.Max(1, totalPages)}";

                // Enable/disable navigation buttons with null checks
                if (FirstPageButton != null) FirstPageButton.IsEnabled = currentPage > 1;
                if (PrevPageButton != null) PrevPageButton.IsEnabled = currentPage > 1;
                if (NextPageButton != null) NextPageButton.IsEnabled = currentPage < totalPages;
                if (LastPageButton != null) LastPageButton.IsEnabled = currentPage < totalPages;
            }
            catch (Exception ex)
            {
                // Silently handle pagination update errors
                System.Diagnostics.Debug.WriteLine($"Error updating pagination: {ex.Message}");
            }
        }

        // Pagination Event Handlers
        private void PageSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (PageSizeComboBox?.SelectedItem is ComboBoxItem selectedItem && selectedItem.Content != null)
                {
                    if (int.TryParse(selectedItem.Content.ToString(), out int newPageSize))
                    {
                        pageSize = newPageSize;
                        currentPage = 1; // Reset to first page
                        LoadCurrentPage();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error changing page size: {ex.Message}");
            }
        }

        private void FirstPageButton_Click(object sender, RoutedEventArgs e)
        {
            currentPage = 1;
            LoadCurrentPage();
        }

        private void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (currentPage > 1)
            {
                currentPage--;
                LoadCurrentPage();
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
            if (currentPage < totalPages)
            {
                currentPage++;
                LoadCurrentPage();
            }
        }

        private void LastPageButton_Click(object sender, RoutedEventArgs e)
        {
            var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
            currentPage = Math.Max(1, totalPages);
            LoadCurrentPage();
        }

        // Pagination variables
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalItems = 0;
        private List<Activity> allActivities = new List<Activity>();

        // Activity Navigation
        private void ActivitiesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ActivitiesDataGrid.SelectedItem is Activity activity)
            {
                NavigateToActivity(activity);
            }
        }

        private void GoToActivity_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Activity activity)
            {
                NavigateToActivity(activity);
            }
        }

        private void ActivityItem_MouseEnter(object sender, MouseEventArgs e)
        {
            if (sender is Border border)
            {
                border.Background = new SolidColorBrush(Color.FromRgb(248, 250, 252));
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(103, 58, 183));
            }
        }

        private void ActivityItem_MouseLeave(object sender, MouseEventArgs e)
        {
            if (sender is Border border)
            {
                border.Background = Brushes.White;
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(225, 229, 233));
            }
        }

        private async void NavigateToActivity(Activity activity)
        {
            try
            {
                switch (activity.Type)
                {
                    case "Project":
                        if (activity.ProjectId.HasValue)
                        {
                            await OpenProjectDetails(activity.ProjectId.Value);
                        }
                        break;

                    case "Invoice":
                        if (activity.InvoiceId.HasValue && activity.ProjectId.HasValue)
                        {
                            await OpenInvoiceDetails(activity.InvoiceId.Value, activity.ProjectId.Value);
                        }
                        break;

                    case "Commitment":
                        if (activity.CommitmentId.HasValue && activity.ProjectId.HasValue)
                        {
                            await OpenCommitmentDetails(activity.CommitmentId.Value, activity.ProjectId.Value);
                        }
                        break;

                    default:
                        ShowDashboard();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to activity: {ex.Message}", "Navigation Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async Task OpenProjectDetails(int projectId)
        {
            try
            {
                var project = await App.DataService.GetProjectByIdAsync(projectId);
                if (project != null)
                {
                    var projectWindow = new ProjectDetailsWindow(project.Id);
                    projectWindow.Show();
                }
                else
                {
                    MessageBox.Show("Project not found or has been deleted.", "Project Not Found",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening project: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task OpenInvoiceDetails(int invoiceId, int projectId)
        {
            try
            {
                var invoice = await App.DataService.GetInvoiceByIdAsync(invoiceId);
                if (invoice != null)
                {
                    var invoiceDialog = new InvoiceDialog(invoice);
                    invoiceDialog.ShowDialog();
                }
                else
                {
                    MessageBox.Show("Invoice not found or has been deleted.", "Invoice Not Found",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task OpenCommitmentDetails(int commitmentId, int projectId)
        {
            try
            {
                var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                if (commitment != null)
                {
                    var commitmentWindow = new CommitmentInvoicesWindow(commitmentId);
                    commitmentWindow.Show();
                }
                else
                {
                    MessageBox.Show("Commitment not found or has been deleted.", "Commitment Not Found",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening commitment: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Chart Drawing Functions

        private void DrawPieChart(List<Project> projects)
        {
            try
            {
                // if (PieChartCanvas == null) return;

                // PieChartCanvas.Children.Clear();

                // var completed = projects.Count(p => p.Status == "Completed");
                // var active = projects.Count(p => p.Status == "Active");
                // var onHold = projects.Count(p => p.Status == "On Hold");
                // var total = projects.Count();

                // if (total == 0) return;

                // var completedPercent = (completed * 100.0) / total;
                // var activePercent = (active * 100.0) / total;
                // var onHoldPercent = (onHold * 100.0) / total;

                // Update center text
                // if (PieChartCenterText != null)
                //     PieChartCenterText.Text = $"{completedPercent:F0}%";

                // Draw pie segments - Commented out for new design
                /*
                double startAngle = 0;
                var centerX = 100;
                var centerY = 100;
                var radius = 80;

                // Completed segment (Green)
                if (completed > 0)
                {
                    var sweepAngle = (completedPercent / 100) * 360;
                    var segment = CreatePieSegment(centerX, centerY, radius, startAngle, sweepAngle, "#4CAF50");
                    PieChartCanvas.Children.Add(segment);

                    // Add percentage label
                    AddPieSliceLabel(centerX, centerY, radius * 0.7, startAngle + sweepAngle / 2, $"{completedPercent:F0}%");

                    startAngle += sweepAngle;
                }

                // Active segment (Orange)
                if (active > 0)
                {
                    var sweepAngle = (activePercent / 100) * 360;
                    var segment = CreatePieSegment(centerX, centerY, radius, startAngle, sweepAngle, "#FF9800");
                    PieChartCanvas.Children.Add(segment);

                    // Add percentage label
                    AddPieSliceLabel(centerX, centerY, radius * 0.7, startAngle + sweepAngle / 2, $"{activePercent:F0}%");

                    startAngle += sweepAngle;
                }

                // On Hold segment (Blue)
                if (onHold > 0)
                {
                    var sweepAngle = (onHoldPercent / 100) * 360;
                    var segment = CreatePieSegment(centerX, centerY, radius, startAngle, sweepAngle, "#2196F3");
                    PieChartCanvas.Children.Add(segment);

                    // Add percentage label
                    AddPieSliceLabel(centerX, centerY, radius * 0.7, startAngle + sweepAngle / 2, $"{onHoldPercent:F0}%");
                }
                */
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing pie chart: {ex.Message}");
            }
        }

        private System.Windows.Shapes.Path CreatePieSegment(double centerX, double centerY, double radius, double startAngle, double sweepAngle, string color)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = (startAngle + sweepAngle) * Math.PI / 180;

            var startPoint = new Point(
                centerX + radius * Math.Cos(startAngleRad),
                centerY + radius * Math.Sin(startAngleRad));

            var endPoint = new Point(
                centerX + radius * Math.Cos(endAngleRad),
                centerY + radius * Math.Sin(endAngleRad));

            var pathFigure = new PathFigure
            {
                StartPoint = new Point(centerX, centerY),
                IsClosed = true
            };

            pathFigure.Segments.Add(new LineSegment(startPoint, true));
            pathFigure.Segments.Add(new ArcSegment(endPoint, new Size(radius, radius), 0, sweepAngle > 180, SweepDirection.Clockwise, true));

            var pathGeometry = new PathGeometry();
            pathGeometry.Figures.Add(pathFigure);

            var path = new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Stroke = Brushes.White,
                StrokeThickness = 2
            };

            return path;
        }

        private void AddPieSliceLabel(double centerX, double centerY, double radius, double angle, string text)
        {
            var angleRad = angle * Math.PI / 180;
            var x = centerX + radius * Math.Cos(angleRad);
            var y = centerY + radius * Math.Sin(angleRad);

            var textBlock = new TextBlock
            {
                Text = text,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // Position the text
            Canvas.SetLeft(textBlock, x - 15); // Offset to center the text
            Canvas.SetTop(textBlock, y - 8);

            // PieChartCanvas.Children.Add(textBlock);
        }

        private void DrawBarChart(List<Project> projects, List<Invoice> invoices)
        {
            try
            {
                // Commented out for new design
                /*
                if (BarChartCanvas == null) return;

                BarChartCanvas.Children.Clear();

                var totalBudget = projects.Sum(p => p.TasksAmount + p.ServicesAmount);
                var totalSpent = invoices.Sum(i => i.AmountUSD);

                if (totalBudget == 0) return;


                var chartHeight = 150;
                var barWidth = 60;
                var spacing = 80;

                // Budget bar (Blue - Total Budget)
                var budgetHeight = chartHeight;
                var budgetBar = new Rectangle
                {
                    Width = barWidth,
                    Height = budgetHeight,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                    Stroke = Brushes.White,
                    StrokeThickness = 2
                };
                Canvas.SetLeft(budgetBar, 50);
                Canvas.SetTop(budgetBar, chartHeight - budgetHeight + 25);
                BarChartCanvas.Children.Add(budgetBar);

                // Budget label with percentage
                var budgetLabel = new TextBlock
                {
                    Text = $"100%\n{totalBudget:C0}",
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center
                };
                Canvas.SetLeft(budgetLabel, 50 + (barWidth - 40) / 2);
                Canvas.SetTop(budgetLabel, 5);
                BarChartCanvas.Children.Add(budgetLabel);

                // Spent bar (Green - Completed/Paid)
                var spentRatio = totalBudget > 0 ? (double)totalSpent / (double)totalBudget : 0;
                var spentHeight = chartHeight * spentRatio;
                var spentBar = new Rectangle
                {
                    Width = barWidth,
                    Height = spentHeight,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                    Stroke = Brushes.White,
                    StrokeThickness = 2
                };
                Canvas.SetLeft(spentBar, 50 + spacing);
                Canvas.SetTop(spentBar, chartHeight - spentHeight + 25);
                BarChartCanvas.Children.Add(spentBar);

                // Spent label with percentage
                var spentPercentage = totalBudget > 0 ? (spentRatio * 100) : 0;
                var spentLabel = new TextBlock
                {
                    Text = $"{spentPercentage:F0}%\n{totalSpent:C0}",
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center
                };
                Canvas.SetLeft(spentLabel, 50 + spacing + (barWidth - 40) / 2);
                Canvas.SetTop(spentLabel, 5);
                BarChartCanvas.Children.Add(spentLabel);

                // X-axis labels
                var budgetXLabel = new TextBlock
                {
                    Text = "Budget",
                    FontSize = 10,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                Canvas.SetLeft(budgetXLabel, 50 + (barWidth - 30) / 2);
                Canvas.SetTop(budgetXLabel, chartHeight + 30);
                BarChartCanvas.Children.Add(budgetXLabel);

                var spentXLabel = new TextBlock
                {
                    Text = "Spent",
                    FontSize = 10,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                Canvas.SetLeft(spentXLabel, 50 + spacing + (barWidth - 30) / 2);
                Canvas.SetTop(spentXLabel, chartHeight + 30);
                BarChartCanvas.Children.Add(spentXLabel);
                */
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing bar chart: {ex.Message}");
            }
        }

        private void DrawLineChart()
        {
            try
            {
                // Commented out for new design
                /*
                if (LineChartCanvas == null) return;

                LineChartCanvas.Children.Clear();

                // No sample data - will be populated with real project data
                var monthlyData = new double[] { 0, 0, 0, 0, 0, 0 };
                var maxValue = monthlyData.Max();

                var chartWidth = 250;
                var chartHeight = 140;
                var pointSpacing = chartWidth / (monthlyData.Length - 1);

                var points = new PointCollection();

                for (int i = 0; i < monthlyData.Length; i++)
                {
                    var x = i * pointSpacing + 25;
                    var y = chartHeight - (monthlyData[i] / maxValue * chartHeight) + 20;
                    points.Add(new Point(x, y));
                }

                // Draw line
                var polyline = new Polyline
                {
                    Points = points,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                    StrokeThickness = 3,
                    Fill = Brushes.Transparent
                };
                LineChartCanvas.Children.Add(polyline);

                // Draw points
                foreach (var point in points)
                {
                    var circle = new Ellipse
                    {
                        Width = 8,
                        Height = 8,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                        Stroke = Brushes.White,
                        StrokeThickness = 2
                    };
                    Canvas.SetLeft(circle, point.X - 4);
                    Canvas.SetTop(circle, point.Y - 4);
                    LineChartCanvas.Children.Add(circle);
                }
                */
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing line chart: {ex.Message}");
            }
        }

        private void DrawGaugeChart(double efficiency)
        {
            try
            {
                // Commented out for new design
                /*
                if (GaugeChartCanvas == null) return;

                GaugeChartCanvas.Children.Clear();

                var centerX = 100;
                var centerY = 120;
                var radius = 70;
                var strokeThickness = 15;

                // Background arc (Gray)
                var backgroundArc = CreateArc(centerX, centerY, radius, -180, 180, "#E0E0E0", strokeThickness);
                GaugeChartCanvas.Children.Add(backgroundArc);

                // Performance arc (Color based on efficiency) - Using consistent colors
                var performanceAngle = (efficiency / 100) * 180;
                var color = efficiency >= 80 ? "#4CAF50" : efficiency >= 60 ? "#FF9800" : "#2196F3";
                var performanceArc = CreateArc(centerX, centerY, radius, -180, performanceAngle, color, strokeThickness);
                GaugeChartCanvas.Children.Add(performanceArc);

                // Update performance text
                if (PerformanceScoreText != null)
                {
                    PerformanceScoreText.Text = $"{efficiency:F0}%";
                    PerformanceScoreText.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
                }
                */
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing gauge chart: {ex.Message}");
            }
        }

        private System.Windows.Shapes.Path CreateArc(double centerX, double centerY, double radius, double startAngle, double sweepAngle, string color, double strokeThickness)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = (startAngle + sweepAngle) * Math.PI / 180;

            var startPoint = new Point(
                centerX + radius * Math.Cos(startAngleRad),
                centerY + radius * Math.Sin(startAngleRad));

            var endPoint = new Point(
                centerX + radius * Math.Cos(endAngleRad),
                centerY + radius * Math.Sin(endAngleRad));

            var pathFigure = new PathFigure
            {
                StartPoint = startPoint,
                IsClosed = false
            };

            pathFigure.Segments.Add(new ArcSegment(endPoint, new Size(radius, radius), 0, Math.Abs(sweepAngle) > 180, SweepDirection.Clockwise, true));

            var pathGeometry = new PathGeometry();
            pathGeometry.Figures.Add(pathFigure);

            var path = new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                StrokeThickness = strokeThickness,
                StrokeStartLineCap = PenLineCap.Round,
                StrokeEndLineCap = PenLineCap.Round
            };

            return path;
        }

        // End of Analytics section

        #region Project Management

        // Removed complex chart functions for simplicity

        private async void AddProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectDialog();
            if (dialog.ShowDialog() == true)
            {
                LoadDashboardData();
                // Also refresh project analytics to show new project
                await LoadProjectAnalytics();
            }
        }

        private async Task<List<PaymentTermsAnalysis>> AnalyzePaymentTermsCompletion(List<Project> projects, List<Invoice> allInvoices)
        {
            var analysis = new List<PaymentTermsAnalysis>();

            using var context = new FinancialContext();

            foreach (var project in projects)
            {
                try
                {
                    var projectInvoices = allInvoices.Where(i => i.ProjectId == project.Id).ToList();

                    // Load payment terms for this project
                    var projectPaymentTerms = await context.ProjectPaymentTerms
                        .Where(pt => pt.ProjectId == project.Id && pt.IsActive)
                        .ToListAsync();

                    if (!projectPaymentTerms.Any()) continue;

                    // Calculate total percentage completed for this project
                    decimal totalExpectedPercentage = projectPaymentTerms.Sum(pt => pt.Percentage);
                    decimal totalCompletedPercentage = 0;

                    foreach (var paymentTerm in projectPaymentTerms)
                    {
                        // Find invoices that used this payment term
                        var invoicesWithThisTerm = projectInvoices
                            .Where(i => i.InvoicePaymentTerms?.Any(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id) == true)
                            .ToList();

                        if (invoicesWithThisTerm.Any())
                        {
                            // Calculate completed percentage for this term
                            var completedForThisTerm = invoicesWithThisTerm
                                .SelectMany(i => i.InvoicePaymentTerms)
                                .Where(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id)
                                .Sum(ipt => ipt.AppliedPercentage);

                            totalCompletedPercentage += completedForThisTerm;
                        }
                    }

                    var completionPercentage = totalExpectedPercentage > 0 ? (totalCompletedPercentage / totalExpectedPercentage) * 100 : 0;

                    analysis.Add(new PaymentTermsAnalysis
                    {
                        ProjectId = project.Id,
                        ProjectName = project.Name,
                        TotalPaymentTerms = projectPaymentTerms.Count(),
                        CompletionPercentage = (double)completionPercentage,
                        RemainingPercentage = (double)(100 - completionPercentage),
                        IsNearCompletion = completionPercentage >= 80 && completionPercentage < 100,
                        IsCompleted = completionPercentage >= 100
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error analyzing payment terms for project {project.Name}: {ex.Message}");
                }
            }

            return analysis.OrderByDescending(a => a.CompletionPercentage).ToList();
        }

        public class PaymentTermsAnalysis
        {
            public int ProjectId { get; set; }
            public string ProjectName { get; set; } = "";
            public int TotalPaymentTerms { get; set; }
            public double CompletionPercentage { get; set; }
            public double RemainingPercentage { get; set; }
            public bool IsNearCompletion { get; set; }
            public bool IsCompleted { get; set; }
        }



        // Project management event handlers

        private void ViewProjectDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            // Handle ProjectsSummaryDataGrid
            ProjectSummary? projectSummary = null;

            if (ProjectsSummaryDataGrid?.SelectedItem is ProjectSummary summary)
            {
                projectSummary = summary;
            }
            else
            {
                // Try to get from button's DataContext
                if (sender is Button button && button.DataContext is ProjectSummary summaryFromButton)
                {
                    projectSummary = summaryFromButton;
                }
                // Handle TextBlock click from project name
                else if (sender is TextBlock textBlock && textBlock.DataContext is ProjectSummary summaryFromTextBlock)
                {
                    projectSummary = summaryFromTextBlock;
                }
            }

            int projectId = projectSummary?.Id ?? 0;

            if (projectId > 0)
            {
                NavigateToProjectDetails(projectId);
            }
        }

        private void NavigateToProjectDetails(int projectId)
        {
            if (_isNavigating) return;

            try
            {
                _isNavigating = true;

                // Hide the main dashboard
                this.Hide();

                // Create and show project details window
                var detailsWindow = new ProjectDetailsWindow(projectId);

                // Add back button functionality
                AddBackButtonToWindow(detailsWindow);

                // Push current window to navigation stack
                _navigationStack.Push(this);

                // Show the details window
                detailsWindow.ShowDialog();

                // When details window closes, refresh dashboard data and show main window again
                LoadDashboardData(); // Refresh data to reflect any changes made in project details
                this.Show();

                // Clear navigation flag
                _isNavigating = false;
            }
            catch (Exception ex)
            {
                _isNavigating = false;
                this.Show();
                MessageBox.Show($"Error opening project details: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddBackButtonToWindow(Window window)
        {
            try
            {
                // Add KeyDown event handler for Escape key
                window.KeyDown += (sender, e) =>
                {
                    if (e.Key == Key.Escape)
                    {
                        NavigateBack(window);
                    }
                };

                // For ProjectDetailsWindow, add back button to the header
                if (window is ProjectDetailsWindow)
                {
                    AddBackButtonToProjectDetailsWindow(window);
                }
                // For other windows, use generic approach
                else if (window.Content is Grid mainGrid)
                {
                    AddBackButtonToGrid(mainGrid, window);
                }
                else if (window.Content is DockPanel dockPanel)
                {
                    AddBackButtonToDockPanel(dockPanel, window);
                }
                else if (window.Content is StackPanel stackPanel)
                {
                    AddBackButtonToStackPanel(stackPanel, window);
                }
            }
            catch (Exception ex)
            {
                // Silently handle back button addition errors
                System.Diagnostics.Debug.WriteLine($"Error adding back button: {ex.Message}");
            }
        }

        private void AddBackButtonToProjectDetailsWindow(Window window)
        {
            try
            {
                if (window.Content is Grid mainGrid && mainGrid.Children.Count > 0)
                {
                    // Find the header Border (first child with Background="#4A5568")
                    Border? headerBorder = null;
                    foreach (var child in mainGrid.Children)
                    {
                        if (child is Border border && border.Background is SolidColorBrush brush)
                        {
                            if (brush.Color == Color.FromRgb(74, 85, 104)) // #4A5568
                            {
                                headerBorder = border;
                                break;
                            }
                        }
                    }

                    if (headerBorder?.Child is Grid headerGrid)
                    {
                        // Find the left StackPanel (Column 0)
                        StackPanel? leftStackPanel = null;
                        foreach (var child in headerGrid.Children)
                        {
                            if (child is StackPanel sp && Grid.GetColumn(sp) == 0)
                            {
                                leftStackPanel = sp;
                                break;
                            }
                        }

                        if (leftStackPanel != null)
                        {
                            // Create back button with ProjectDetailsWindow styling
                            var backButton = CreateProjectDetailsBackButton(window);

                            // Insert at the beginning of the left StackPanel
                            leftStackPanel.Children.Insert(0, backButton);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding back button to ProjectDetailsWindow: {ex.Message}");
                // Fallback to generic approach
                if (window.Content is Grid grid)
                {
                    AddBackButtonToGrid(grid, window);
                }
            }
        }

        private Button CreateProjectDetailsBackButton(Window window)
        {
            var backButton = new Button
            {
                Style = Application.Current.FindResource("MaterialDesignOutlinedButton") as Style,
                BorderBrush = Brushes.White,
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 24, 0),
                Padding = new Thickness(16, 8, 16, 8),
                FontWeight = FontWeights.Medium,
                Cursor = Cursors.Hand,
                VerticalAlignment = VerticalAlignment.Center
            };

            // Create content with icon and text
            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // Add back icon
            var icon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.ArrowLeft,
                Width = 18,
                Height = 18,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            // Add text
            var textBlock = new TextBlock
            {
                Text = "Back to Dashboard",
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(icon);
            stackPanel.Children.Add(textBlock);
            backButton.Content = stackPanel;

            backButton.Click += (sender, e) => NavigateBack(window);

            return backButton;
        }

        private void AddBackButtonToGrid(Grid grid, Window window)
        {
            try
            {
                // Create back button
                var backButton = CreateBackButton(window);

                // Add to top-left corner
                Grid.SetRow(backButton, 0);
                Grid.SetColumn(backButton, 0);
                backButton.HorizontalAlignment = HorizontalAlignment.Left;
                backButton.VerticalAlignment = VerticalAlignment.Top;
                backButton.Margin = new Thickness(10, 10, 0, 0);

                grid.Children.Add(backButton);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding back button to grid: {ex.Message}");
            }
        }

        private void AddBackButtonToDockPanel(DockPanel dockPanel, Window window)
        {
            try
            {
                var backButton = CreateBackButton(window);
                DockPanel.SetDock(backButton, Dock.Top);
                backButton.HorizontalAlignment = HorizontalAlignment.Left;
                backButton.Margin = new Thickness(10, 10, 0, 5);

                dockPanel.Children.Insert(0, backButton);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding back button to dock panel: {ex.Message}");
            }
        }

        private void AddBackButtonToStackPanel(StackPanel stackPanel, Window window)
        {
            try
            {
                var backButton = CreateBackButton(window);
                backButton.HorizontalAlignment = HorizontalAlignment.Left;
                backButton.Margin = new Thickness(10, 10, 0, 5);

                stackPanel.Children.Insert(0, backButton);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding back button to stack panel: {ex.Message}");
            }
        }

        private Button CreateBackButton(Window window)
        {
            var backButton = new Button
            {
                Style = Application.Current.FindResource("MaterialDesignRaisedButton") as Style,
                Background = new SolidColorBrush(Color.FromRgb(74, 85, 104)), // Match header color #4A5568
                Foreground = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(74, 85, 104)),
                BorderThickness = new Thickness(1),
                Padding = new Thickness(16, 8, 16, 8),
                FontSize = 12,
                FontWeight = FontWeights.Medium,
                Cursor = Cursors.Hand,
                Margin = new Thickness(10, 10, 0, 0)
            };

            // Create content with icon and text
            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // Add back icon
            var icon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = MaterialDesignThemes.Wpf.PackIconKind.ArrowLeft,
                Width = 16,
                Height = 16,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 6, 0)
            };

            // Add text
            var textBlock = new TextBlock
            {
                Text = "Back to Dashboard",
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 12
            };

            stackPanel.Children.Add(icon);
            stackPanel.Children.Add(textBlock);
            backButton.Content = stackPanel;

            backButton.Click += (sender, e) => NavigateBack(window);

            return backButton;
        }

        private void NavigateBack(Window currentWindow)
        {
            try
            {
                if (_navigationStack.Count > 0)
                {
                    var previousWindow = _navigationStack.Pop();
                    currentWindow.Close();

                    if (previousWindow == this)
                    {
                        // Refresh dashboard data when returning to main window
                        LoadDashboardData();
                        this.Show();
                        this.Activate();
                        this.Focus();
                    }
                    else
                    {
                        previousWindow.Show();
                        previousWindow.Activate();
                        previousWindow.Focus();
                    }
                }
                else
                {
                    // If no previous window, just close current and show main
                    currentWindow.Close();
                    // Refresh dashboard data when returning to main window
                    LoadDashboardData();
                    this.Show();
                    this.Activate();
                    this.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating back: {ex.Message}", "Navigation Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void EditProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                try
                {
                    // Get the full project object
                    var project = await App.DataService.GetProjectByIdAsync(projectSummary.Id);
                    if (project != null)
                    {
                        var dialog = new ProjectDialog(project);
                        if (dialog.ShowDialog() == true)
                        {
                            LoadDashboardData();
                            // Also refresh project analytics to reflect changes
                            await LoadProjectAnalytics();
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading project data: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
            {
                try
                {
                    // Check if project is completely empty before allowing deletion
                    var isEmpty = await CheckIfProjectIsEmpty(projectSummary.Id);
                    if (!isEmpty.IsEmpty)
                    {
                        string criticalItems = "";
                        if (isEmpty.InvoicesCount > 0) criticalItems += $"• Invoices: {isEmpty.InvoicesCount}\n";
                        if (isEmpty.CommitmentsCount > 0) criticalItems += $"• Commitments: {isEmpty.CommitmentsCount}\n";
                        if (isEmpty.ProjectFilesCount > 0) criticalItems += $"• Project Files: {isEmpty.ProjectFilesCount}\n";

                        string autoDeleteItems = "";
                        if (isEmpty.SitesCount > 0) autoDeleteItems += $"• Sites: {isEmpty.SitesCount} (will be auto-deleted)\n";
                        if (isEmpty.PaymentTermsCount > 0) autoDeleteItems += $"• Payment Terms: {isEmpty.PaymentTermsCount} (will be auto-deleted)\n";

                        MessageBox.Show($"❌ Cannot delete project '{projectSummary.Name}'\n\n" +
                            $"Please delete the following items first:\n\n" +
                            $"{criticalItems}\n" +
                            (autoDeleteItems.Length > 0 ? $"The following will be automatically deleted:\n{autoDeleteItems}\n" : "") +
                            $"After deleting the required items, you can delete the project.",
                            "Project Contains Critical Data", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    string autoDeleteMessage = "";
                    if (isEmpty.SitesCount > 0 || isEmpty.PaymentTermsCount > 0)
                    {
                        autoDeleteMessage = $"\n🗑️ The following will also be deleted:\n";
                        if (isEmpty.SitesCount > 0) autoDeleteMessage += $"• {isEmpty.SitesCount} Site(s)\n";
                        if (isEmpty.PaymentTermsCount > 0) autoDeleteMessage += $"• {isEmpty.PaymentTermsCount} Payment Term(s)\n";
                    }

                    var result = MessageBox.Show($"Are you sure you want to delete project '{projectSummary.Name}'?\n\n" +
                        $"✅ No critical data found (invoices, commitments, files).\n" +
                        autoDeleteMessage +
                        $"\n⚠️ This action cannot be undone!",
                        "Confirm Deletion", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        var deleteResult = await App.DataService.DeleteProjectAsync(projectSummary.Id);
                        if (deleteResult)
                        {
                            LoadDashboardData();

                            // Remove the deleted project's analytics from display
                            RemoveProjectAnalytics(projectSummary.Id);

                            MessageBox.Show($"✅ Empty project '{projectSummary.Name}' has been successfully deleted.\n\nIts analytics have also been removed from the display.",
                                "Deleted", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("The project to be deleted was not found", "Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"An error occurred while checking/deleting the project:\n\n{ex.Message}\n\nPlease try again or contact technical support.",
                        "Deletion Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Helper class for project emptiness check
        private class ProjectEmptinessResult
        {
            public bool IsEmpty { get; set; }
            public int InvoicesCount { get; set; }
            public int CommitmentsCount { get; set; }
            public int ProjectFilesCount { get; set; }
            public int SitesCount { get; set; }
            public int PaymentTermsCount { get; set; }
        }

        private async Task<ProjectEmptinessResult> CheckIfProjectIsEmpty(int projectId)
        {
            try
            {
                // Only check for critical data that prevents deletion: Invoices, Commitments, and Project Files
                var invoices = await App.DataService.GetInvoicesByProjectAsync(projectId);
                var commitments = await App.DataService.GetCommitmentsAsync();
                var projectCommitments = commitments.Where(c => c.ProjectId == projectId).ToList();
                var projectFiles = await App.DataService.GetProjectFilesAsync(projectId);

                // Get payment terms and sites for display purposes only (they will be auto-deleted)
                var paymentTerms = await App.DataService.GetProjectPaymentTermsAsync(projectId);
                var sites = await App.DataService.GetProjectSitesAsync(projectId);

                var result = new ProjectEmptinessResult
                {
                    InvoicesCount = invoices.Count,
                    CommitmentsCount = projectCommitments.Count,
                    ProjectFilesCount = projectFiles.Count,
                    SitesCount = sites.Count,
                    PaymentTermsCount = paymentTerms.Count
                };

                // Only check critical data for deletion permission
                result.IsEmpty = result.InvoicesCount == 0 &&
                                result.CommitmentsCount == 0 &&
                                result.ProjectFilesCount == 0;

                return result;
            }
            catch (Exception)
            {
                // If there's an error checking, assume not empty for safety
                return new ProjectEmptinessResult { IsEmpty = false };
            }
        }

        private void RemoveProjectAnalytics(int projectId)
        {
            try
            {
                var container = this.FindName("ProjectAnalyticsContainer") as StackPanel;
                if (container != null)
                {
                    // Find and remove the analytics panel for this project
                    var panelToRemove = container.Children.OfType<Border>()
                        .FirstOrDefault(border => border.Tag != null && border.Tag.ToString() == projectId.ToString());

                    if (panelToRemove != null)
                    {
                        container.Children.Remove(panelToRemove);

                        // If no more analytics are displayed, add instruction text back
                        var remainingAnalytics = container.Children.OfType<Border>().Count();
                        if (remainingAnalytics == 0)
                        {
                            var instructionText = new TextBlock
                            {
                                Text = "📊 Click the analysis button (📊) next to any project above to display its analytics here.\n\nYou can display multiple projects at once for comparison.",
                                FontSize = 14,
                                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                                HorizontalAlignment = HorizontalAlignment.Center,
                                TextAlignment = TextAlignment.Center,
                                Margin = new Thickness(0, 40, 0, 0),
                                TextWrapping = TextWrapping.Wrap
                            };
                            container.Children.Add(instructionText);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing project analytics: {ex.Message}");
            }
        }

        private async void AnalyzeProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProjectSummary projectSummary)
            {
                try
                {
                    // Get the full project object with all related data
                    var project = await App.DataService.GetProjectByIdAsync(projectSummary.Id);
                    if (project != null)
                    {
                        var container = this.FindName("ProjectAnalyticsContainer") as StackPanel;
                        if (container != null)
                        {
                            // Check if this project's analysis is already displayed
                            bool alreadyExists = false;
                            foreach (Border existingBorder in container.Children.OfType<Border>())
                            {
                                if (existingBorder.Tag != null && existingBorder.Tag.ToString() == project.Id.ToString())
                                {
                                    alreadyExists = true;
                                    break;
                                }
                            }

                            if (alreadyExists)
                            {
                                MessageBox.Show($"📊 Analysis for project '{project.Name}' is already displayed in the Analytics section.",
                                    "Already Displayed", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                            else
                            {
                                // Remove instruction text if it exists
                                var instructionText = container.Children.OfType<TextBlock>().FirstOrDefault();
                                if (instructionText != null)
                                {
                                    container.Children.Remove(instructionText);
                                }

                                // Add analytics for this specific project (don't clear existing ones)
                                var analyticsPanel = await CreateSimpleProjectAnalytics(project);
                                analyticsPanel.Tag = project.Id.ToString(); // Tag to identify this project's panel
                                container.Children.Add(analyticsPanel);

                                // Count only Border elements (actual project analytics)
                                var projectCount = container.Children.OfType<Border>().Count();

                                // Success message removed as requested
                            }
                        }
                        else
                        {
                            MessageBox.Show("Analytics container not found. Please try refreshing the page.",
                                "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    else
                    {
                        MessageBox.Show("Project not found. It may have been deleted.",
                            "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading project analysis: {ex.Message}",
                        "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ClearAnalytics_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var container = this.FindName("ProjectAnalyticsContainer") as StackPanel;
                if (container != null)
                {
                    container.Children.Clear();

                    // Add instruction message back
                    var instructionText = new TextBlock
                    {
                        Text = "📊 Click the analysis button (📊) next to any project above to display its analytics here.\n\nYou can display multiple projects at once for comparison.",
                        FontSize = 14,
                        Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        TextAlignment = TextAlignment.Center,
                        Margin = new Thickness(0, 40, 0, 0),
                        TextWrapping = TextWrapping.Wrap
                    };
                    container.Children.Add(instructionText);

                    MessageBox.Show("📊 Analytics section has been cleared and is ready for new selections.",
                        "Analytics Cleared", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("Analytics container not found.",
                        "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing analytics: {ex.Message}",
                    "Clear Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Search and filter functions
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception)
            {
                // Silently handle search errors
            }
        }



        private void ApplyFilters()
        {
            try
            {
                if (_allProjects == null || !_allProjects.Any())
                {
                    _filteredProjects = new List<ProjectSummary>();
                    if (ProjectsSummaryDataGrid != null)
                        ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                    return;
                }

                var filtered = _allProjects.AsEnumerable();

                // Text search filtering
                if (SearchTextBox != null && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(p =>
                        (p.Name?.ToLower().Contains(searchText) ?? false) ||
                        (p.Description?.ToLower().Contains(searchText) ?? false));
                }

                // Status filtering


                _filteredProjects = filtered.ToList();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                if (ProjectsTableDataGrid != null)
                    ProjectsTableDataGrid.ItemsSource = _filteredProjects;

                // Update analytics for filtered projects
                LoadProjectAnalyticsForFiltered();
            }
            catch (Exception)
            {
                // In case of error, show all projects
                _filteredProjects = _allProjects?.ToList() ?? new List<ProjectSummary>();
                if (ProjectsSummaryDataGrid != null)
                    ProjectsSummaryDataGrid.ItemsSource = _filteredProjects;
                if (ProjectsTableDataGrid != null)
                    ProjectsTableDataGrid.ItemsSource = _filteredProjects;

                // Update analytics for all projects in case of error
                LoadProjectAnalyticsForFiltered();
            }
        }

        private void LoadProjectAnalyticsForFiltered()
        {
            try
            {
                var container = this.FindName("ProjectAnalyticsContainer") as StackPanel;
                if (container == null) return;

                container.Children.Clear();

                // Don't load any analytics by default - user must click analysis button for each project
                // Add instruction message for user
                var instructionText = new TextBlock
                {
                    Text = "📊 Click the analysis button (📊) next to any project above to display its analytics here.\n\nYou can display multiple projects at once for comparison.",
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 40, 0, 0),
                    TextWrapping = TextWrapping.Wrap
                };
                container.Children.Add(instructionText);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading filtered project analytics: {ex.Message}");
            }
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SearchTextBox != null) SearchTextBox.Text = "";
                ApplyFilters();
            }
            catch (Exception)
            {
                // Silently handle clear filters errors
            }
        }

        private void ProjectName_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (sender is TextBlock textBlock && textBlock.DataContext is ProjectSummary project)
                {
                    // Navigate to project details with improved navigation
                    NavigateToProjectDetails(project.Id);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening project details: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadDashboardData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // File management functions
        private void ViewProjectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is ProjectSummary projectSummary)
                {
                    var filesWindow = new ProjectFilesWindow(projectSummary.Id);
                    filesWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening files window: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Converters

        // Converter for utilization progress bar width
        public class UtilizationToWidthConverter : IValueConverter
        {
            public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
            {
                if (value is string utilizationStr && utilizationStr.EndsWith("%"))
                {
                    if (double.TryParse(utilizationStr.Replace("%", ""), out double percentage))
                    {
                        return Math.Max(0, Math.Min(100, percentage)); // Clamp between 0 and 100
                    }
                }
                return 0;
            }

            public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
            {
                throw new NotImplementedException();
            }
        }

        #endregion

        #region Activity Logging

        private async Task LogActivityAsync(Activity activity)
        {
            try
            {
                using var context = new FinancialContext();
                context.Activities.Add(activity);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error logging activity: {ex.Message}");
                // Don't show error to user for activity logging failures
            }
        }

        // Helper methods to log common activities
        private async Task LogProjectActivityAsync(string type, Project project, string description = "")
        {
            var activity = ActivityHelper.CreateProjectActivity(type, project, description);
            await LogActivityAsync(activity);
        }

        private async Task LogInvoiceActivityAsync(string type, Invoice invoice, string description = "")
        {
            var activity = ActivityHelper.CreateInvoiceActivity(type, invoice, description);
            await LogActivityAsync(activity);
        }

        private async Task LogCommitmentActivityAsync(string type, Commitment commitment, string description = "")
        {
            var activity = ActivityHelper.CreateCommitmentActivity(type, commitment, description);
            await LogActivityAsync(activity);
        }

        private async Task LogGeneralActivityAsync(string type, string title, string description = "", string details = "")
        {
            var activity = ActivityHelper.CreateGeneralActivity(type, title, description, details);
            await LogActivityAsync(activity);
        }



        private async void TestInvoiceClassification_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                using var context = new FinancialContext();
                var invoices = await context.Invoices
                    .Include(i => i.Project)
                    .ToListAsync();

                var results = new List<string>();
                results.Add("🧪 Invoice Classification Test");
                results.Add("=====================================");
                results.Add("");

                foreach (var invoice in invoices)
                {
                    results.Add($"📄 Invoice: {invoice.InvoiceNumber}");
                    results.Add($"   Type: '{invoice.Type}'");
                    results.Add($"   Description: '{invoice.Description}'");
                    results.Add($"   Amount: ${invoice.AmountUSD:F2}");
                    results.Add($"   Paid: ${invoice.PaidAmount:F2}");

                    // Test classification logic
                    var classification = TestInvoiceClassificationLogic(invoice);
                    results.Add($"   Will be classified as: {classification}");

                    if (classification == "UNCATEGORIZED (will go to TASKS)")
                    {
                        results.Add($"   ⚠️ WARNING: This invoice will fall into Tasks by default!");
                    }

                    results.Add("");
                }

                // Show results
                var resultText = string.Join("\n", results);
                var testDialog = new Views.TextInputDialog(resultText, "Invoice Classification Test");
                testDialog.InputTextBox.Text = resultText;
                testDialog.InputTextBox.IsReadOnly = true;
                testDialog.InputTextBox.Height = 400;
                testDialog.Height = 500;
                testDialog.Width = 700;
                testDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error testing classification: {ex.Message}", "Test Error",
                                MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string TestInvoiceClassificationLogic(Invoice invoice)
        {
            var invoiceType = invoice.Type ?? "";
            var description = invoice.Description ?? "";

            // Test Hardware classification
            if (invoiceType.ToLower().Contains("dell") || invoiceType.ToLower().Contains("vmware") ||
                invoiceType.ToLower().Contains("hw sizing") || invoiceType.ToLower().Contains("hardware"))
            {
                return "HARDWARE";
            }

            if (invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) &&
                (description.ToLower().Contains("hardware") || description.ToLower().Contains("hw")))
            {
                return "HARDWARE";
            }

            // Test Software classification
            if (invoiceType.ToLower().Contains("license") || invoiceType.ToLower().Contains("operating") ||
                invoiceType.ToLower().Contains("sw license") || invoiceType.ToLower().Contains("software"))
            {
                return "SOFTWARE";
            }

            if (invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) &&
                (description.ToLower().Contains("software") || description.ToLower().Contains("sw")))
            {
                return "SOFTWARE";
            }

            // Test Services classification
            if (invoiceType.Equals("SERVICES", StringComparison.OrdinalIgnoreCase) ||
                invoiceType.ToLower().Contains("service") ||
                invoiceType.ToLower().Contains("professional") ||
                invoiceType.ToLower().Contains("maintenance") ||
                invoiceType.ToLower().Contains("support"))
            {
                return "SERVICES";
            }

            // Test Spare Parts classification
            if (invoiceType.Equals("Spare Parts", StringComparison.OrdinalIgnoreCase) ||
                invoiceType.ToLower().Contains("spare"))
            {
                return "SPARE PARTS";
            }

            // Test Equipment classification (formerly Tasks)
            if (invoiceType.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) ||
                invoiceType.ToLower().Contains("task") ||
                invoiceType.ToLower().Contains("equipment"))
            {
                return "EQUIPMENT";
            }

            return "UNCATEGORIZED (will go to EQUIPMENT)";
        }

        private async void ToggleAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (AnalysisSection == null)
                {
                    MessageBox.Show("AnalysisSection not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (AnalysisSection.Visibility == Visibility.Collapsed)
                {
                    // Show analysis
                    AnalysisSection.Visibility = Visibility.Visible;
                    ToggleAnalysisButton.Content = "📊 Hide Analysis";
                    await LoadAnalysisData();
                }
                else
                {
                    // Hide analysis
                    AnalysisSection.Visibility = Visibility.Collapsed;
                    ToggleAnalysisButton.Content = "📊 Show Analysis";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error toggling analysis: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadAnalysisData()
        {
            try
            {
                using var context = new FinancialContext();

                // Get all projects with invoices
                var projects = await context.Projects
                    .Include(p => p.Invoices)
                    .ToListAsync();
                var invoices = await context.Invoices.ToListAsync();

                // Calculate totals
                var totalBudget = projects.Sum(p => p.POAmount);
                var totalSpent = invoices.Sum(i => i.AmountUSD);

                // Create Financial Overview Style Analysis (same as Financial Overview)
                CreateFinancialOverviewStyleAnalysis(projects, invoices);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading analysis data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateFinancialOverviewStyleAnalysis(List<Project> projects, List<Invoice> invoices)
        {
            try
            {
                // Calculate totals
                var totalBudget = projects.Sum(p => p.POAmount);
                var totalSpent = invoices.Sum(i => i.AmountUSD);
                var totalRemaining = totalBudget - totalSpent;

                // Create Bar Chart (exactly like Financial Overview)
                CreateAnalysisBarChart(totalBudget, totalSpent, totalRemaining);

                // Create Donut Chart (exactly like Financial Overview)
                CreateAnalysisDonutChart(totalBudget, totalSpent, totalRemaining);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating financial overview style analysis: {ex.Message}");
            }
        }

        private void CreateAnalysisBarChart(decimal totalBudget, decimal totalSpent, decimal totalRemaining)
        {
            try
            {
                if (AnalysisModernBarChart == null) return;
                AnalysisModernBarChart.Children.Clear();

                if (totalBudget <= 0) return;

                // Professional chart configuration (exactly like Financial Overview)
                double chartWidth = 500;
                double barWidth = 70;
                double barSpacing = 140;
                double maxBarHeight = 200;
                double chartPadding = 60;

                // Professional color scheme (exactly like Financial Overview)
                var data = new[]
                {
                    new { Label = "Total Budget", Value = (double)totalBudget, Color = "#3B82F6", LightColor = "#DBEAFE", DarkColor = "#1E40AF" },
                    new { Label = "Amount Spent", Value = (double)totalSpent, Color = "#4CAF50", LightColor = "#D1FAE5", DarkColor = "#047857" },
                    new { Label = "Remaining", Value = (double)totalRemaining, Color = "#FF9800", LightColor = "#FEF3C7", DarkColor = "#D97706" }
                };

                double maxValue = (double)totalBudget;
                double startX = (chartWidth - (data.Length * barWidth + (data.Length - 1) * (barSpacing - barWidth))) / 2;

                // Add professional grid lines
                for (int i = 0; i <= 5; i++)
                {
                    double gridY = chartPadding + (maxBarHeight / 5) * i;
                    double gridValue = maxValue - (maxValue / 5) * i;

                    // Horizontal grid line
                    var gridLine = new Line
                    {
                        X1 = chartPadding - 20,
                        Y1 = gridY,
                        X2 = chartWidth - chartPadding,
                        Y2 = gridY,
                        Stroke = new SolidColorBrush(Color.FromRgb(226, 232, 240)),
                        StrokeThickness = 1,
                        StrokeDashArray = new DoubleCollection { 4, 4 }
                    };
                    AnalysisModernBarChart.Children.Add(gridLine);

                    // Y-axis labels
                    if (i < 5)
                    {
                        var gridLabel = new TextBlock
                        {
                            Text = ((decimal)gridValue).ToString("C0"),
                            FontSize = 10,
                            Foreground = new SolidColorBrush(Color.FromRgb(100, 116, 139)),
                            FontWeight = FontWeights.Medium
                        };
                        Canvas.SetLeft(gridLabel, 10);
                        Canvas.SetTop(gridLabel, gridY - 8);
                        AnalysisModernBarChart.Children.Add(gridLabel);
                    }
                }

                // Create professional bars
                for (int i = 0; i < data.Length; i++)
                {
                    var item = data[i];
                    double barHeight = (item.Value / maxValue) * maxBarHeight;
                    double x = startX + i * barSpacing;
                    double y = chartPadding + maxBarHeight - barHeight;

                    // Professional gradient
                    var gradientBrush = new LinearGradientBrush();
                    gradientBrush.StartPoint = new Point(0, 0);
                    gradientBrush.EndPoint = new Point(0, 1);
                    gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(item.LightColor), 0.0));
                    gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(item.Color), 0.6));
                    gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(item.DarkColor), 1.0));

                    // Main bar with rounded corners
                    var bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = gradientBrush,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString(item.Color)),
                        StrokeThickness = 2,
                        RadiusX = 8,
                        RadiusY = 8
                    };

                    // Professional shadow
                    bar.Effect = new DropShadowEffect
                    {
                        Color = Color.FromArgb(30, 0, 0, 0),
                        Direction = 315,
                        ShadowDepth = 8,
                        BlurRadius = 15,
                        Opacity = 0.3
                    };

                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y);
                    AnalysisModernBarChart.Children.Add(bar);

                    // Value label with professional styling
                    var valueBg = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(item.Color)),
                        CornerRadius = new CornerRadius(8),
                        Padding = new Thickness(10, 6, 10, 6),
                        Child = new TextBlock
                        {
                            Text = item.Value.ToString("C0"),
                            FontSize = 11,
                            FontWeight = FontWeights.Bold,
                            Foreground = Brushes.White
                        }
                    };

                    Canvas.SetLeft(valueBg, x + barWidth / 2 - 30);
                    Canvas.SetTop(valueBg, y - 35);
                    AnalysisModernBarChart.Children.Add(valueBg);

                    // Category label
                    var textLabel = new TextBlock
                    {
                        Text = item.Label,
                        FontSize = 13,
                        FontWeight = FontWeights.Medium,
                        Foreground = new SolidColorBrush(Color.FromRgb(51, 65, 85)),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        TextWrapping = TextWrapping.Wrap,
                        Width = barWidth + 40
                    };

                    Canvas.SetLeft(textLabel, x - 20);
                    Canvas.SetTop(textLabel, chartPadding + maxBarHeight + 25);
                    AnalysisModernBarChart.Children.Add(textLabel);

                    // Percentage indicator
                    double percentage = (item.Value / maxValue) * 100;
                    var percentLabel = new TextBlock
                    {
                        Text = $"{percentage:F1}%",
                        FontSize = 11,
                        FontWeight = FontWeights.Medium,
                        Foreground = new SolidColorBrush(Color.FromRgb(100, 116, 139)),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(percentLabel, x + barWidth / 2 - 15);
                    Canvas.SetTop(percentLabel, chartPadding + maxBarHeight + 50);
                    AnalysisModernBarChart.Children.Add(percentLabel);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating analysis bar chart: {ex.Message}");
            }
        }

        private void CreateAnalysisDonutChart(decimal totalBudget, decimal totalSpent, decimal totalRemaining)
        {
            try
            {
                if (AnalysisModernPieChart == null) return;
                AnalysisModernPieChart.Children.Clear();

                if (totalBudget <= 0) return;

                var remaining = totalBudget - totalSpent;
                var spentPercent = (double)(totalSpent / totalBudget) * 100;
                var remainingPercent = (double)(remaining / totalBudget) * 100;

                // Center positioning for 380px width canvas
                var center = new Point(190, 160);
                var outerRadius = 85;
                var innerRadius = 50;

                // Create legend on the left (exactly like Financial Overview)
                var legendX = 20;
                var legendY = 60;

                // Spent legend item (Green)
                var spentLegendEllipse = new Ellipse
                {
                    Width = 12,
                    Height = 12,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"))
                };
                Canvas.SetLeft(spentLegendEllipse, legendX);
                Canvas.SetTop(spentLegendEllipse, legendY);
                AnalysisModernPieChart.Children.Add(spentLegendEllipse);

                var spentLegendText = new TextBlock
                {
                    Text = "Spent",
                    FontSize = 12,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                    FontWeight = FontWeights.Medium
                };
                Canvas.SetLeft(spentLegendText, legendX + 20);
                Canvas.SetTop(spentLegendText, legendY - 2);
                AnalysisModernPieChart.Children.Add(spentLegendText);

                // Remaining legend item (Orange)
                var remainingLegendEllipse = new Ellipse
                {
                    Width = 12,
                    Height = 12,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800"))
                };
                Canvas.SetLeft(remainingLegendEllipse, legendX);
                Canvas.SetTop(remainingLegendEllipse, legendY + 25);
                AnalysisModernPieChart.Children.Add(remainingLegendEllipse);

                var remainingLegendText = new TextBlock
                {
                    Text = "Remaining",
                    FontSize = 12,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
                    FontWeight = FontWeights.Medium
                };
                Canvas.SetLeft(remainingLegendText, legendX + 20);
                Canvas.SetTop(remainingLegendText, legendY + 23);
                AnalysisModernPieChart.Children.Add(remainingLegendText);

                // Draw donut segments (exactly like Financial Overview)
                double startAngle = 0;

                // Spent segment (Green)
                if (totalSpent > 0)
                {
                    var spentAngle = (spentPercent / 100) * 360;
                    var spentSegment = CreateProfessionalDonutSegment(center, innerRadius, outerRadius, startAngle, spentAngle, "#4CAF50", "#66BB6A");
                    AnalysisModernPieChart.Children.Add(spentSegment);
                    startAngle += spentAngle;
                }

                // Remaining segment (Orange)
                if (remaining > 0)
                {
                    var remainingAngle = (remainingPercent / 100) * 360;
                    var remainingSegment = CreateProfessionalDonutSegment(center, innerRadius, outerRadius, startAngle, remainingAngle, "#FF9800", "#FFB74D");
                    AnalysisModernPieChart.Children.Add(remainingSegment);
                }

                // Add percentage labels outside the donut (exactly like Financial Overview)
                var labelRadius = outerRadius + 55;

                if (totalSpent > 0)
                {
                    var spentLabelAngle = (spentPercent / 100) * 360 / 2;
                    AddAnalysisDonutLabel(center, labelRadius, spentLabelAngle, $"{spentPercent:F0}%");
                }

                if (remaining > 0)
                {
                    var remainingLabelAngle = ((spentPercent / 100) * 360) + ((remainingPercent / 100) * 360 / 2);
                    AddAnalysisDonutLabel(center, labelRadius, remainingLabelAngle, $"{remainingPercent:F0}%");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating analysis donut chart: {ex.Message}");
            }
        }

        private void AddAnalysisDonutLabel(Point center, double radius, double angle, string text)
        {
            var angleRad = (angle - 90) * Math.PI / 180; // -90 to start from top
            var x = center.X + radius * Math.Cos(angleRad);
            var y = center.Y + radius * Math.Sin(angleRad);

            // Create a border with background for better visibility (exactly like Financial Overview)
            var labelBorder = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333333")),
                CornerRadius = new CornerRadius(3),
                Padding = new Thickness(4, 2, 4, 2)
            };

            var label = new TextBlock
            {
                Text = text,
                FontSize = 11,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFFFFF")),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            labelBorder.Child = label;

            Canvas.SetLeft(labelBorder, x - 18);
            Canvas.SetTop(labelBorder, y - 10);
            AnalysisModernPieChart.Children.Add(labelBorder);
        }





        private System.Windows.Shapes.Path CreateProfessionalDonutSegment(Point center, double innerRadius, double outerRadius, double startAngle, double sweepAngle, string color, string lightColor)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = (startAngle + sweepAngle) * Math.PI / 180;

            // Outer arc points
            var outerStartPoint = new Point(
                center.X + outerRadius * Math.Cos(startAngleRad),
                center.Y + outerRadius * Math.Sin(startAngleRad)
            );
            var outerEndPoint = new Point(
                center.X + outerRadius * Math.Cos(endAngleRad),
                center.Y + outerRadius * Math.Sin(endAngleRad)
            );

            // Inner arc points
            var innerStartPoint = new Point(
                center.X + innerRadius * Math.Cos(startAngleRad),
                center.Y + innerRadius * Math.Sin(startAngleRad)
            );
            var innerEndPoint = new Point(
                center.X + innerRadius * Math.Cos(endAngleRad),
                center.Y + innerRadius * Math.Sin(endAngleRad)
            );

            var isLargeArc = sweepAngle > 180;

            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure { StartPoint = outerStartPoint };

            // Outer arc
            pathFigure.Segments.Add(new ArcSegment
            {
                Point = outerEndPoint,
                Size = new Size(outerRadius, outerRadius),
                IsLargeArc = isLargeArc,
                SweepDirection = SweepDirection.Clockwise
            });

            // Line to inner arc
            pathFigure.Segments.Add(new LineSegment(innerEndPoint, true));

            // Inner arc (reverse direction)
            pathFigure.Segments.Add(new ArcSegment
            {
                Point = innerStartPoint,
                Size = new Size(innerRadius, innerRadius),
                IsLargeArc = isLargeArc,
                SweepDirection = SweepDirection.Counterclockwise
            });

            pathFigure.IsClosed = true;
            pathGeometry.Figures.Add(pathFigure);

            // Professional gradient
            var gradientBrush = new RadialGradientBrush();
            gradientBrush.Center = new Point(0.3, 0.3);
            gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(lightColor), 0.0));
            gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(color), 0.7));
            gradientBrush.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString(color), 1.0));

            var path = new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Fill = gradientBrush,
                Stroke = Brushes.White,
                StrokeThickness = 3
            };

            // Professional shadow effect
            path.Effect = new DropShadowEffect
            {
                Color = Color.FromArgb(40, 0, 0, 0),
                Direction = 315,
                ShadowDepth = 8,
                BlurRadius = 15,
                Opacity = 0.3
            };

            return path;
        }

        private System.Windows.Shapes.Path Create3DPieSlice(Point center, double radius, double startAngle, double endAngle, string color, double opacity, bool isTopLayer)
        {
            var startAngleRad = (startAngle - 90) * Math.PI / 180;
            var endAngleRad = (endAngle - 90) * Math.PI / 180;

            var startPoint = new Point(
                center.X + radius * Math.Cos(startAngleRad),
                center.Y + radius * Math.Sin(startAngleRad)
            );

            var endPoint = new Point(
                center.X + radius * Math.Cos(endAngleRad),
                center.Y + radius * Math.Sin(endAngleRad)
            );

            var isLargeArc = (endAngle - startAngle) > 180;

            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure { StartPoint = center };

            pathFigure.Segments.Add(new LineSegment(startPoint, true));
            pathFigure.Segments.Add(new ArcSegment
            {
                Point = endPoint,
                Size = new Size(radius, radius),
                IsLargeArc = isLargeArc,
                SweepDirection = SweepDirection.Clockwise
            });
            pathFigure.Segments.Add(new LineSegment(center, true));

            pathGeometry.Figures.Add(pathFigure);

            var baseColor = (Color)ColorConverter.ConvertFromString(color);
            Brush fillBrush;

            if (isTopLayer)
            {
                // Add gradient for top layer
                fillBrush = new LinearGradientBrush
                {
                    StartPoint = new Point(0, 0),
                    EndPoint = new Point(1, 1),
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop(Color.FromArgb((byte)(255 * opacity), baseColor.R, baseColor.G, baseColor.B), 0),
                        new GradientStop(Color.FromArgb((byte)(200 * opacity),
                            (byte)Math.Max(0, baseColor.R - 30),
                            (byte)Math.Max(0, baseColor.G - 30),
                            (byte)Math.Max(0, baseColor.B - 30)), 1)
                    }
                };
            }
            else
            {
                fillBrush = new SolidColorBrush(baseColor) { Opacity = opacity };
            }

            var path = new System.Windows.Shapes.Path
            {
                Data = pathGeometry,
                Fill = fillBrush,
                Stroke = isTopLayer ? new SolidColorBrush(Colors.White) : null,
                StrokeThickness = isTopLayer ? 1 : 0
            };

            if (isTopLayer)
            {
                path.Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 315,
                    ShadowDepth = 4,
                    Opacity = 0.3,
                    BlurRadius = 6
                };
            }

            return path;
        }

        private void UpdateAnalysisSummaryCards(decimal totalBudget, decimal totalSpent, decimal totalRemaining)
        {
            try
            {
                // No KPI cards to update anymore - this method can be simplified or removed
                System.Diagnostics.Debug.WriteLine($"Analysis Summary - Budget: {totalBudget:C}, Spent: {totalSpent:C}, Remaining: {totalRemaining:C}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating analysis summary cards: {ex}");
            }
        }

        #endregion

        #region New Analytics Charts

        private void DrawProjectAnalyticsFinancialStyle(List<Project> projects, List<Invoice> invoices)
        {
            try
            {
                // Calculate totals for donut chart
                var totalBudget = projects.Sum(p => p.POAmount);
                var totalSpent = invoices.Sum(i => i.AmountUSD);

                // Create project data for DataGrid (same as Financial Overview style)
                var projectData = projects.Select(p => new
                {
                    Name = p.Name,
                    POAmount = p.POAmount,
                    TotalSpent = invoices.Where(i => i.ProjectId == p.Id).Sum(i => i.AmountUSD),
                    Remaining = p.POAmount - invoices.Where(i => i.ProjectId == p.Id).Sum(i => i.AmountUSD)
                }).ToList();

                // Populate DataGrid
                if (ProjectAnalyticsDataGrid != null)
                {
                    ProjectAnalyticsDataGrid.ItemsSource = projectData;
                }

                // Draw donut chart (exactly like Financial Overview)
                DrawProjectAnalyticsDonutChartFinancialStyle(totalBudget, totalSpent);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing project analytics: {ex.Message}");
            }
        }

        private void DrawProjectAnalyticsDonutChartFinancialStyle(decimal totalBudget, decimal totalSpent)
        {
            try
            {
                if (ProjectAnalyticsChart == null) return;
                ProjectAnalyticsChart.Children.Clear();

                if (totalBudget <= 0) return;

                var remaining = totalBudget - totalSpent;
                var spentPercent = (double)(totalSpent / totalBudget) * 100;
                var remainingPercent = (double)(remaining / totalBudget) * 100;

                // Exact same positioning as Financial Overview
                var center = new Point(280, 160);
                var outerRadius = 70;
                var innerRadius = 45;

                // Create legend on the left (exactly like Financial Overview)
                var legendX = 20;
                var legendY = 60;

                // Spent legend item (Green)
                var spentLegendEllipse = new Ellipse
                {
                    Width = 12,
                    Height = 12,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"))
                };
                Canvas.SetLeft(spentLegendEllipse, legendX);
                Canvas.SetTop(spentLegendEllipse, legendY);
                ProjectAnalyticsChart.Children.Add(spentLegendEllipse);

                var spentLegendText = new TextBlock
                {
                    Text = "Spent",
                    FontSize = 12,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                    FontWeight = FontWeights.Medium
                };
                Canvas.SetLeft(spentLegendText, legendX + 20);
                Canvas.SetTop(spentLegendText, legendY - 2);
                ProjectAnalyticsChart.Children.Add(spentLegendText);

                // Remaining legend item (Orange)
                var remainingLegendEllipse = new Ellipse
                {
                    Width = 12,
                    Height = 12,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800"))
                };
                Canvas.SetLeft(remainingLegendEllipse, legendX);
                Canvas.SetTop(remainingLegendEllipse, legendY + 25);
                ProjectAnalyticsChart.Children.Add(remainingLegendEllipse);

                var remainingLegendText = new TextBlock
                {
                    Text = "Remaining",
                    FontSize = 12,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
                    FontWeight = FontWeights.Medium
                };
                Canvas.SetLeft(remainingLegendText, legendX + 20);
                Canvas.SetTop(remainingLegendText, legendY + 23);
                ProjectAnalyticsChart.Children.Add(remainingLegendText);

                // Draw donut segments (exactly like Financial Overview)
                double startAngle = 0;

                // Spent segment (Green)
                if (totalSpent > 0)
                {
                    var spentAngle = (spentPercent / 100) * 360;
                    var spentSegment = CreateProfessionalDonutSegment(center, innerRadius, outerRadius, startAngle, spentAngle, "#4CAF50", "#66BB6A");
                    ProjectAnalyticsChart.Children.Add(spentSegment);
                    startAngle += spentAngle;
                }

                // Remaining segment (Orange)
                if (remaining > 0)
                {
                    var remainingAngle = (remainingPercent / 100) * 360;
                    var remainingSegment = CreateProfessionalDonutSegment(center, innerRadius, outerRadius, startAngle, remainingAngle, "#FF9800", "#FFB74D");
                    ProjectAnalyticsChart.Children.Add(remainingSegment);
                }

                // Add percentage labels outside the donut (exactly like Financial Overview)
                var labelRadius = outerRadius + 55;

                if (totalSpent > 0)
                {
                    var spentLabelAngle = (spentPercent / 100) * 360 / 2;
                    AddProjectAnalyticsLabel(center, labelRadius, spentLabelAngle, $"{spentPercent:F0}%");
                }

                if (remaining > 0)
                {
                    var remainingLabelAngle = ((spentPercent / 100) * 360) + ((remainingPercent / 100) * 360 / 2);
                    AddProjectAnalyticsLabel(center, labelRadius, remainingLabelAngle, $"{remainingPercent:F0}%");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error drawing project analytics donut chart: {ex.Message}");
            }
        }

        private void AddProjectAnalyticsLabel(Point center, double radius, double angle, string text)
        {
            var angleRad = (angle - 90) * Math.PI / 180; // -90 to start from top
            var x = center.X + radius * Math.Cos(angleRad);
            var y = center.Y + radius * Math.Sin(angleRad);

            // Create a border with background for better visibility (exactly like Financial Overview)
            var labelBorder = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333333")),
                CornerRadius = new CornerRadius(3),
                Padding = new Thickness(4, 2, 4, 2)
            };

            var label = new TextBlock
            {
                Text = text,
                FontSize = 11,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFFFFF")),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            labelBorder.Child = label;

            Canvas.SetLeft(labelBorder, x - 18);
            Canvas.SetTop(labelBorder, y - 10);
            ProjectAnalyticsChart.Children.Add(labelBorder);
        }

        // Old methods removed - using new Project Analytics design

        #endregion

        #region Project Files Methods

        private async void LoadProjectFilesData()
        {
            try
            {
                using var context = new FinancialContext();

                // Load all projects for the ComboBox
                var projects = await context.Projects.ToListAsync();
                ProjectFilesComboBox.ItemsSource = projects;
                ProjectFilesComboBox.DisplayMemberPath = "Name";
                ProjectFilesComboBox.SelectedValuePath = "Id";

                // Clear the files grid initially
                ProjectFilesDataGrid.ItemsSource = null;
                NoFilesPanel.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading project files data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ProjectFilesComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProjectFilesComboBox.SelectedValue is int projectId)
            {
                try
                {
                    using var context = new FinancialContext();

                    // Load files for the selected project
                    var projectFiles = await context.ProjectFiles
                        .Where(pf => pf.ProjectId == projectId && pf.IsActive)
                        .Include(pf => pf.Project)
                        .ToListAsync();

                    if (projectFiles.Any())
                    {
                        // Create file info objects
                        var fileInfos = projectFiles.Select(pf => new
                        {
                            FileName = pf.FileName,
                            ProjectName = pf.Project?.Name ?? "Unknown Project",
                            UploadDate = pf.CreatedDate,
                            FileSizeFormatted = pf.FileSizeFormatted,
                            FilePath = pf.FilePath
                        }).ToList();

                        ProjectFilesDataGrid.ItemsSource = fileInfos;
                        NoFilesPanel.Visibility = Visibility.Collapsed;
                    }
                    else
                    {
                        ProjectFilesDataGrid.ItemsSource = null;
                        NoFilesPanel.Visibility = Visibility.Visible;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading files for project: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void RefreshProjectFiles_Click(object sender, RoutedEventArgs e)
        {
            LoadProjectFilesData();
        }

        private void ProjectFilesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ProjectFilesDataGrid.SelectedItem != null)
            {
                try
                {
                    var selectedFile = ProjectFilesDataGrid.SelectedItem;
                    var filePathProperty = selectedFile.GetType().GetProperty("FilePath");

                    if (filePathProperty != null)
                    {
                        var filePath = filePathProperty.GetValue(selectedFile)?.ToString();

                        if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                        {
                            // Open the file with default application
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = filePath,
                                UseShellExecute = true
                            });
                        }
                        else
                        {
                            MessageBox.Show("File not found or path is invalid.", "File Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening file: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion
    }
}
