﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialTracker.Migrations
{
    public partial class AddSiteToInvoicePaymentTerms : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ProjectSiteId",
                table: "InvoicePaymentTerms",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SiteName",
                table: "InvoicePaymentTerms",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoicePaymentTerms_ProjectSiteId",
                table: "InvoicePaymentTerms",
                column: "ProjectSiteId");

            migrationBuilder.AddForeignKey(
                name: "FK_InvoicePaymentTerms_ProjectSites_ProjectSiteId",
                table: "InvoicePaymentTerms",
                column: "ProjectSiteId",
                principalTable: "ProjectSites",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InvoicePaymentTerms_ProjectSites_ProjectSiteId",
                table: "InvoicePaymentTerms");

            migrationBuilder.DropIndex(
                name: "IX_InvoicePaymentTerms_ProjectSiteId",
                table: "InvoicePaymentTerms");

            migrationBuilder.DropColumn(
                name: "ProjectSiteId",
                table: "InvoicePaymentTerms");

            migrationBuilder.DropColumn(
                name: "SiteName",
                table: "InvoicePaymentTerms");
        }
    }
}
