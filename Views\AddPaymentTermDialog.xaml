<Window x:Class="FinancialTracker.AddPaymentTermDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Add New Payment Term"
        Width="500" Height="400"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="16,12" Margin="0,0,0,20" Background="#4A5568" CornerRadius="4">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,4">
                    <materialDesign:PackIcon Kind="Plus" Width="24" Height="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                    <TextBlock Text="Add New Payment Term" FontSize="18" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock Text="Create a new payment term for this project"
                          FontSize="12" Foreground="#E5E7EB" HorizontalAlignment="Center" Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20">
            <StackPanel>
                <!-- Category Selection -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Category:" FontWeight="Medium" FontSize="12" Margin="0,0,0,6"/>
                    <ComboBox x:Name="CategoryComboBox"
                              materialDesign:HintAssist.Hint="Select category"
                              FontSize="12" Height="40">
                        <ComboBoxItem Content="Equipment" IsSelected="True"/>
                        <ComboBoxItem Content="Services"/>
                        <ComboBoxItem Content="Software Equipment"/>
                        <ComboBoxItem Content="Hardware Equipment"/>
                        <ComboBoxItem Content="Spare Parts"/>
                        <!-- Legacy support -->
                        <ComboBoxItem Content="Tasks"/>
                        <ComboBoxItem Content="Software Tasks"/>
                        <ComboBoxItem Content="Hardware Tasks"/>
                    </ComboBox>
                </StackPanel>

                <!-- Description -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Description:" FontWeight="Medium" FontSize="12" Margin="0,0,0,6"/>
                    <TextBox x:Name="DescriptionTextBox" 
                             materialDesign:HintAssist.Hint="e.g., Upon delivery and installation"
                             FontSize="12" Height="40"
                             TextWrapping="Wrap" AcceptsReturn="True"/>
                </StackPanel>

                <!-- Percentage -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Percentage:" FontWeight="Medium" FontSize="12" Margin="0,0,0,6"/>
                    <TextBox x:Name="PercentageTextBox" 
                             materialDesign:HintAssist.Hint="e.g., 30.00"
                             FontSize="12" Height="40"
                             PreviewTextInput="PercentageTextBox_PreviewTextInput"/>
                </StackPanel>

                <!-- Trigger Condition -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Trigger Condition (Optional):" FontWeight="Medium" FontSize="12" Margin="0,0,0,6"/>
                    <TextBox x:Name="TriggerConditionTextBox" 
                             materialDesign:HintAssist.Hint="e.g., Upon completion of installation and testing"
                             FontSize="12" Height="60"
                             TextWrapping="Wrap" AcceptsReturn="True"/>
                </StackPanel>

                <!-- Site Application Mode -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="Site Application:" FontWeight="Medium" FontSize="12" Margin="0,0,0,6"/>
                    <CheckBox x:Name="IgnoreSiteDivisionCheckBox" 
                              Content="Apply to all sites combined (don't divide by number of sites)"
                              FontSize="11" Margin="0,4,0,0"/>
                    <TextBlock Text="When checked, the percentage will apply to the total project amount regardless of number of sites"
                               FontSize="10" Foreground="#666666" Margin="20,4,0,0" TextWrapping="Wrap"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="CancelButton" Content="Cancel" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100" Height="36" Margin="0,0,12,0"
                    Click="CancelButton_Click"/>
            <Button x:Name="SaveButton" Content="Save" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="100" Height="36"
                    Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
