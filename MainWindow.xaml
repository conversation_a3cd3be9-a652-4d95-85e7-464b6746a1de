<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="#F7F7F7">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Excel-Style DataGrid Styles -->
        <Style x:Key="ExcelHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F3F4F6"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridColumnHeader">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid>
                                <ContentPresenter Margin="{TemplateBinding Padding}"
                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                <Path x:Name="SortArrow" Visibility="Collapsed" Data="M0,0 L1,0 0.5,1 z"
                                      Stretch="Fill" Width="8" Height="6" Fill="#6B7280"
                                      HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="SortDirection" Value="Ascending">
                                <Setter TargetName="SortArrow" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="SortArrow" Property="Data" Value="M0,1 L1,1 0.5,0 z"/>
                            </Trigger>
                            <Trigger Property="SortDirection" Value="Descending">
                                <Setter TargetName="SortArrow" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="SortArrow" Property="Data" Value="M0,0 L1,0 0.5,1 z"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E5E7EB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ExcelRowStyle" TargetType="DataGridRow">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F9FF"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#DBEAFE"/>
                    <Setter Property="BorderBrush" Value="#3B82F6"/>
                </Trigger>
                <MultiTrigger>
                    <MultiTrigger.Conditions>
                        <Condition Property="IsSelected" Value="True"/>
                        <Condition Property="IsMouseOver" Value="True"/>
                    </MultiTrigger.Conditions>
                    <Setter Property="Background" Value="#BFDBFE"/>
                </MultiTrigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ExcelCellStyle" TargetType="DataGridCell">
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter VerticalAlignment="{TemplateBinding VerticalAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="Foreground" Value="#1F2937"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar - SAP Style -->
        <Border Grid.Row="0" Background="#0F2027" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,12,0" Foreground="White"/>
                    <TextBlock Text="Financial Tracker" FontSize="22" FontWeight="SemiBold" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard"
                            Background="Transparent" BorderBrush="White" BorderThickness="1"
                            Foreground="White" Margin="8,0" Padding="16,8" Click="DashboardButton_Click"/>



                    <Button x:Name="ActivitiesButton" Content="🕒 Recent Activities"
                            Background="Transparent" BorderBrush="White" BorderThickness="1"
                            Foreground="White" Margin="8,0" Padding="16,8" Click="ActivitiesButton_Click"/>

                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E1E5E9" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <StackPanel Margin="0,20,0,20">
                        <!-- Sidebar Header -->
                        <Border Background="White" Margin="12,0,12,16" CornerRadius="8" Padding="16,12"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <materialDesign:PackIcon Kind="Menu" Width="20" Height="20"
                                                           Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Navigation" FontSize="16" FontWeight="SemiBold"
                                             Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>
                                <TextBlock Text="Quick access to all features" FontSize="11"
                                         Foreground="#6B7280" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Dashboard Section -->
                        <Border Background="White" Margin="12,0,12,8" CornerRadius="8" Padding="0"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <!-- Section Header -->
                                <Border Background="#6366F1" CornerRadius="8,8,0,0" Padding="16,12">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ViewDashboard" Width="16" Height="16"
                                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Dashboard" FontSize="14" FontWeight="SemiBold"
                                                 Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Dashboard Items -->
                                <StackPanel Margin="8,8,8,12">
                                    <Button x:Name="SidebarOverviewButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarOverview_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ViewDashboard" Width="16" Height="16"
                                                                   Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Financial Overview" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarProjectsTableButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarProjectsTable_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Table" Width="16" Height="16"
                                                                   Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Projects Table" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarAnalyticsButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarAnalytics_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartPie" Width="16" Height="16"
                                                                   Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Charts and Analysis" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Projects Section -->
                        <Border Background="White" Margin="12,0,12,8" CornerRadius="8" Padding="0"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <!-- Section Header -->
                                <Border Background="#10B981" CornerRadius="8,8,0,0" Padding="16,12">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderMultiple" Width="16" Height="16"
                                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Projects" FontSize="14" FontWeight="SemiBold"
                                                 Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Projects Items -->
                                <StackPanel Margin="8,8,8,12">
                                    <Button x:Name="SidebarAddProjectButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarAddProject_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"
                                                                   Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Add New Project" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarProjectsListButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarProjectsList_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FormatListBulleted" Width="16" Height="16"
                                                                   Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="All Projects" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarProjectFilesButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarProjectFiles_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileMultiple" Width="16" Height="16"
                                                                   Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="All Project Files" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Financial Management Section -->
                        <Border Background="White" Margin="12,0,12,8" CornerRadius="8" Padding="0"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <!-- Section Header -->
                                <Border Background="#F59E0B" CornerRadius="8,8,0,0" Padding="16,12">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="16" Height="16"
                                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Financial" FontSize="14" FontWeight="SemiBold"
                                                 Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Financial Items -->
                                <StackPanel Margin="8,8,8,12">
                                    <Button x:Name="SidebarInvoicesButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarInvoices_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Receipt" Width="16" Height="16"
                                                                   Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Invoices" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarCommitmentsButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarCommitments_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Handshake" Width="16" Height="16"
                                                                   Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Commitments" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarPaymentTermsButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarPaymentTerms_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="CalendarClock" Width="16" Height="16"
                                                                   Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Payment Terms" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Reports Section -->
                        <Border Background="White" Margin="12,0,12,8" CornerRadius="8" Padding="0"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <!-- Section Header -->
                                <Border Background="#8B5CF6" CornerRadius="8,8,0,0" Padding="16,12">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileChart" Width="16" Height="16"
                                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Reports" FontSize="14" FontWeight="SemiBold"
                                                 Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Reports Items -->
                                <StackPanel Margin="8,8,8,12">
                                    <Button x:Name="SidebarExportButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarExport_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16"
                                                                   Foreground="#8B5CF6" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Export Data" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarActivitiesButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarActivities_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="History" Width="16" Height="16"
                                                                   Foreground="#8B5CF6" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Recent Activities" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Settings Section -->
                        <Border Background="White" Margin="12,0,12,8" CornerRadius="8" Padding="0"
                                Effect="{StaticResource MaterialDesignShadowDepth1}">
                            <StackPanel>
                                <!-- Section Header -->
                                <Border Background="#6B7280" CornerRadius="8,8,0,0" Padding="16,12">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Settings" Width="16" Height="16"
                                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="Settings" FontSize="14" FontWeight="SemiBold"
                                                 Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Settings Items -->
                                <StackPanel Margin="8,8,8,12">
                                    <Button x:Name="SidebarBackupButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarBackup_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Backup" Width="16" Height="16"
                                                                   Foreground="#6B7280" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Backup Data" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="SidebarRefreshButton" Style="{StaticResource MaterialDesignFlatButton}"
                                            HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"
                                            Padding="12,8" Margin="0,2" Click="SidebarRefresh_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"
                                                                   Foreground="#6B7280" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock Text="Refresh All" FontSize="12" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Main Content Area -->
            <Grid Grid.Column="1">
                <!-- Financial Overview Content -->
                <ScrollViewer x:Name="OverviewScrollViewer" Margin="16,16,16,16" Visibility="Visible" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                    <Grid x:Name="OverviewGrid">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="20,16" Margin="0,0,0,16"
                                Effect="{StaticResource MaterialDesignShadowDepth2}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="ViewDashboard" Width="24" Height="24"
                                                             Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="Financial Overview" FontSize="20" FontWeight="SemiBold"
                                               Foreground="#1F2937" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Border Grid.Column="1" Background="#F0F9FF" CornerRadius="12" Padding="12,6">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,8,0"/>
                                        <TextBlock Text="Live Data" FontSize="11" Foreground="#0284C7" FontWeight="Medium"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- Financial Overview Cards -->
                        <Grid Grid.Row="1" Margin="0,0,0,10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Total Projects Card -->
                                <Border Grid.Row="0" Grid.Column="0" Background="White" CornerRadius="12" Padding="16,12" Margin="0,0,8,8"
                                        Effect="{StaticResource MaterialDesignShadowDepth1}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Row="0" Grid.Column="1" Kind="FolderMultiple" Width="20" Height="20"
                                                                 Foreground="#6366F1" HorizontalAlignment="Right" VerticalAlignment="Top"/>

                                        <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2">
                                            <TextBlock x:Name="OverviewTotalProjectsText" Text="0" FontSize="20" FontWeight="Bold"
                                                     Foreground="#1F2937" Margin="0,0,0,1"/>
                                            <TextBlock Text="Total Projects" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- Total PO Value Card -->
                                <Border Grid.Row="0" Grid.Column="1" Background="White" CornerRadius="12" Padding="16,12" Margin="8,0,0,8"
                                        Effect="{StaticResource MaterialDesignShadowDepth1}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Row="0" Grid.Column="1" Kind="CurrencyUsd" Width="20" Height="20"
                                                                 Foreground="#10B981" HorizontalAlignment="Right" VerticalAlignment="Top"/>

                                        <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2">
                                            <TextBlock x:Name="OverviewTotalPOValueText" Text="$0" FontSize="20" FontWeight="Bold"
                                                     Foreground="#1F2937" Margin="0,0,0,1"/>
                                            <TextBlock Text="Total PO Value" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- Total Spent Card -->
                                <Border Grid.Row="1" Grid.Column="0" Background="White" CornerRadius="12" Padding="16,12" Margin="0,8,8,0"
                                        Effect="{StaticResource MaterialDesignShadowDepth1}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Row="0" Grid.Column="1" Kind="TrendingDown" Width="20" Height="20"
                                                                 Foreground="#EF4444" HorizontalAlignment="Right" VerticalAlignment="Top"/>

                                        <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2">
                                            <TextBlock x:Name="OverviewTotalSpentText" Text="$0" FontSize="20" FontWeight="Bold"
                                                     Foreground="#1F2937" Margin="0,0,0,1"/>
                                            <TextBlock Text="Total Spent" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- Total Remaining Card -->
                                <Border Grid.Row="1" Grid.Column="1" Background="White" CornerRadius="12" Padding="16,12" Margin="8,8,0,0"
                                        Effect="{StaticResource MaterialDesignShadowDepth1}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Row="0" Grid.Column="1" Kind="TrendingUp" Width="20" Height="20"
                                                                 Foreground="#3B82F6" HorizontalAlignment="Right" VerticalAlignment="Top"/>

                                        <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2">
                                            <TextBlock x:Name="OverviewTotalRemainingText" Text="$0" FontSize="20" FontWeight="Bold"
                                                     Foreground="#1F2937" Margin="0,0,0,1"/>
                                            <TextBlock Text="Remaining" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Grid>
                        </Grid>
                    </Grid>
                </ScrollViewer>

                <!-- Projects Table Content -->
                <ScrollViewer x:Name="ProjectsTableScrollViewer" Margin="16,16,16,16" Visibility="Collapsed" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                    <Grid x:Name="ProjectsTableGrid">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="20,16" Margin="0,0,0,16"
                                Effect="{StaticResource MaterialDesignShadowDepth2}">
                            <Grid>
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Table" Width="24" Height="24"
                                                             Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="Projects Table" FontSize="20" FontWeight="SemiBold"
                                               Foreground="#1F2937" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- Projects Table -->
                        <Border Grid.Row="1" Background="White" CornerRadius="6" Padding="0"
                                BorderBrush="#E1E5E9" BorderThickness="1">
                            <DataGrid x:Name="ProjectsTableDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                      HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                      BorderThickness="0" Background="Transparent"
                                      HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto"
                                      AlternatingRowBackground="#F7F9FA" ColumnHeaderHeight="44"
                                      MinHeight="400" MaxHeight="600" CanUserResizeColumns="True">
                                <DataGrid.Resources>
                                    <!-- SAP Style Headers -->
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#F1F5F9"/>
                                        <Setter Property="Foreground" Value="#475569"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="11"/>
                                        <Setter Property="Padding" Value="12,12"/>
                                        <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    </Style>

                                    <!-- SAP Style Rows -->
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Background" Value="White"/>
                                        <Setter Property="BorderBrush" Value="#F1F5F9"/>
                                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                        <Setter Property="Height" Value="48"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8FAFC"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#EEF2FF"/>
                                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.Resources>
                            </DataGrid>
                        </Border>
                    </Grid>
                </ScrollViewer>

                <!-- Dashboard Content (Original) -->
                <ScrollViewer x:Name="DashboardScrollViewer" Margin="16,16,16,16" Visibility="Collapsed" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardGrid" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>



                    <!-- Compact Header -->
                    <Border Grid.Row="1" Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Padding="20,16" Margin="0,0,0,16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="24" Height="24" Foreground="#6366F1" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <TextBlock Text="Financial Dashboard" FontSize="20" FontWeight="SemiBold" Foreground="#374151" VerticalAlignment="Center"/>

                                <!-- Analysis Toggle Button -->
                                <Button x:Name="ToggleAnalysisButton" Content="📊 Show Analysis"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="20,0,0,0" Padding="12,6" FontSize="12"
                                        Background="#E3F2FD" BorderBrush="#2196F3" Foreground="#1976D2"
                                        Click="ToggleAnalysisButton_Click"/>
                            </StackPanel>

                            <Border Grid.Column="1" Background="#F0F9FF" CornerRadius="12" Padding="12,6">
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,8,0"/>
                                    <TextBlock Text="Live Data" FontSize="11" Foreground="#0284C7" FontWeight="Medium"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Ultra Compact Financial Overview Cards -->
                    <Grid Grid.Row="2" Margin="0,0,0,10">
                        <!-- Cards Grid - 2x2 Layout for maximum space utilization -->
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Projects Card -->
                            <Border Grid.Row="0" Grid.Column="0" Background="White" CornerRadius="8" Padding="12,10" Margin="0,0,6,6"
                                    Effect="{StaticResource MaterialDesignShadowDepth1}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0" Margin="0,0,0,6">
                                        <materialDesign:PackIcon Kind="FolderMultiple" Width="16" Height="16"
                                                               Foreground="#6366F1" HorizontalAlignment="Left" VerticalAlignment="Center"/>
                                        <Border Background="#F0F9FF" CornerRadius="4" Padding="4,2" HorizontalAlignment="Right">
                                            <TextBlock Text="Active" FontSize="8" Foreground="#0284C7" FontWeight="Medium"/>
                                        </Border>
                                    </Grid>

                                    <StackPanel Grid.Row="1">
                                        <TextBlock x:Name="TotalProjectsText" Text="0" FontSize="20" FontWeight="Bold"
                                                 Foreground="#1F2937" Margin="0,0,0,1"/>
                                        <TextBlock Text="Projects" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Budget Card -->
                            <Border Grid.Row="0" Grid.Column="1" Background="White" CornerRadius="8" Padding="12,10" Margin="6,0,0,6"
                                    Effect="{StaticResource MaterialDesignShadowDepth1}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0" Margin="0,0,0,6">
                                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="16" Height="16"
                                                               Foreground="#10B981" HorizontalAlignment="Left" VerticalAlignment="Center"/>
                                        <Border Background="#ECFDF5" CornerRadius="4" Padding="4,2" HorizontalAlignment="Right">
                                            <TextBlock Text="Budget" FontSize="8" Foreground="#059669" FontWeight="Medium"/>
                                        </Border>
                                    </Grid>

                                    <StackPanel Grid.Row="1">
                                        <TextBlock x:Name="TotalPOValueText" Text="$0" FontSize="20" FontWeight="Bold"
                                                 Foreground="#1F2937" Margin="0,0,0,1"/>
                                        <TextBlock Text="Total Budget" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Spent Card -->
                            <Border Grid.Row="1" Grid.Column="0" Background="White" CornerRadius="8" Padding="12,10" Margin="0,6,6,0"
                                    Effect="{StaticResource MaterialDesignShadowDepth1}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0" Margin="0,0,0,6">
                                        <materialDesign:PackIcon Kind="TrendingUp" Width="16" Height="16"
                                                               Foreground="#F59E0B" HorizontalAlignment="Left" VerticalAlignment="Center"/>
                                        <Border Background="#FFFBEB" CornerRadius="4" Padding="4,2" HorizontalAlignment="Right">
                                            <TextBlock Text="Spent" FontSize="8" Foreground="#D97706" FontWeight="Medium"/>
                                        </Border>
                                    </Grid>

                                    <StackPanel Grid.Row="1">
                                        <TextBlock x:Name="TotalSpentText" Text="$0" FontSize="20" FontWeight="Bold"
                                                 Foreground="#1F2937" Margin="0,0,0,1"/>
                                        <TextBlock Text="Expenses" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Remaining Card -->
                            <Border Grid.Row="1" Grid.Column="1" Background="White" CornerRadius="8" Padding="12,10" Margin="6,6,0,0"
                                    Effect="{StaticResource MaterialDesignShadowDepth1}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <Grid Grid.Row="0" Margin="0,0,0,6">
                                        <materialDesign:PackIcon Kind="TrendingDown" Width="16" Height="16"
                                                               Foreground="#8B5CF6" HorizontalAlignment="Left" VerticalAlignment="Center"/>
                                        <Border Background="#F5F3FF" CornerRadius="4" Padding="4,2" HorizontalAlignment="Right">
                                            <TextBlock Text="Available" FontSize="8" Foreground="#7C3AED" FontWeight="Medium"/>
                                        </Border>
                                    </Grid>

                                    <StackPanel Grid.Row="1">
                                        <TextBlock x:Name="TotalRemainingText" Text="$0" FontSize="20" FontWeight="Bold"
                                                 Foreground="#1F2937" Margin="0,0,0,1"/>
                                        <TextBlock Text="Remaining" FontSize="10" Foreground="#6B7280" FontWeight="Medium"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>

                    <!-- Professional Financial Overview Section -->
                    <Border x:Name="AnalysisSection" Grid.Row="3" Background="White" CornerRadius="16" Padding="0"
                            Effect="{StaticResource MaterialDesignShadowDepth3}" Margin="0,0,0,20"
                            Visibility="Collapsed">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Professional Header - Financial Overview Style -->
                            <Border Grid.Row="0" CornerRadius="16,16,0,0" Padding="32,24">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#3B82F6" Offset="0"/>
                                        <GradientStop Color="#1E40AF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="White" CornerRadius="12" Padding="12" Margin="0,0,16,0">
                                        <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" Foreground="#3B82F6"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="Financial Overview" FontSize="24" FontWeight="Bold" Foreground="White"/>
                                        <TextBlock Text="Real-time budget analysis and spending insights" FontSize="14" Foreground="#E3F2FD" Margin="0,4,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>



                            <!-- Charts Section - Financial Overview Style -->
                            <Border Grid.Row="1" Background="White" CornerRadius="0,0,16,16" Padding="25">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Budget vs Spending Analysis Section -->
                                    <Border Grid.Column="0" Background="#FAFBFC" CornerRadius="16" Padding="24" Margin="0,0,16,0"
                                            BorderBrush="#E2E8F0" BorderThickness="1">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                                <materialDesign:PackIcon Kind="ChartBar" Width="20" Height="20"
                                                                       Foreground="#475569" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                                <TextBlock Text="Budget vs Spending Analysis" FontSize="18" FontWeight="SemiBold" Foreground="#334155"/>
                                            </StackPanel>
                                            <Canvas x:Name="AnalysisModernBarChart" Width="500" Height="320" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Distribution Section -->
                                    <Border Grid.Column="1" Background="#FAFBFC" CornerRadius="16" Padding="24" Margin="16,0,0,0"
                                            BorderBrush="#E2E8F0" BorderThickness="1">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                                <materialDesign:PackIcon Kind="ChartDonut" Width="20" Height="20"
                                                                       Foreground="#475569" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                                <TextBlock Text="Distribution" FontSize="18" FontWeight="SemiBold" Foreground="#334155"/>
                                            </StackPanel>
                                            <Canvas x:Name="AnalysisModernPieChart" Width="380" Height="320" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>


                                </Grid>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Ultra Compact Projects Management Section -->
                    <Border Grid.Row="4" Background="White" CornerRadius="8" Padding="12,10"
                            Effect="{StaticResource MaterialDesignShadowDepth1}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Ultra Compact Header - SAP Style -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,12">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="18" Height="18"
                                                       Foreground="#0070F2" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="Projects Overview" FontSize="18" FontWeight="SemiBold"
                                         Foreground="#32363A" VerticalAlignment="Center"/>
                                <Border Background="#E8F4FD" CornerRadius="4" Padding="8,4" Margin="16,0,0,0" BorderBrush="#0070F2" BorderThickness="1">
                                    <TextBlock Text="Live Data" FontSize="10" Foreground="#0070F2" FontWeight="Medium"/>
                                </Border>

                                <!-- Add Project Button -->
                                <Button x:Name="AddProjectButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                        Background="#0070F2" Foreground="White" Margin="20,0,0,0" Padding="12,6"
                                        Click="AddProjectButton_Click" materialDesign:ButtonAssist.CornerRadius="4">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="14" Height="14" Margin="0,0,6,0"/>
                                        <TextBlock Text="New Project" FontSize="11" FontWeight="Medium"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <!-- Ultra Compact Search Section - SAP Style -->
                            <Border Grid.Row="1" Background="#F7F9FA" CornerRadius="4" Padding="12,10" Margin="0,0,0,12"
                                    BorderBrush="#E1E5E9" BorderThickness="1">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Compact Search Box - SAP Style -->
                                    <Border Grid.Column="0" Background="White" CornerRadius="4" Padding="10,8"
                                            BorderBrush="#E1E5E9" BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="16" Height="16"
                                                                   Foreground="#6A6D70" VerticalAlignment="Center" Margin="0,0,8,0"/>

                                            <TextBox Grid.Column="1" x:Name="SearchTextBox"
                                                   materialDesign:HintAssist.Hint="Search projects..."
                                                   Style="{StaticResource MaterialDesignTextBox}"
                                                   FontSize="12" BorderThickness="0" Background="Transparent"
                                                   Foreground="#32363A" TextChanged="SearchTextBox_TextChanged"/>
                                        </Grid>
                                    </Border>

                                    <!-- Compact Action Buttons - SAP Style -->
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,0">
                                        <Button x:Name="ClearFiltersButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Margin="0,0,8,0" Padding="8,6" FontSize="11" FontWeight="Medium"
                                                BorderBrush="#0070F2" Foreground="#0070F2"
                                                Click="ClearFiltersButton_Click" ToolTip="Clear Filters">
                                            <materialDesign:PackIcon Kind="FilterRemove" Width="14" Height="14"/>
                                        </Button>

                                        <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                                Background="#0070F2" Padding="8,6" FontSize="11" FontWeight="Medium"
                                                ToolTip="Refresh Data" Click="RefreshButton_Click">
                                            <materialDesign:PackIcon Kind="Refresh" Width="14" Height="14"/>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- Ultra Compact Projects Table - SAP Style -->
                            <Border Grid.Row="2" Background="White" CornerRadius="6" Padding="0"
                                    BorderBrush="#E1E5E9" BorderThickness="1">
                                <DataGrid x:Name="ProjectsSummaryDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                          IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                          HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                          BorderThickness="0" Background="Transparent"
                                          HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto"
                                          AlternatingRowBackground="#F7F9FA" ColumnHeaderHeight="44"
                                          MinHeight="300" MaxHeight="500" CanUserResizeColumns="True">
                                    <DataGrid.Resources>
                                        <!-- SAP Style Headers -->
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#0070F2"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                            <Setter Property="BorderBrush" Value="#005BB5"/>
                                            <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                            <Setter Property="Padding" Value="8,8"/>
                                            <Setter Property="Height" Value="44"/>
                                        </Style>
                                        <!-- SAP Style Cells -->
                                        <Style TargetType="DataGridCell">
                                            <Setter Property="BorderBrush" Value="#E1E5E9"/>
                                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                            <Setter Property="Padding" Value="8,6"/>
                                            <Setter Property="FontSize" Value="11"/>
                                            <Setter Property="Foreground" Value="#32363A"/>
                                            <Setter Property="Height" Value="38"/>
                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#E8F4FD"/>
                                                    <Setter Property="Foreground" Value="#0070F2"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                        <!-- SAP Style Rows -->
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="Height" Value="38"/>
                                            <Setter Property="FontSize" Value="11"/>
                                            <Style.Triggers>
                                                <!-- Active Projects - SAP Green Background -->
                                                <DataTrigger Binding="{Binding Status}" Value="Active">
                                                    <Setter Property="Background" Value="#E8F5E8"/>
                                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                                </DataTrigger>
                                                <!-- On Hold Projects - SAP Gray Background -->
                                                <DataTrigger Binding="{Binding Status}" Value="On Hold">
                                                    <Setter Property="Background" Value="#F5F5F5"/>
                                                    <Setter Property="BorderBrush" Value="#E0E0E0"/>
                                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                                </DataTrigger>
                                                <!-- Completed Projects - SAP Blue Background -->
                                                <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                    <Setter Property="Background" Value="#E8F4FD"/>
                                                    <Setter Property="BorderBrush" Value="#B3D9F2"/>
                                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                                </DataTrigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#F0F2F5"/>
                                                </Trigger>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#E8F4FD"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <!-- Project Name Column - Clickable for details -->
                                    <DataGridTemplateColumn Header="Project" Width="120" MinWidth="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Name}"
                                                         HorizontalAlignment="Center" VerticalAlignment="Center"
                                                         FontWeight="Bold" FontSize="14"
                                                         Foreground="#6366F1" Margin="4,0"
                                                         Cursor="Hand" TextDecorations="Underline"
                                                         MouseLeftButtonDown="ProjectName_Click"
                                                         ToolTip="Click to view project details"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <!-- Total Column - SAP Style -->
                                    <DataGridTextColumn Header="Total" Binding="{Binding POAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <!-- Total Spent Column - SAP Style -->
                                    <DataGridTextColumn Header="Total Spent" Binding="{Binding TotalSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Total Remaining Column - SAP Style -->
                                    <DataGridTextColumn Header="Remaining" Binding="{Binding PORemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="105">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Equipment Column - SAP Style -->
                                    <DataGridTextColumn Header="Equipment" Binding="{Binding TaskCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="115">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Equipment Spent Column - SAP Style -->
                                    <DataGridTextColumn Header="Equip. Spent" Binding="{Binding TasksSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Equipment Remaining Column - SAP Style -->
                                    <DataGridTextColumn Header="Equip. Remaining" Binding="{Binding TasksRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Column - SAP Style -->
                                    <DataGridTextColumn Header="Services" Binding="{Binding ServiceCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Spent Column - SAP Style -->
                                    <DataGridTextColumn Header="Serv. Spent" Binding="{Binding ServicesSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="105">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Remaining Column - SAP Style -->
                                    <DataGridTextColumn Header="Serv. Remaining" Binding="{Binding ServicesRemaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="115">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Margin" Value="8,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Actions Column - SAP Style -->
                                    <DataGridTemplateColumn Header="Actions" Width="130" MinWidth="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="1">
                                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="1" Padding="4,3" FontSize="9" Click="AnalyzeProjectButton_Click"
                                                            BorderBrush="#2196F3" Foreground="#2196F3" ToolTip="Show Project Analysis" Width="28" Height="22"
                                                            Tag="{Binding}">
                                                        <materialDesign:PackIcon Kind="ChartLine" Width="10" Height="10"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="1" Padding="4,3" FontSize="9" Click="EditProjectButton_Click"
                                                            BorderBrush="#FF9800" Foreground="#FF9800" ToolTip="Edit Project" Width="28" Height="22">
                                                        <materialDesign:PackIcon Kind="Pencil" Width="10" Height="10"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="1" Padding="4,3" FontSize="9" Click="DeleteProjectButton_Click"
                                                            BorderBrush="#F44336" Foreground="#F44336" ToolTip="Delete Project" Width="28" Height="22">
                                                        <materialDesign:PackIcon Kind="Delete" Width="10" Height="10"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Border>

                            <!-- Project Analytics Section -->
                            <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="8" Padding="16" Margin="0,16,0,0"
                                    BorderBrush="#E1E5E9" BorderThickness="1">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- Analytics Header -->
                                    <Grid Grid.Row="0" Margin="0,0,0,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24"
                                                                   Foreground="#673AB7" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                            <TextBlock Text="📊 Project Analytics" FontSize="20" FontWeight="SemiBold"
                                                     Foreground="#32363A" VerticalAlignment="Center"/>
                                            <Border Background="#E8F4FD" CornerRadius="4" Padding="6,3" Margin="16,0,0,0" BorderBrush="#673AB7" BorderThickness="1">
                                                <TextBlock Text="Real-time Data" FontSize="9" Foreground="#673AB7" FontWeight="Medium"/>
                                            </Border>
                                        </StackPanel>

                                        <Button Grid.Column="1" Content="🗑️ Clear Analytics"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                BorderBrush="#F44336" Foreground="#F44336"
                                                Padding="10,5" FontSize="10" Margin="20,0,0,0"
                                                Click="ClearAnalytics_Click"
                                                ToolTip="Clear all project analytics from display"/>
                                    </Grid>

                                    <!-- Projects Analytics Container -->
                                    <StackPanel Grid.Row="1" x:Name="ProjectAnalyticsContainer">
                                        <!-- Project analytics will be dynamically added here -->
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
            </ScrollViewer>

            <!-- Analytics Content - Full Width -->
            <ScrollViewer x:Name="AnalyticsScrollViewer" Margin="16,16,16,16" Visibility="Collapsed" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                <Grid x:Name="AnalyticsGrid">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Analytics Header - Simple -->
                    <Border Grid.Row="0" Background="#0070F2" CornerRadius="8" Padding="24,20" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Analytics" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="📊 Project Analytics Dashboard" FontSize="24" FontWeight="Bold" Foreground="White"/>
                                    <TextBlock Text="Real-time insights and performance metrics" FontSize="14" Foreground="#E3F2FD" Margin="0,4,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <Button Grid.Column="1" x:Name="RefreshAnalyticsButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="White" Foreground="#0070F2" Content="🔄 Refresh" Padding="16,8"
                                    Click="RefreshAnalyticsButton_Click"/>
                        </Grid>
                    </Border>

                    <!-- Project Analytics - Financial Overview Design -->
                    <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="25" Margin="0,0,0,20"
                            BorderBrush="#E1E5E9" BorderThickness="1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Charts Section -->
                            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="ChartPie" Width="24" Height="24" Foreground="#673AB7" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="📊 Distribution" FontSize="16" FontWeight="SemiBold" Foreground="#32363A"/>
                                </StackPanel>

                                <!-- Chart Container -->
                                <Grid Height="200" Width="400">
                                    <Canvas x:Name="ProjectAnalyticsChart" Height="200" Width="400"/>
                                </Grid>
                            </StackPanel>

                            <!-- Data Table Section -->
                            <StackPanel Grid.Column="1" Margin="20,0,0,0">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="TableLarge" Width="24" Height="24" Foreground="#673AB7" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="📋 Projects Data" FontSize="16" FontWeight="SemiBold" Foreground="#32363A"/>
                                </StackPanel>

                                <!-- Projects DataGrid -->
                                <DataGrid x:Name="ProjectAnalyticsDataGrid"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          CanUserReorderColumns="False"
                                          CanUserResizeRows="False"
                                          CanUserSortColumns="True"
                                          GridLinesVisibility="Horizontal"
                                          HeadersVisibility="Column"
                                          SelectionMode="Single"
                                          Background="White"
                                          BorderThickness="1"
                                          BorderBrush="#E1E5E9"
                                          RowBackground="White"
                                          AlternatingRowBackground="#F8F9FA"
                                          FontSize="12"
                                          Height="150">

                                    <DataGrid.Columns>
                                        <!-- Project Name -->
                                        <DataGridTextColumn Header="Project" Binding="{Binding Name}" Width="*" MinWidth="100">
                                            <DataGridTextColumn.HeaderStyle>
                                                <Style TargetType="DataGridColumnHeader">
                                                    <Setter Property="Background" Value="#F1F5F9"/>
                                                    <Setter Property="Foreground" Value="#475569"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Padding" Value="12,8"/>
                                                    <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                                </Style>
                                            </DataGridTextColumn.HeaderStyle>
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Padding" Value="12,6"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="Medium"/>
                                                    <Setter Property="Foreground" Value="#1E293B"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Total Budget -->
                                        <DataGridTextColumn Header="Total" Binding="{Binding POAmount, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                            <DataGridTextColumn.HeaderStyle>
                                                <Style TargetType="DataGridColumnHeader">
                                                    <Setter Property="Background" Value="#F1F5F9"/>
                                                    <Setter Property="Foreground" Value="#475569"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Padding" Value="12,8"/>
                                                    <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                                </Style>
                                            </DataGridTextColumn.HeaderStyle>
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Padding" Value="12,6"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#059669"/>
                                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Total Spent -->
                                        <DataGridTextColumn Header="Spent" Binding="{Binding TotalSpent, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                            <DataGridTextColumn.HeaderStyle>
                                                <Style TargetType="DataGridColumnHeader">
                                                    <Setter Property="Background" Value="#F1F5F9"/>
                                                    <Setter Property="Foreground" Value="#475569"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Padding" Value="12,8"/>
                                                    <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                                </Style>
                                            </DataGridTextColumn.HeaderStyle>
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Padding" Value="12,6"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#DC2626"/>
                                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Remaining -->
                                        <DataGridTextColumn Header="Remaining" Binding="{Binding Remaining, StringFormat='{}{0:C0}'}" Width="*" MinWidth="120">
                                            <DataGridTextColumn.HeaderStyle>
                                                <Style TargetType="DataGridColumnHeader">
                                                    <Setter Property="Background" Value="#F1F5F9"/>
                                                    <Setter Property="Foreground" Value="#475569"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Padding" Value="12,8"/>
                                                    <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                                </Style>
                                            </DataGridTextColumn.HeaderStyle>
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Padding" Value="12,6"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#D97706"/>
                                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Grid>
                    </Border>


                </Grid>
            </ScrollViewer>

            <!-- Project Files Content -->
            <ScrollViewer x:Name="ProjectFilesScrollViewer" Margin="16,16,16,16" Visibility="Collapsed" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                <Grid x:Name="ProjectFilesGrid">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="20,16" Margin="0,0,0,16"
                            Effect="{StaticResource MaterialDesignShadowDepth2}">
                        <Grid>
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" Width="24" Height="24"
                                                         Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <TextBlock Text="All Project Files" FontSize="20" FontWeight="SemiBold"
                                           Foreground="#1F2937" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Project Selection -->
                    <Border Grid.Row="1" Background="White" CornerRadius="12" Padding="20,16" Margin="0,0,0,16"
                            Effect="{StaticResource MaterialDesignShadowDepth1}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="Select Project:" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" VerticalAlignment="Center" Margin="0,0,16,0"/>

                            <ComboBox x:Name="ProjectFilesComboBox" Grid.Column="1"
                                      materialDesign:HintAssist.Hint="Choose a project to view its files"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      FontSize="14" Height="48" VerticalContentAlignment="Center"
                                      SelectionChanged="ProjectFilesComboBox_SelectionChanged"/>

                            <Button Grid.Column="2" Content="🔄 Refresh" Background="#10B981" Foreground="White"
                                    BorderThickness="0" Padding="16,8" Margin="16,0,0,0"
                                    FontSize="12" Click="RefreshProjectFiles_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#059669"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- Files List -->
                    <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="20"
                            Effect="{StaticResource MaterialDesignShadowDepth1}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Files Header -->
                            <TextBlock Grid.Row="0" Text="Project Files" FontSize="16" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,16"/>

                            <!-- Files DataGrid -->
                            <DataGrid Grid.Row="1" x:Name="ProjectFilesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                                      HorizontalContentAlignment="Left" VerticalContentAlignment="Center"
                                      BorderThickness="0" Background="Transparent"
                                      HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto"
                                      AlternatingRowBackground="#F7F9FA" ColumnHeaderHeight="44"
                                      MinHeight="300" CanUserResizeColumns="True"
                                      MouseDoubleClick="ProjectFilesDataGrid_MouseDoubleClick">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#F1F5F9"/>
                                        <Setter Property="Foreground" Value="#475569"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="11"/>
                                        <Setter Property="Padding" Value="12,12"/>
                                        <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                    </Style>

                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Background" Value="White"/>
                                        <Setter Property="BorderBrush" Value="#F1F5F9"/>
                                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                        <Setter Property="Height" Value="48"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8FAFC"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#EEF2FF"/>
                                                <Setter Property="BorderBrush" Value="#10B981"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.Resources>

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="📁 File Name" Binding="{Binding FileName}" Width="*" MinWidth="200">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="12,6"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Medium"/>
                                                <Setter Property="Foreground" Value="#374151"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="📂 Project" Binding="{Binding ProjectName}" Width="150" MinWidth="120">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="12,6"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#6B7280"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="📅 Upload Date" Binding="{Binding UploadDate, StringFormat=dd/MM/yyyy}" Width="120" MinWidth="100">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="12,6"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#6B7280"/>
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="📏 File Size" Binding="{Binding FileSizeFormatted}" Width="100" MinWidth="80">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Padding" Value="12,6"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#6B7280"/>
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- Empty State -->
                            <StackPanel Grid.Row="1" x:Name="NoFilesPanel" Visibility="Collapsed"
                                        HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,50,0,0">
                                <materialDesign:PackIcon Kind="FileOutline" Width="64" Height="64"
                                                         Foreground="#E0E0E0" HorizontalAlignment="Center"/>
                                <TextBlock Text="No files found for selected project" FontSize="16"
                                           Foreground="#999" HorizontalAlignment="Center" Margin="0,16,0,8"/>
                                <TextBlock Text="Upload files to this project to see them here"
                                           FontSize="12" Foreground="#CCC" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </ScrollViewer>

            <!-- Recent Activities Content - Full Width -->
            <ScrollViewer x:Name="ActivitiesScrollViewer" Margin="0" Visibility="Collapsed"
                          VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" CanContentScroll="False">
                <Grid x:Name="ActivitiesGrid">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Activities Header -->
                    <Border Grid.Row="0" Background="#673AB7" CornerRadius="0" Padding="32,24" Margin="0,0,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="History" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="🕒 Recent Activities Log" FontSize="24" FontWeight="Bold" Foreground="White"/>
                                    <TextBlock Text="Track all your recent actions and changes" FontSize="14" Foreground="#E1BEE7" Margin="0,4,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <Button Grid.Column="1" x:Name="RefreshActivitiesButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="White" Foreground="#673AB7" Content="🔄 Refresh" Padding="16,8"
                                    Click="RefreshActivitiesButton_Click"/>
                        </Grid>
                    </Border>

                    <!-- Activities Content -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Filter Section -->
                        <Border Grid.Row="0" Background="White" CornerRadius="0" Padding="32,20" Margin="0,0,0,0"
                                BorderBrush="#E1E5E9" BorderThickness="0,0,0,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0" Kind="Filter" Width="20" Height="20"
                                                         Foreground="#673AB7" VerticalAlignment="Center" Margin="0,0,12,0"/>

                                <ComboBox Grid.Column="1" x:Name="ActivityTypeFilter" materialDesign:HintAssist.Hint="Filter by Type"
                                          Margin="0,0,20,0" SelectionChanged="ActivityTypeFilter_SelectionChanged">
                                    <ComboBoxItem Content="All Activities" IsSelected="True"/>
                                    <ComboBoxItem Content="Projects"/>
                                    <ComboBoxItem Content="Invoices"/>
                                    <ComboBoxItem Content="Commitments"/>
                                    <ComboBoxItem Content="Edits"/>
                                </ComboBox>

                                <TextBlock Grid.Column="2" Text="Date Range:" VerticalAlignment="Center"
                                           Foreground="#673AB7" FontWeight="SemiBold" Margin="0,0,12,0"/>

                                <ComboBox Grid.Column="3" x:Name="DateRangeFilter" materialDesign:HintAssist.Hint="Select Period"
                                          Margin="0,0,20,0" SelectionChanged="DateRangeFilter_SelectionChanged">
                                    <ComboBoxItem Content="Today" IsSelected="True"/>
                                    <ComboBoxItem Content="Last 7 Days"/>
                                    <ComboBoxItem Content="Last 30 Days"/>
                                    <ComboBoxItem Content="Last 3 Months"/>
                                    <ComboBoxItem Content="All Time"/>
                                </ComboBox>

                                <Button Grid.Column="4" x:Name="ClearActivityFiltersButton" Content="Clear Filters"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        BorderBrush="#673AB7" Foreground="#673AB7" Padding="12,6"
                                        Click="ClearActivityFiltersButton_Click"/>
                            </Grid>
                        </Border>

                        <!-- Activities List -->
                        <Border Grid.Row="1" Background="White" CornerRadius="0" Padding="32,24"
                                BorderBrush="#E1E5E9" BorderThickness="0">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Excel-Style Toolbar -->
                                <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" Padding="16,12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Left Side - Title and Count -->
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                            <TextBlock Text="📊 Activity Log" FontSize="16" FontWeight="SemiBold"
                                                       Foreground="#111827" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="ActivityCountText" Text="(0 records)" FontSize="12"
                                                       Foreground="#6B7280" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Right Side - Excel-Style Buttons -->
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                            <Button Content="📤 Export" Background="White" Foreground="#374151"
                                                    BorderBrush="#D1D5DB" BorderThickness="1" Padding="12,6"
                                                    FontSize="11" Margin="0,0,8,0"
                                                    Click="ExportActivities_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#F3F4F6"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Button Content="🔄 Refresh" Background="White" Foreground="#374151"
                                                    BorderBrush="#D1D5DB" BorderThickness="1" Padding="12,6"
                                                    FontSize="11" Margin="0,0,8,0"
                                                    Click="RefreshActivities_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#F3F4F6"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Button Content="🗑️ Clear" Background="White" Foreground="#DC2626"
                                                    BorderBrush="#FCA5A5" BorderThickness="1" Padding="12,6"
                                                    FontSize="11"
                                                    Click="ClearActivities_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#FEF2F2"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Button Content="🧪 Test Classification" Background="White" Foreground="#7C3AED"
                                                    BorderBrush="#C4B5FD" BorderThickness="1" Padding="12,6"
                                                    FontSize="11" Margin="10,0,0,0"
                                                    Click="TestInvoiceClassification_Click"
                                                    ToolTip="Test how invoices will be classified in spending calculations">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#FAF5FF"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>


                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- Activities DataGrid - Excel Style -->
                                <DataGrid Grid.Row="1" x:Name="ActivitiesDataGrid" AutoGenerateColumns="False"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="True"
                                          CanUserResizeColumns="True" CanUserSortColumns="True"
                                          GridLinesVisibility="All" HeadersVisibility="Column"
                                          RowHeight="45" FontSize="11" Background="White"
                                          BorderBrush="#D1D5DB" BorderThickness="1"
                                          MouseDoubleClick="ActivitiesDataGrid_MouseDoubleClick"
                                          AlternatingRowBackground="#F9FAFB"
                                          MinHeight="500" MaxHeight="800"
                                          SelectionMode="Extended" SelectionUnit="FullRow"
                                          ColumnHeaderStyle="{DynamicResource ExcelHeaderStyle}"
                                          RowStyle="{DynamicResource ExcelRowStyle}"
                                          CellStyle="{DynamicResource ExcelCellStyle}">



                                    <DataGrid.Columns>
                                        <!-- Row Number - Excel Style -->
                                        <DataGridTextColumn Header="№" Binding="{Binding FormattedSequence}"
                                                            Width="60" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Foreground" Value="#6B7280"/>
                                                    <Setter Property="Background" Value="#F9FAFB"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Type - Excel Style -->
                                        <DataGridTemplateColumn Header="Category" Width="100" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <Border Background="{Binding IconColor}" CornerRadius="4" Padding="8,4"
                                                            HorizontalAlignment="Center">
                                                        <TextBlock Text="{Binding Type}" FontSize="10" FontWeight="SemiBold"
                                                                   Foreground="White" HorizontalAlignment="Center"/>
                                                    </Border>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>

                                        <!-- Action - Excel Style -->
                                        <DataGridTextColumn Header="Action" Binding="{Binding Action}"
                                                            Width="90" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="FontWeight" Value="Medium"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Foreground" Value="#374151"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Title - Main Content -->
                                        <DataGridTextColumn Header="Title" Binding="{Binding Title}"
                                                            Width="*" MinWidth="250" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontWeight" Value="Medium"/>
                                                    <Setter Property="FontSize" Value="11"/>
                                                    <Setter Property="Foreground" Value="#111827"/>
                                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Description -->
                                        <DataGridTextColumn Header="Description" Binding="{Binding Description}"
                                                            Width="200" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="FontSize" Value="10"/>
                                                    <Setter Property="Foreground" Value="#6B7280"/>
                                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Location -->
                                        <DataGridTextColumn Header="Location" Binding="{Binding Location}"
                                                            Width="120" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="FontSize" Value="10"/>
                                                    <Setter Property="Foreground" Value="#6B7280"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Date -->
                                        <DataGridTextColumn Header="Date" Binding="{Binding FormattedDate}"
                                                            Width="100" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="FontSize" Value="10"/>
                                                    <Setter Property="Foreground" Value="#374151"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Time -->
                                        <DataGridTextColumn Header="Time" Binding="{Binding FormattedTime}"
                                                            Width="80" IsReadOnly="True">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                                    <Setter Property="FontSize" Value="10"/>
                                                    <Setter Property="Foreground" Value="#6B7280"/>
                                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>

                                        <!-- Actions - Excel Style -->
                                        <DataGridTemplateColumn Header="Actions" Width="100" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <Button Content="View" Background="#3B82F6" Foreground="White"
                                                            BorderThickness="0" Padding="12,4" FontSize="10"
                                                            HorizontalAlignment="Center" VerticalAlignment="Center"
                                                            Click="GoToActivity_Click" Tag="{Binding}">
                                                        <Button.Style>
                                                            <Style TargetType="Button">
                                                                <Setter Property="Template">
                                                                    <Setter.Value>
                                                                        <ControlTemplate TargetType="Button">
                                                                            <Border Background="{TemplateBinding Background}"
                                                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                                                    CornerRadius="3" Padding="{TemplateBinding Padding}">
                                                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                            </Border>
                                                                        </ControlTemplate>
                                                                    </Setter.Value>
                                                                </Setter>
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="#2563EB"/>
                                                                    </Trigger>
                                                                    <Trigger Property="IsPressed" Value="True">
                                                                        <Setter Property="Background" Value="#1D4ED8"/>
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Button.Style>
                                                    </Button>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- Pagination Controls -->
                                <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0" Padding="32,20" Margin="0,24,0,0"
                                        BorderBrush="#E1E5E9" BorderThickness="0,1,0,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Items Info -->
                                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                            <TextBlock Text="Showing " FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="ItemsFromText" Text="1" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#333" VerticalAlignment="Center"/>
                                            <TextBlock Text=" - " FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="ItemsToText" Text="20" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#333" VerticalAlignment="Center"/>
                                            <TextBlock Text=" of " FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="TotalItemsText" Text="100" FontSize="12" FontWeight="SemiBold"
                                                       Foreground="#673AB7" VerticalAlignment="Center"/>
                                            <TextBlock Text=" activities" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Page Size Selector -->
                                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                            <TextBlock Text="Show:" FontSize="12" Foreground="#666" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <ComboBox x:Name="PageSizeComboBox" Width="80" FontSize="12"
                                                      SelectionChanged="PageSizeComboBox_SelectionChanged">
                                                <ComboBoxItem Content="10" IsSelected="True"/>
                                                <ComboBoxItem Content="20"/>
                                                <ComboBoxItem Content="50"/>
                                                <ComboBoxItem Content="100"/>
                                            </ComboBox>
                                        </StackPanel>

                                        <!-- Navigation Buttons -->
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                                            <Button x:Name="FirstPageButton" Width="40" Height="36"
                                                    Background="#673AB7" BorderThickness="0" Foreground="White"
                                                    Click="FirstPageButton_Click" Margin="0,0,6,0" ToolTip="First Page">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <materialDesign:PackIcon Kind="PageFirst" Width="16" Height="16"
                                                                                                 Foreground="{TemplateBinding Foreground}"
                                                                                                 HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#5E35B1"/>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#4527A0"/>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Button x:Name="PrevPageButton" Width="40" Height="36"
                                                    Background="#673AB7" BorderThickness="0" Foreground="White"
                                                    Click="PrevPageButton_Click" Margin="0,0,6,0" ToolTip="Previous Page">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <materialDesign:PackIcon Kind="ChevronLeft" Width="16" Height="16"
                                                                                                 Foreground="{TemplateBinding Foreground}"
                                                                                                 HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#5E35B1"/>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#4527A0"/>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Border Background="#F8F9FA" CornerRadius="8" Padding="12,8" Margin="8,0">
                                                <TextBlock x:Name="CurrentPageText" Text="Page 1 of 5" FontSize="13" FontWeight="SemiBold"
                                                           Foreground="#673AB7" VerticalAlignment="Center"/>
                                            </Border>

                                            <Button x:Name="NextPageButton" Width="40" Height="36"
                                                    Background="#673AB7" BorderThickness="0" Foreground="White"
                                                    Click="NextPageButton_Click" Margin="6,0,0,0" ToolTip="Next Page">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <materialDesign:PackIcon Kind="ChevronRight" Width="16" Height="16"
                                                                                                 Foreground="{TemplateBinding Foreground}"
                                                                                                 HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#5E35B1"/>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#4527A0"/>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <Button x:Name="LastPageButton" Width="40" Height="36"
                                                    Background="#673AB7" BorderThickness="0" Foreground="White"
                                                    Click="LastPageButton_Click" Margin="6,0,0,0" ToolTip="Last Page">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <materialDesign:PackIcon Kind="PageLast" Width="16" Height="16"
                                                                                                 Foreground="{TemplateBinding Foreground}"
                                                                                                 HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#5E35B1"/>
                                                                        </Trigger>
                                                                        <Trigger Property="IsPressed" Value="True">
                                                                            <Setter Property="Background" Value="#4527A0"/>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>

                                <!-- Empty State -->
                                <StackPanel Grid.Row="1" x:Name="EmptyStatePanel" Visibility="Collapsed"
                                            HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,100,0,0">
                                    <materialDesign:PackIcon Kind="ClockOutline" Width="64" Height="64"
                                                             Foreground="#E0E0E0" HorizontalAlignment="Center"/>
                                    <TextBlock Text="No recent activities found" FontSize="16"
                                               Foreground="#999" HorizontalAlignment="Center" Margin="0,16,0,8"/>
                                    <TextBlock Text="Start creating projects, invoices, or commitments to see activity here"
                                               FontSize="12" Foreground="#CCC" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </ScrollViewer>
            </Grid>

        </Grid>

        <!-- Status Bar - SAP Style -->
        <Border Grid.Row="2" Background="#F7F9FA" BorderBrush="#E1E5E9" BorderThickness="0,1,0,0" Padding="20,8">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="8" Height="8" Fill="#4CAF50" Margin="0,0,8,0"/>
                    <TextBlock Text="Ready" VerticalAlignment="Center" Foreground="#6A6D70" FontSize="12"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <TextBlock Text="Financial Tracker - WE Edition v2.0" VerticalAlignment="Center" Foreground="#6B7280" FontSize="12" Margin="0,0,16,0"/>
                    <Border Background="#6366F1" CornerRadius="12" Padding="8,4" Cursor="Hand"
                            ToolTip="Click to visit Mostafa Yassin's Portfolio"
                            MouseLeftButtonDown="DeveloperInfo_Click"
                            MouseEnter="DeveloperInfo_MouseEnter"
                            MouseLeave="DeveloperInfo_MouseLeave">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountCircle" Width="14" Height="14" Foreground="White" VerticalAlignment="Center" Margin="0,0,6,0"/>
                            <TextBlock Text="Developed by " Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                            <TextBlock Text="Mostafa Yassin" Foreground="#E0E7FF" FontSize="11" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
