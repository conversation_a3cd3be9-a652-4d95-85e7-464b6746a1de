using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Linq;
using FinancialTracker.Helpers;

namespace FinancialTracker.Services
{
    public interface IFileService
    {
        Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName);
        Task<string> SavePOFileAsync(string sourceFilePath, string projectName, string fileName);
        Task<string> SaveProjectFileAsync(string sourceFilePath, string projectName, string fileName, string category);
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> OpenFileAsync(string filePath);
        string GetFullPath(string relativePath);
        void EnsureDirectoriesExist();
        string SelectFile(string filter = "All Files (*.*)|*.*");
    }

    public class FileService : IFileService
    {
        private readonly string _baseDirectory;
        private readonly string _dataDirectory;
        private readonly string _invoicesDirectory;
        private readonly string _commitmentsDirectory;
        private readonly string _repliesDirectory;
        private readonly string _lettersDirectory;
        private readonly string _poDirectory;
        private readonly string _filesDirectory;
        private readonly string _projectFilesDirectory;

        public FileService()
        {
            _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

            // Check if we're running from development environment and FinancialTracker_Standalone exists
            string standaloneFolder = Path.Combine(_baseDirectory, "FinancialTracker_Standalone");
            if (Directory.Exists(standaloneFolder))
            {
                _baseDirectory = standaloneFolder;
            }
            // If we're already in FinancialTracker_Standalone, use current directory
            else if (_baseDirectory.EndsWith("FinancialTracker_Standalone"))
            {
                // Already in the right place
            }

            _dataDirectory = Path.Combine(_baseDirectory, "data");
            _invoicesDirectory = Path.Combine(_dataDirectory, "invoices");
            _commitmentsDirectory = Path.Combine(_dataDirectory, "commitments");
            _repliesDirectory = Path.Combine(_dataDirectory, "replies");
            _lettersDirectory = Path.Combine(_dataDirectory, "letters");
            _poDirectory = Path.Combine(_dataDirectory, "po");
            _filesDirectory = Path.Combine(_baseDirectory, "Files");
            _projectFilesDirectory = Path.Combine(_dataDirectory, "project-files");

            EnsureDirectoriesExist();

            // Debug: Show where files will be saved
            System.Diagnostics.Debug.WriteLine($"FileService: Base Directory = {_baseDirectory}");
            System.Diagnostics.Debug.WriteLine($"FileService: Project Files Directory = {_projectFilesDirectory}");
        }

        public void EnsureDirectoriesExist()
        {
            Directory.CreateDirectory(_dataDirectory);
            Directory.CreateDirectory(_invoicesDirectory);
            Directory.CreateDirectory(_commitmentsDirectory);
            Directory.CreateDirectory(_repliesDirectory);
            Directory.CreateDirectory(_lettersDirectory);
            Directory.CreateDirectory(_poDirectory);
            Directory.CreateDirectory(_filesDirectory);
            Directory.CreateDirectory(_projectFilesDirectory);
            Directory.CreateDirectory(Path.Combine(_filesDirectory, "Projects"));
        }

        public string SelectFile(string filter = "All Files (*.*)|*.*")
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = filter,
                Title = "Select File"
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : string.Empty;
        }

        public async Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName)
        {
            if (!File.Exists(sourceFilePath))
                throw new FileNotFoundException("Source file not found", sourceFilePath);

            string targetDirectory = category.ToLower() switch
            {
                "invoice" => _invoicesDirectory,
                "commitment" => _commitmentsDirectory,
                "reply" => _repliesDirectory,
                "letters" => _lettersDirectory,
                "po" => _poDirectory,
                "project" => _projectFilesDirectory,
                _ => throw new ArgumentException("Invalid category", nameof(category))
            };

            // Generate unique filename if file already exists
            string targetFilePath = Path.Combine(targetDirectory, fileName);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string extension = Path.GetExtension(fileName);
            int counter = 1;

            while (File.Exists(targetFilePath))
            {
                string newFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                targetFilePath = Path.Combine(targetDirectory, newFileName);
                counter++;
            }

            await Task.Run(() => File.Copy(sourceFilePath, targetFilePath, true));
            
            // Return relative path from base directory
            return Path.GetRelativePath(_baseDirectory, targetFilePath);
        }

        public async Task<string> SavePOFileAsync(string sourceFilePath, string projectName, string fileName)
        {
            if (!File.Exists(sourceFilePath))
                throw new FileNotFoundException("Source file not found", sourceFilePath);

            // Create project-specific directory
            string projectDirectory = Path.Combine(_filesDirectory, "Projects", SanitizeFileName(projectName));
            Directory.CreateDirectory(projectDirectory);

            // Generate unique filename if file already exists
            string targetFilePath = Path.Combine(projectDirectory, fileName);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string extension = Path.GetExtension(fileName);
            int counter = 1;

            while (File.Exists(targetFilePath))
            {
                string newFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                targetFilePath = Path.Combine(projectDirectory, newFileName);
                counter++;
            }

            await Task.Run(() => File.Copy(sourceFilePath, targetFilePath, true));

            // Return relative path from base directory
            return Path.GetRelativePath(_baseDirectory, targetFilePath);
        }

        public async Task<string> SaveProjectFileAsync(string sourceFilePath, string projectName, string fileName, string category)
        {
            if (!File.Exists(sourceFilePath))
                throw new FileNotFoundException("Source file not found", sourceFilePath);

            // Determine target directory based on category
            string targetDirectory;

            // Special handling for specific categories that have dedicated folders
            if (!string.IsNullOrEmpty(category))
            {
                switch (category.ToLower())
                {
                    case "po":
                        targetDirectory = _poDirectory;
                        break;
                    case "general":
                        targetDirectory = _projectFilesDirectory;
                        break;
                    default:
                        targetDirectory = Path.Combine(_projectFilesDirectory, SanitizeFileName(category));
                        break;
                }
            }
            else
            {
                targetDirectory = _projectFilesDirectory;
            }

            Directory.CreateDirectory(targetDirectory);

            // Debug: Show where the file will be saved
            System.Diagnostics.Debug.WriteLine($"SaveProjectFileAsync: Category = {category}, Target Directory = {targetDirectory}");

            // Generate unique filename if file already exists
            string targetFilePath = Path.Combine(targetDirectory, fileName);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string extension = Path.GetExtension(fileName);
            int counter = 1;

            while (File.Exists(targetFilePath))
            {
                string newFileName = $"{fileNameWithoutExtension}_{counter}{extension}";
                targetFilePath = Path.Combine(targetDirectory, newFileName);
                counter++;
            }

            await Task.Run(() => File.Copy(sourceFilePath, targetFilePath, true));

            // Return relative path from base directory
            return Path.GetRelativePath(_baseDirectory, targetFilePath);
        }

        private string SanitizeFileName(string fileName)
        {
            // Remove invalid characters from project name for folder creation
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }
            return fileName;
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                string fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() => File.Delete(fullPath));
                    return true;
                }
                return false;
            }
            catch (UnauthorizedAccessException ex)
            {
                System.Diagnostics.Debug.WriteLine($"Access denied when deleting file {filePath}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                System.Diagnostics.Debug.WriteLine($"IO error when deleting file {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Unexpected error when deleting file {filePath}: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> OpenFileAsync(string filePath)
        {
            try
            {
                string fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() =>
                    {
                        var processStartInfo = new ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        };
                        Process.Start(processStartInfo);
                    });
                    return true;
                }
                return false;
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"No application associated with file {filePath}: {ex.Message}");
                return false;
            }
            catch (UnauthorizedAccessException ex)
            {
                System.Diagnostics.Debug.WriteLine($"Access denied when opening file {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Unexpected error when opening file {filePath}: {ex.Message}");
                return false;
            }
        }

        public string GetFullPath(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
                return string.Empty;

            if (Path.IsPathRooted(relativePath))
                return relativePath;

            return Path.Combine(_baseDirectory, relativePath);
        }

        #region Security and Validation

        private ValidationResult ValidateFileForUpload(string sourceFilePath, string fileName)
        {
            try
            {
                // Check file size
                var fileInfo = new FileInfo(sourceFilePath);
                var maxSize = Config.Security.MaxFileSize;
                if (fileInfo.Length > maxSize)
                {
                    return ValidationResult.Error($"File size ({fileInfo.Length:N0} bytes) exceeds maximum allowed size ({maxSize:N0} bytes).");
                }

                // Check file extension
                var extension = Path.GetExtension(fileName).ToLowerInvariant();
                var allowedExtensions = Config.Security.AllowedFileExtensions;
                if (!allowedExtensions.Any(ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase)))
                {
                    return ValidationResult.Error($"File type '{extension}' is not allowed. Allowed types: {string.Join(", ", allowedExtensions)}");
                }

                // Validate filename
                var filenameValidation = ValidationHelper.ValidateFilePath(fileName, "File Name", allowedExtensions);
                if (!filenameValidation.IsValid)
                {
                    return filenameValidation;
                }

                // Check for malicious content (basic check)
                if (IsExecutableFile(extension))
                {
                    return ValidationResult.Error("Executable files are not allowed for security reasons.");
                }

                return ValidationResult.Success();
            }
            catch (Exception)
            {
                // Error validating file
                return ValidationResult.Error("File validation failed due to an internal error.");
            }
        }

        private bool IsExecutableFile(string extension)
        {
            var executableExtensions = new[] { ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".js", ".jar", ".msi" };
            return executableExtensions.Any(ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase));
        }

        #endregion
    }

    public class SecurityException : Exception
    {
        public SecurityException(string message) : base(message) { }
        public SecurityException(string message, Exception innerException) : base(message, innerException) { }
    }
}
