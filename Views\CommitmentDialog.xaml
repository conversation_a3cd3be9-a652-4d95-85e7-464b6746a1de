<Window x:Class="FinancialTracker.CommitmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Commitment Details"
        WindowState="Maximized"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Padding="24,20" Margin="0,0,0,24" Background="#4A5568" CornerRadius="4">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="Handshake" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock Text="Commitment Information" FontSize="24" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock Text="Create and manage commitment details with financial tracking"
                          FontSize="13" Foreground="#E5E7EB" HorizontalAlignment="Center" Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="12">
            <StackPanel Margin="0">

                <!-- Step 1: Basic Commitment Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="1" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Basic Commitment Information" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Enter commitment title and select type" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Commitment Title -->
                            <TextBox x:Name="TitleTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Commitment Title *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     MaxLength="100" Height="56" Margin="0,0,8,0"
                                     FontSize="14" VerticalContentAlignment="Center"
                                     TextChanged="TitleTextBox_TextChanged"/>

                            <!-- Commitment Type -->
                            <ComboBox x:Name="TypeComboBox" Grid.Column="1"
                                      materialDesign:HintAssist.Hint="Select Type *"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      Height="56" Margin="8,0,0,0" FontSize="14"
                                      SelectionChanged="TypeComboBox_SelectionChanged">
                                <!-- Types will be loaded dynamically based on project configuration -->
                            </ComboBox>
                        </Grid>

                        <!-- Custom Type (Hidden by default) -->
                        <TextBox x:Name="CustomTypeTextBox"
                                 materialDesign:HintAssist.Hint="Custom Type"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 Height="56" Margin="0,16,0,0"
                                 FontSize="14" VerticalContentAlignment="Center"
                                 Visibility="Collapsed"/>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step1CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 1 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step1CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 2: Financial Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step2Card" Opacity="0.5">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="2" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Financial Information" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Enter amount and exchange rate details" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Amount USD -->
                            <TextBox x:Name="AmountTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Amount (USD) *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     Height="56" Margin="0,0,8,0"
                                     FontSize="14" VerticalContentAlignment="Center"
                                     TextChanged="AmountTextBox_TextChanged"/>

                            <!-- Exchange Rate -->
                            <TextBox x:Name="ExchangeRateTextBox" Grid.Column="1"
                                     materialDesign:HintAssist.Hint="Exchange Rate (EGP/USD) *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     Text="1.0" Height="56" Margin="8,0,0,0"
                                     FontSize="14" VerticalContentAlignment="Center"
                                     TextChanged="ExchangeRateTextBox_TextChanged"/>
                        </Grid>

                        <!-- Amount in EGP Display -->
                        <Border Background="#F0F9FF" BorderBrush="#0EA5E9" BorderThickness="1" CornerRadius="6" Padding="16,12" Margin="0,16,0,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Foreground="#0EA5E9" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <StackPanel>
                                    <TextBlock Text="Amount in EGP" FontSize="12" Foreground="#0369A1" Margin="0,0,0,2"/>
                                    <TextBlock x:Name="AmountEGPText" Text="EGP 0" FontWeight="SemiBold" FontSize="18" Foreground="#0EA5E9"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Remaining Amount Display (will be populated after save) -->
                        <Border x:Name="RemainingAmountPanel" Background="#F0FDF4" BorderBrush="#22C55E" BorderThickness="1" CornerRadius="6" Padding="16,12" Margin="0,8,0,0" Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" Foreground="#22C55E" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <StackPanel>
                                    <TextBlock Text="Remaining Amount" FontSize="12" Foreground="#15803D" Margin="0,0,0,2"/>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock x:Name="RemainingAmountUSDText" Text="$0" FontWeight="SemiBold" FontSize="16" Foreground="#22C55E" Margin="0,0,8,0"/>
                                        <TextBlock Text="•" FontSize="16" Foreground="#22C55E" Margin="0,0,8,0"/>
                                        <TextBlock x:Name="RemainingAmountEGPText" Text="EGP 0" FontWeight="SemiBold" FontSize="16" Foreground="#22C55E"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step2CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 2 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step2CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 3: Additional Details -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step3Card" Opacity="0.5">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="3" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Additional Details" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Set creation date and add description" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Creation Date -->
                            <DatePicker x:Name="CreatedDatePicker" Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Creation Date *"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        Height="56" Margin="0,0,8,0"
                                        FontSize="14" VerticalContentAlignment="Center"/>

                            <!-- Placeholder for future field -->
                            <Border Grid.Column="1" Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="4" Margin="8,0,0,0" Height="56">
                                <TextBlock Text="Additional fields can be added here" FontSize="12" Foreground="#9CA3AF"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </Grid>

                        <!-- Description -->
                        <TextBox x:Name="DescriptionTextBox"
                                 materialDesign:HintAssist.Hint="Description (Optional)"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 MaxLength="500" Height="120" Margin="0,16,0,0"
                                 FontSize="14" TextWrapping="Wrap"
                                 AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step3CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 3 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step3CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 4: Link Invoices & Attachments -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step4Card" Opacity="0.5">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#6B7280" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="4" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Link Invoices &amp; Attachments" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Link existing invoices and attach files (optional)" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Invoice Linking Section -->
                        <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="Link" Width="18" Height="18" Foreground="#64748B" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="Link Existing Invoices" FontWeight="Medium" FontSize="14" Foreground="#334155"/>
                                </StackPanel>

                                <TextBlock Text="You can select multiple existing invoices in the project and link them to this commitment"
                                           FontSize="12" Foreground="#64748B" Margin="0,0,0,12"/>

                                <Grid Margin="0,0,0,12">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Available Invoices List with Multi-Select -->
                                    <Border Grid.Row="0" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="4" Padding="8" Background="White" MaxHeight="200">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                            <ListBox x:Name="AvailableInvoicesListBox" SelectionMode="Multiple" BorderThickness="0">
                                                <ListBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <Grid Margin="0,4">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>

                                                            <CheckBox Grid.Column="0" IsChecked="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}"
                                                                      Margin="0,0,8,0" VerticalAlignment="Center"/>

                                                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                                <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" FontSize="13" Foreground="#1E293B" Margin="0,0,8,0"/>
                                                                <TextBlock Text="•" FontSize="13" Foreground="#94A3B8" Margin="0,0,6,0"/>
                                                                <TextBlock Text="{Binding AmountUSD, StringFormat='{}{0:C0}'}" FontSize="13" Foreground="#059669" Margin="0,0,8,0"/>
                                                                <TextBlock Text="{Binding Description}" FontSize="12" Foreground="#64748B" TextTrimming="CharacterEllipsis" MaxWidth="200"/>
                                                            </StackPanel>
                                                        </Grid>
                                                    </DataTemplate>
                                                </ListBox.ItemTemplate>
                                            </ListBox>
                                        </ScrollViewer>
                                    </Border>

                                    <!-- Action Buttons -->
                                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,8,0,0">
                                        <Button x:Name="LinkInvoiceButton"
                                                Content="Link Selected Invoices" Style="{StaticResource MaterialDesignRaisedButton}"
                                                Height="40" Padding="16,0"
                                                Click="LinkInvoiceButton_Click">
                                            <Button.ToolTip>
                                                <ToolTip Content="Link all selected invoices to this commitment"/>
                                            </Button.ToolTip>
                                        </Button>

                                        <Button x:Name="RefreshInvoicesButton"
                                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                                Content="🔄 Refresh" Margin="8,0,0,0" Height="40" Padding="12,0"
                                                Click="RefreshInvoicesButton_Click"/>

                                        <TextBlock x:Name="SelectedCountText" VerticalAlignment="Center" Margin="16,0,0,0"
                                                   FontSize="12" Foreground="#64748B" Text="No invoices selected"/>
                                    </StackPanel>
                                </Grid>

                                <!-- Linked Invoices List -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <materialDesign:PackIcon Kind="Invoice" Width="16" Height="16" Foreground="#64748B" VerticalAlignment="Center" Margin="0,0,6,0"/>
                                    <TextBlock Text="Linked Invoices:" FontWeight="Medium" FontSize="13" Foreground="#334155"/>
                                </StackPanel>

                                <Border Background="White" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="4">
                                    <ListBox x:Name="LinkedInvoicesListBox" MaxHeight="120" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             Background="Transparent" BorderThickness="0">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="#FEFEFE" BorderBrush="#F1F5F9" BorderThickness="0,0,0,1" Padding="12,8">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                        <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" FontSize="13" Foreground="#1E293B" Margin="0,0,8,0"/>
                                                        <TextBlock Text="•" FontSize="13" Foreground="#94A3B8" Margin="0,0,6,0"/>
                                                        <TextBlock Text="{Binding AmountUSD, StringFormat='{}{0:C0}'}" FontSize="13" Foreground="#059669" Margin="0,0,8,0"/>
                                                        <TextBlock Text="{Binding Description}" FontSize="12" Foreground="#64748B"/>
                                                    </StackPanel>

                                                    <Button Grid.Column="1" Content="Unlink" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            FontSize="11" Padding="8,4" Height="28" Click="UnlinkInvoiceButton_Click" Tag="{Binding}"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                    </ListBox>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- File Attachment Section -->
                        <Border Background="#FEF7F0" BorderBrush="#FB923C" BorderThickness="1" CornerRadius="6" Padding="16" Margin="0,0,0,0">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                    <materialDesign:PackIcon Kind="Attachment" Width="18" Height="18" Foreground="#EA580C" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="File Attachment" FontWeight="Medium" FontSize="14" Foreground="#9A3412"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="AttachedFileTextBox" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="No file selected"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Height="48" FontSize="14" VerticalContentAlignment="Center"
                                             IsReadOnly="True"/>

                                    <Button x:Name="SelectFileButton" Grid.Column="1"
                                            Content="Browse" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="8,0,0,0" Height="48" Padding="16,0"
                                            Click="SelectFileButton_Click"/>

                                    <Button x:Name="OpenFileButton" Grid.Column="2"
                                            Content="Open" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="8,0,0,0" Height="48" Padding="16,0"
                                            IsEnabled="False" Click="OpenFileButton_Click"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step4CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 4 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step4CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Final Summary -->
                <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,0" x:Name="FinalSummaryCard" Visibility="Collapsed">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CheckAll" Width="24" Height="24" Foreground="#059669" VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <TextBlock Text="Commitment Summary" FontSize="18" FontWeight="SemiBold" Foreground="#065F46"/>
                        </StackPanel>

                        <TextBlock x:Name="FinalSummaryDisplay" Text="📋 Ready to create commitment"
                                   FontSize="14" Foreground="#374151" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons - SAP Style -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="24,16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,12,0" Padding="24,12" FontSize="14" Height="44"
                        Click="CancelButton_Click"/>
                <Button Content="Save Commitment" Style="{StaticResource MaterialDesignRaisedButton}"
                        Padding="24,12" FontSize="14" Height="44" Background="#4A5568"
                        Click="SaveButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
