using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace FinancialTracker.Converters
{
    /// <summary>
    /// Converter to change button background color based on file attachment
    /// Returns green color if file is attached, gray color if not
    /// </summary>
    public class FileAttachmentBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);
            
            if (hasFile)
            {
                // Green background when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E8F5E8"));
            }
            else
            {
                // Gray background when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F3F4F6"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to change button foreground color based on file attachment
    /// Returns green color if file is attached, gray color if not
    /// </summary>
    public class FileAttachmentForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);
            
            if (hasFile)
            {
                // Dark green text when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E7D32"));
            }
            else
            {
                // Gray text when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter to change button border color based on file attachment
    /// Returns green color if file is attached, gray color if not
    /// </summary>
    public class FileAttachmentBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);
            
            if (hasFile)
            {
                // Green border when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E7D32"));
            }
            else
            {
                // Gray border when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D1D5DB"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Letter button - uses blue colors
    /// </summary>
    public class LetterAttachmentBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);
            
            if (hasFile)
            {
                // Blue background when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E3F2FD"));
            }
            else
            {
                // Gray background when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F3F4F6"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Letter button foreground - uses blue colors
    /// </summary>
    public class LetterAttachmentForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);
            
            if (hasFile)
            {
                // Dark blue text when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1976D2"));
            }
            else
            {
                // Gray text when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Letter button border - uses blue colors
    /// </summary>
    public class LetterAttachmentBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);

            if (hasFile)
            {
                // Blue border when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1976D2"));
            }
            else
            {
                // Gray border when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D1D5DB"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Commitment file button - uses orange colors
    /// </summary>
    public class CommitmentFileBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);

            if (hasFile)
            {
                // Orange background when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFF3E0"));
            }
            else
            {
                // Gray background when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F3F4F6"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Commitment file button foreground - uses orange colors
    /// </summary>
    public class CommitmentFileForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);

            if (hasFile)
            {
                // Dark orange text when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F57C00"));
            }
            else
            {
                // Gray text when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converter for Commitment file button border - uses orange colors
    /// </summary>
    public class CommitmentFileBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool hasFile = !string.IsNullOrWhiteSpace(value as string);

            if (hasFile)
            {
                // Orange border when file is attached
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F57C00"));
            }
            else
            {
                // Gray border when no file
                return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D1D5DB"));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

