<Window x:Class="FinancialTracker.Views.ProjectFileUploadDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Upload Project File" Height="650" Width="750"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,24">
            <TextBlock Text="📁 Upload Project File" FontSize="24" FontWeight="Bold"
                       Foreground="{DynamicResource PrimaryHueMidBrush}" Margin="0,0,0,8"/>
            <TextBlock x:Name="ProjectNameText" Text="Project: Loading..."
                       FontSize="16" FontWeight="SemiBold" Margin="0,0,0,8"/>
            <TextBlock Text="Upload a file related to this project (documents, images, specifications, etc.)"
                       FontSize="14" Opacity="0.7"/>
            <TextBlock Text="Files will be organized by category (PO files go to data/po/, others to data/project-files/[Category]/)"
                       FontSize="12" Opacity="0.6" Margin="0,4,0,0" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- File Selection -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="📎 File Selection" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="FilePathTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Selected file path"
                                     IsReadOnly="True" Margin="0,0,8,0"/>
                            
                            <Button x:Name="SelectFileButton" Grid.Column="1"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Content="Browse..." Padding="16,8"
                                    Click="SelectFileButton_Click"/>
                        </Grid>
                        
                        <TextBlock x:Name="FileSizeText" Text="" FontSize="12" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}" 
                                   Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- File Information -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="📝 File Information" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                        
                        <TextBox x:Name="FileNameTextBox"
                                 materialDesign:HintAssist.Hint="File name (will be auto-filled)"
                                 Margin="0,0,0,16"/>
                        
                        <TextBox x:Name="DescriptionTextBox"
                                 materialDesign:HintAssist.Hint="Description (optional)"
                                 AcceptsReturn="True" TextWrapping="Wrap"
                                 Height="80" VerticalScrollBarVisibility="Auto"
                                 Margin="0,0,0,16"/>
                        
                        <ComboBox x:Name="CategoryComboBox"
                                  materialDesign:HintAssist.Hint="Category"
                                  Margin="0,0,0,0">
                            <ComboBoxItem Content="General" IsSelected="True"/>
                            <ComboBoxItem Content="Document"/>
                            <ComboBoxItem Content="Image"/>
                            <ComboBoxItem Content="Specification"/>
                            <ComboBoxItem Content="Contract"/>
                            <ComboBoxItem Content="Report"/>
                            <ComboBoxItem Content="PO"/>
                            <ComboBoxItem Content="Other"/>
                        </ComboBox>
                    </StackPanel>
                </materialDesign:Card>

                <!-- File Preview/Info -->
                <materialDesign:Card x:Name="FileInfoCard" Padding="16" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="📋 File Details" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="File Type:" FontWeight="SemiBold" Margin="0,0,8,4"/>
                            <TextBlock x:Name="FileTypeText" Grid.Row="0" Grid.Column="1" Text="" Margin="0,0,0,4"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="File Size:" FontWeight="SemiBold" Margin="0,0,8,4"/>
                            <TextBlock x:Name="FileDetailSizeText" Grid.Row="1" Grid.Column="1" Text="" Margin="0,0,0,4"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Modified:" FontWeight="SemiBold" Margin="0,0,8,0"/>
                            <TextBlock x:Name="LastModifiedText" Grid.Row="2" Grid.Column="1" Text="" Margin="0,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,24,0,0">
            <Button Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,8,0" Padding="24,8" Click="CancelButton_Click"/>
            <Button x:Name="UploadButton" Content="Upload File" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="24,8" Click="UploadButton_Click" IsEnabled="False"/>
        </StackPanel>
    </Grid>
</Window>
