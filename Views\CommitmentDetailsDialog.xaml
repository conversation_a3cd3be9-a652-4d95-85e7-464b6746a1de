<Window x:Class="FinancialTracker.Views.CommitmentDetailsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:FinancialTracker.Converters"
        Title="Commitment Details" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        Style="{StaticResource MaterialDesignWindow}">

    <Window.Resources>
        <converters:CommitmentFileBackgroundConverter x:Key="CommitmentFileBackgroundConverter"/>
        <converters:CommitmentFileForegroundConverter x:Key="CommitmentFileForegroundConverter"/>
        <converters:CommitmentFileBorderConverter x:Key="CommitmentFileBorderConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="{Binding Title}" FontSize="24" FontWeight="Bold" Foreground="#1976D2"/>
            <TextBlock Text="{Binding FinancialBreakdown}" FontSize="14" Foreground="#666" Margin="0,5,0,0"/>
            <TextBlock Text="{Binding DetailedStatus}" FontSize="12" Foreground="#888" Margin="0,2,0,0"/>
        </StackPanel>

        <!-- Summary Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="Total Commitment" FontSize="12" Foreground="#666"/>
                    <TextBlock Text="{Binding AmountUSD, StringFormat=\$\{0:N0\}}" FontSize="20" FontWeight="Bold" Foreground="#1976D2"/>
                    <TextBlock Text="{Binding Type}" FontSize="10" Foreground="#888"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Invoiced Amount Card -->
            <materialDesign:Card Grid.Column="1" Margin="5,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="Total Invoiced" FontSize="12" Foreground="#666"/>
                    <TextBlock Text="{Binding TotalInvoicedAmount, StringFormat=\$\{0:N0\}}" FontSize="20" FontWeight="Bold" Foreground="#F57C00"/>
                    <TextBlock Text="{Binding InvoicesCount, StringFormat=\{0\} invoices}" FontSize="10" Foreground="#888"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Paid Amount Card -->
            <materialDesign:Card Grid.Column="2" Margin="5,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="Total Paid" FontSize="12" Foreground="#666"/>
                    <TextBlock Text="{Binding TotalPaidAmount, StringFormat=\$\{0:N0\}}" FontSize="20" FontWeight="Bold" Foreground="#388E3C"/>
                    <TextBlock Text="{Binding FullyPaidInvoicesCount, StringFormat=\{0\} fully paid}" FontSize="10" Foreground="#888"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Remaining Card -->
            <materialDesign:Card Grid.Column="3" Margin="10,0,0,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="Remaining" FontSize="12" Foreground="#666"/>
                    <TextBlock Text="{Binding RemainingCommitmentAmount, StringFormat=\$\{0:N0\}}" FontSize="20" FontWeight="Bold" Foreground="#D32F2F"/>
                    <TextBlock Text="{Binding RemainingAmountEGP, StringFormat=EGP \{0:N0\}}" FontSize="14" FontWeight="SemiBold" Foreground="#D32F2F" Margin="0,2,0,0"/>
                    <TextBlock Text="{Binding CompletionPercentage, StringFormat=\{0:F1\}% complete}" FontSize="10" Foreground="#888"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Invoices DataGrid -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- DataGrid Header -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="15,10">
                    <TextBlock Text="Related Invoices" FontSize="16" FontWeight="SemiBold" Foreground="#333"/>
                </Border>

                <!-- DataGrid -->
                <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid" 
                          ItemsSource="{Binding Invoices}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          Margin="15">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Invoice #" Binding="{Binding InvoiceNumber}" Width="100"/>
                        <DataGridTextColumn Header="Type" Binding="{Binding TypeDisplay}" Width="80"/>
                        <DataGridTextColumn Header="Amount" Binding="{Binding AmountUSD, StringFormat=C0}" Width="100"/>
                        <DataGridTextColumn Header="Paid" Binding="{Binding PaidAmount, StringFormat=C0}" Width="100"/>
                        <DataGridTextColumn Header="Remaining" Binding="{Binding RemainingAmount, StringFormat=C0}" Width="100"/>
                        
                        <!-- Payment Status -->
                        <DataGridTemplateColumn Header="Status" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Margin="0,0,5,0" Fill="Orange"/>
                                        <TextBlock Text="Status" FontSize="10"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="Site" Binding="{Binding SiteName}" Width="100"/>
                        <DataGridTextColumn Header="Signature Date" Binding="{Binding SignatureDate, StringFormat=dd/MM/yyyy, TargetNullValue=Not Signed}" Width="120"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="150"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="OpenFileButton" Content="Open File" Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0" Padding="20,8" Click="OpenFile_Click"
                    Background="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBackgroundConverter}}"
                    Foreground="{Binding AttachedFileName, Converter={StaticResource CommitmentFileForegroundConverter}}"
                    BorderBrush="{Binding AttachedFileName, Converter={StaticResource CommitmentFileBorderConverter}}"
                    ToolTip="Open attached commitment file"/>
            <Button Content="Export Report" Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0" Padding="20,8" Click="ExportReport_Click"/>
            <Button Content="Close" Style="{StaticResource MaterialDesignRaisedButton}"
                    Padding="20,8" Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window>
