<Window x:Class="FinancialTracker.Views.ExportColumnsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Export Invoices to Excel"
        Height="600"
        Width="500"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7"
        ResizeMode="NoResize">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#4A5568" Padding="20,16" CornerRadius="6" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="FileExcel" Width="24" Height="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <TextBlock Text="Select Columns to Export" FontSize="18" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Columns Selection -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="Choose which columns to include in the Excel file:" FontSize="14" Foreground="#6B7280" Margin="0,0,0,16"/>
                    
                    <!-- Select All/None -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                        <Button x:Name="SelectAllButton" Content="Select All" Style="{StaticResource MaterialDesignOutlinedButton}"
                                FontSize="12" Padding="12,6" Margin="0,0,8,0" Click="SelectAllButton_Click"/>
                        <Button x:Name="SelectNoneButton" Content="Select None" Style="{StaticResource MaterialDesignOutlinedButton}"
                                FontSize="12" Padding="12,6" Click="SelectNoneButton_Click"/>
                    </StackPanel>

                    <!-- Column Checkboxes -->
                    <StackPanel x:Name="ColumnsPanel">
                        <CheckBox x:Name="InvoiceNumberCheckBox" Content="Invoice Number" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="TypeCheckBox" Content="Type" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="PaymentTermsCheckBox" Content="Payment Terms Details" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="SiteCheckBox" Content="Site" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="AmountCheckBox" Content="Amount" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="ExchangeRateCheckBox" Content="Exchange Rate" IsChecked="False" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="PaidCheckBox" Content="Paid" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="RemainingCheckBox" Content="Remaining" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="DateStatusCheckBox" Content="Date &amp; Status" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="CommitmentCheckBox" Content="Commitment" IsChecked="False" Margin="0,0,0,8" FontSize="14"/>
                        <CheckBox x:Name="DescriptionCheckBox" Content="Description" IsChecked="True" Margin="0,0,0,8" FontSize="14"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="CancelButton" Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}"
                    FontSize="14" Padding="20,10" Margin="0,0,12,0" Click="CancelButton_Click"/>
            <Button x:Name="ExportButton" Style="{StaticResource MaterialDesignRaisedButton}"
                    FontSize="14" Padding="20,10" Background="#059669" BorderBrush="#059669" Click="ExportButton_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="Export to Excel" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>
    </Grid>
</Window>
