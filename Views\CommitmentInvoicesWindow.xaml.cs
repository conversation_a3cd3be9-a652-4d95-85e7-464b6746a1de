#nullable enable
using System;
using System.Linq;
using System.Windows;
using FinancialTracker.Models;
using FinancialTracker.Views;

namespace FinancialTracker
{
    public partial class CommitmentInvoicesWindow : Window
    {
        private Commitment? _commitment;
        private int _commitmentId;

        public CommitmentInvoicesWindow(int commitmentId)
        {
            InitializeComponent();
            _commitmentId = commitmentId;
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load commitment details
                _commitment = await App.DataService.GetCommitmentByIdAsync(_commitmentId);
                if (_commitment == null)
                {
                    MessageBox.Show("Commitment not found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // Update UI with commitment info
                CommitmentTitleText.Text = $"Invoices for: {_commitment.Title}";
                Title = $"Commitment Invoices - {_commitment.Title}";
                
                CommitmentTitleDetail.Text = _commitment.Title;
                CommitmentTypeDetail.Text = _commitment.Type.ToString();
                CommitmentAmountDetail.Text = $"${_commitment.AmountUSD:N2}";

                // Load related invoices
                await LoadInvoicesData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadInvoicesData()
        {
            try
            {
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var commitmentInvoices = allInvoices.Where(i => i.CommitmentId == _commitmentId).ToList();
                
                InvoicesDataGrid.ItemsSource = commitmentInvoices;
                TotalInvoicesDetail.Text = commitmentInvoices.Count.ToString();

                // Update summary
                var totalAmount = commitmentInvoices.Sum(i => i.AmountUSD);
                var paidAmount = commitmentInvoices.Sum(i => i.PaidAmount);
                var unpaidAmount = commitmentInvoices.Sum(i => i.RemainingAmount);

                TotalAmountText.Text = $"${totalAmount:N2}";
                PaidAmountText.Text = $"${paidAmount:N2}";
                UnpaidAmountText.Text = $"${unpaidAmount:N2}";

                // Add exchange rate information if commitment is available
                if (_commitment != null)
                {
                    // Update commitment amount display with exchange rate info
                    CommitmentAmountDetail.Text = $"${_commitment.AmountUSD:N2} (EGP {_commitment.AmountEGP:N0} @ {_commitment.ExchangeRate:F2})";

                    // Add remaining amount with corrected calculation
                    var remainingCorrected = _commitment.RemainingCommitmentAmountCorrected;
                    var remainingEGP = _commitment.AmountEGP - _commitment.TotalInvoicedAmountEGP;

                    // You can add a new TextBlock in XAML to show this information
                    Title = $"Commitment Invoices - {_commitment.Title} | Remaining: ${remainingCorrected:N2} (EGP {remainingEGP:N0})";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            // Show dialog to choose between existing invoice or create new one
            var choiceDialog = new InvoiceChoiceDialog(_commitment?.ProjectId ?? 0, _commitmentId);
            if (choiceDialog.ShowDialog() == true)
            {
                await LoadInvoicesData();
            }
        }

        private async void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var dialog = new InvoiceDialog(invoice);
                if (dialog.ShowDialog() == true)
                {
                    await LoadInvoicesData();
                }
            }
        }

        private async void CopyInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice originalInvoice)
            {
                // Create a new invoice with the same properties
                var newInvoice = new Invoice
                {
                    InvoiceNumber = $"{originalInvoice.InvoiceNumber}_Copy_{DateTime.Now:yyyyMMddHHmmss}",
                    ProjectId = originalInvoice.ProjectId,
                    CommitmentId = originalInvoice.CommitmentId,
                    AmountUSD = originalInvoice.AmountUSD,
                    PaidAmount = 0, // Reset paid amount for new invoice
                    Description = originalInvoice.Description,
                    Type = originalInvoice.Type,
                    InvoiceDate = DateTime.Now,
                    CreatedDate = DateTime.Now
                };

                var dialog = new InvoiceDialog(newInvoice);
                if (dialog.ShowDialog() == true)
                {
                    await LoadInvoicesData();
                }
            }
        }

        private async void OpenInvoiceFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice && !string.IsNullOrEmpty(invoice.AttachedFilePath))
            {
                try
                {
                    await App.FileService.OpenFileAsync(invoice.AttachedFilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening file: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice invoice)
            {
                var result = MessageBox.Show($"Are you sure you want to remove invoice '{invoice.InvoiceNumber}' from this commitment?\n\nThe invoice will not be deleted, just unlinked from this commitment.",
                    "Remove from Commitment", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await App.DataService.RemoveInvoiceFromCommitmentAsync(invoice.Id);
                        await LoadInvoicesData();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error removing invoice from commitment: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesData();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
