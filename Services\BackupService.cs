#nullable enable
using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using FinancialTracker.Helpers;

namespace FinancialTracker.Services
{
    public interface IBackupService
    {
        Task<string> CreateBackupAsync();
        Task<bool> RestoreBackupAsync(string backupPath);
        Task CleanupOldBackupsAsync();
        Task<bool> AutoBackupAsync();
    }

    public class BackupService : IBackupService
    {
        private readonly string _backupDirectory;
        private readonly string _dataDirectory;
        private readonly string _databasePath;

        public BackupService()
        {
            _backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Config.Files.BackupDirectory);
            _dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, Config.Files.DataDirectory);
            _databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FinancialTracker.db");
            
            EnsureBackupDirectoryExists();
        }

        private void EnsureBackupDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    Directory.CreateDirectory(_backupDirectory);
                }
            }
            catch (Exception)
            {
                // Failed to create backup directory
            }
        }

        public async Task<string> CreateBackupAsync()
        {
            // Starting backup creation
            
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupFileName = $"FinancialTracker_Backup_{timestamp}";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                if (Config.Files.CompressBackups)
                {
                    backupPath += ".zip";
                    await CreateCompressedBackupAsync(backupPath);
                }
                else
                {
                    Directory.CreateDirectory(backupPath);
                    await CreateUncompressedBackupAsync(backupPath);
                }

                // Backup created successfully
                return backupPath;
            }
            catch (Exception)
            {
                // Failed to create backup
                throw;
            }
        }

        private async Task CreateCompressedBackupAsync(string backupPath)
        {
            await Task.Run(() =>
            {
                using var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create);

                // Backup database
                if (File.Exists(_databasePath))
                {
                    archive.CreateEntryFromFile(_databasePath, "FinancialTracker.db");
                }

                // Backup data directory
                if (Directory.Exists(_dataDirectory))
                {
                    AddDirectoryToArchive(archive, _dataDirectory, "data");
                }

                // Backup configuration
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                if (File.Exists(configPath))
                {
                    archive.CreateEntryFromFile(configPath, "appsettings.json");
                }
            });
        }

        private async Task CreateUncompressedBackupAsync(string backupPath)
        {
            await Task.Run(() =>
            {
                // Copy database
                if (File.Exists(_databasePath))
                {
                    var dbBackupPath = Path.Combine(backupPath, "FinancialTracker.db");
                    File.Copy(_databasePath, dbBackupPath, true);
                }

                // Copy data directory
                if (Directory.Exists(_dataDirectory))
                {
                    var dataBackupPath = Path.Combine(backupPath, "data");
                    CopyDirectory(_dataDirectory, dataBackupPath);
                }

                // Copy configuration
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                if (File.Exists(configPath))
                {
                    var configBackupPath = Path.Combine(backupPath, "appsettings.json");
                    File.Copy(configPath, configBackupPath, true);
                }
            });
        }

        private void AddDirectoryToArchive(ZipArchive archive, string sourceDir, string entryName)
        {
            var files = Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories);
            foreach (var file in files)
            {
                var relativePath = Path.GetRelativePath(sourceDir, file);
                var entryPath = Path.Combine(entryName, relativePath).Replace('\\', '/');
                archive.CreateEntryFromFile(file, entryPath);
            }
        }

        private void CopyDirectory(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (var dir in Directory.GetDirectories(sourceDir))
            {
                var destSubDir = Path.Combine(destDir, Path.GetFileName(dir));
                CopyDirectory(dir, destSubDir);
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupPath)
        {
            // Starting backup restoration

            try
            {
                if (!File.Exists(backupPath) && !Directory.Exists(backupPath))
                {
                    // Backup not found
                    return false;
                }

                // Create backup of current state before restoration
                var currentBackupPath = await CreateBackupAsync();
                // Current state backed up

                if (backupPath.EndsWith(".zip"))
                {
                    await RestoreFromCompressedBackupAsync(backupPath);
                }
                else
                {
                    await RestoreFromUncompressedBackupAsync(backupPath);
                }

                // Backup restoration completed successfully
                return true;
            }
            catch (Exception)
            {
                // Failed to restore backup
                return false;
            }
        }

        private async Task RestoreFromCompressedBackupAsync(string backupPath)
        {
            await Task.Run(() =>
            {
                using var archive = ZipFile.OpenRead(backupPath);
                
                foreach (var entry in archive.Entries)
                {
                    var destPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, entry.FullName);
                    var destDir = Path.GetDirectoryName(destPath);
                    
                    if (!string.IsNullOrEmpty(destDir))
                    {
                        Directory.CreateDirectory(destDir);
                    }
                    
                    entry.ExtractToFile(destPath, true);
                }
            });
        }

        private async Task RestoreFromUncompressedBackupAsync(string backupPath)
        {
            await Task.Run(() =>
            {
                var baseDir = AppDomain.CurrentDomain.BaseDirectory;
                
                // Restore database
                var dbBackupPath = Path.Combine(backupPath, "FinancialTracker.db");
                if (File.Exists(dbBackupPath))
                {
                    File.Copy(dbBackupPath, _databasePath, true);
                }

                // Restore data directory
                var dataBackupPath = Path.Combine(backupPath, "data");
                if (Directory.Exists(dataBackupPath))
                {
                    if (Directory.Exists(_dataDirectory))
                    {
                        Directory.Delete(_dataDirectory, true);
                    }
                    CopyDirectory(dataBackupPath, _dataDirectory);
                }

                // Restore configuration
                var configBackupPath = Path.Combine(backupPath, "appsettings.json");
                if (File.Exists(configBackupPath))
                {
                    var configPath = Path.Combine(baseDir, "appsettings.json");
                    File.Copy(configBackupPath, configPath, true);
                }
            });
        }

        public Task CleanupOldBackupsAsync()
        {
            // Starting backup cleanup
            
            try
            {
                if (!Directory.Exists(_backupDirectory))
                    return Task.CompletedTask;

                var retentionDays = Config.Files.BackupRetentionDays;
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);

                var backupFiles = Directory.GetFiles(_backupDirectory, "FinancialTracker_Backup_*");
                var backupDirs = Directory.GetDirectories(_backupDirectory, "FinancialTracker_Backup_*");

                int deletedCount = 0;

                // Clean up old backup files
                foreach (var file in backupFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                }

                // Clean up old backup directories
                foreach (var dir in backupDirs)
                {
                    var dirInfo = new DirectoryInfo(dir);
                    if (dirInfo.CreationTime < cutoffDate)
                    {
                        Directory.Delete(dir, true);
                        deletedCount++;
                    }
                }

                // Backup cleanup completed
            }
            catch (Exception)
            {
                // Failed to cleanup old backups
            }

            return Task.CompletedTask;
        }

        public async Task<bool> AutoBackupAsync()
        {
            if (!Config.Files.AutoBackup)
            {
                return false;
            }

            try
            {
                await CreateBackupAsync();
                await CleanupOldBackupsAsync();
                return true;
            }
            catch (Exception)
            {
                // Auto backup failed
                return false;
            }
        }
    }
}
