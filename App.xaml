<Application x:Class="FinancialTracker.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:FinancialTracker.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Indigo" SecondaryColor="Teal" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:PaymentStatusToColorConverter x:Key="PaymentStatusToColorConverter"/>
            <converters:PaymentStatusConverter x:Key="PaymentStatusConverter"/>
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- File Attachment Color Converters -->
            <converters:FileAttachmentBackgroundConverter x:Key="FileAttachmentBackgroundConverter"/>
            <converters:FileAttachmentForegroundConverter x:Key="FileAttachmentForegroundConverter"/>
            <converters:FileAttachmentBorderConverter x:Key="FileAttachmentBorderConverter"/>
            <converters:LetterAttachmentBackgroundConverter x:Key="LetterAttachmentBackgroundConverter"/>
            <converters:LetterAttachmentForegroundConverter x:Key="LetterAttachmentForegroundConverter"/>
            <converters:LetterAttachmentBorderConverter x:Key="LetterAttachmentBorderConverter"/>
            <converters:CommitmentFileBackgroundConverter x:Key="CommitmentFileBackgroundConverter"/>
            <converters:CommitmentFileForegroundConverter x:Key="CommitmentFileForegroundConverter"/>
            <converters:CommitmentFileBorderConverter x:Key="CommitmentFileBorderConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
