#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace FinancialTracker.Models
{
    public class ProjectSite
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string SiteName { get; set; } = string.Empty;
        
        public int SiteOrder { get; set; } // For ordering
        
        public bool IsActive { get; set; } = true;
        
        // Navigation property
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;

        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

        // Computed properties for display
        [NotMapped]
        public int InvoicesCount => Invoices?.Count ?? 0;

        [NotMapped]
        public decimal TotalAmount => Invoices?.Sum(i => i.AmountUSD) ?? 0;
    }
}
