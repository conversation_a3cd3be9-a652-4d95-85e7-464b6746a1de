<Window x:Class="FinancialTracker.CommitmentInvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Commitment Invoices"
        Height="750"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F7F7F7">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Background="#4A5568" Padding="24,20" CornerRadius="6" Margin="0,0,0,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FileDocument" Width="36" Height="36" Margin="0,0,16,0" Foreground="White"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="CommitmentTitleText" Text="Commitment Invoices" FontSize="24" FontWeight="SemiBold" Foreground="White"/>
                        <TextBlock Text="Manage invoices linked to this commitment" FontSize="13" Foreground="#E5E7EB" Opacity="0.9" Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Refresh Data" Margin="12,0" Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="22" Height="22" Foreground="White"/>
                    </Button>
                    <Button Content="Close" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" BorderBrush="White" Margin="12,0" Padding="20,10" FontWeight="Medium" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Commitment Info -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Commitment Title" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentTitleDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="Type" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentTypeDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="Amount (USD)" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="CommitmentAmountDetail" Text="" FontSize="16" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3">
                    <TextBlock Text="Total Related Invoices" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="TotalInvoicesDetail" Text="" FontSize="16" FontWeight="Bold" Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Invoices List -->
        <Grid Grid.Row="2" Margin="16,8,16,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Related Invoices" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                <Button Grid.Column="1" Content="Refresh" Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0" Click="RefreshButton_Click"/>
                <Button Grid.Column="2" Content="Add New Invoice for this Commitment" Style="{StaticResource MaterialDesignRaisedButton}"
                        Click="AddInvoiceButton_Click"/>
            </Grid>
            
            <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal">
                <DataGrid.Resources>
                    <!-- Style for paid invoices -->
                    <Style TargetType="DataGridRow">
                        <Style.Triggers>
                            <!-- Fully Paid - Green -->
                            <DataTrigger Binding="{Binding IsFullyPaid}" Value="True">
                                <Setter Property="Background" Value="#ECFDF5"/>
                                <Setter Property="BorderBrush" Value="#10B981"/>
                                <Setter Property="BorderThickness" Value="0,0,4,0"/>
                            </DataTrigger>
                            <!-- Partially Paid - Orange -->
                            <DataTrigger Binding="{Binding IsPartiallyPaid}" Value="True">
                                <Setter Property="Background" Value="#FFFBEB"/>
                                <Setter Property="BorderBrush" Value="#F59E0B"/>
                                <Setter Property="BorderThickness" Value="0,0,4,0"/>
                            </DataTrigger>
                            <!-- Unpaid - Red -->
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding IsFullyPaid}" Value="False"/>
                                    <Condition Binding="{Binding IsPartiallyPaid}" Value="False"/>
                                </MultiDataTrigger.Conditions>
                                <Setter Property="Background" Value="#FEF2F2"/>
                                <Setter Property="BorderBrush" Value="#EF4444"/>
                                <Setter Property="BorderThickness" Value="0,0,4,0"/>
                            </MultiDataTrigger>
                            <!-- Hover effects -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F1F5F9"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Invoice No." Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="Amount (USD)" Binding="{Binding AmountUSD, StringFormat='{}{0:C}'}" Width="120"/>
                    <DataGridTextColumn Header="Exchange Rate" Binding="{Binding ExchangeRate, StringFormat='{}{0:F2}'}" Width="100"/>
                    <DataGridTextColumn Header="Invoice Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:dd/MM/yyyy}'}" Width="120"/>
                    <DataGridCheckBoxColumn Header="Paid" Binding="{Binding IsPaid}" Width="60"/>
                    <DataGridTextColumn Header="Paid Date" Binding="{Binding PaidDate, StringFormat='{}{0:dd/MM/yyyy}'}" Width="120"/>
                    <DataGridTemplateColumn Header="Description" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Description}"
                                           TextTrimming="CharacterEllipsis"
                                           ToolTip="{Binding Description}"
                                           ToolTipService.ShowDuration="30000"
                                           ToolTipService.InitialShowDelay="500"
                                           Padding="4"
                                           VerticalAlignment="Center"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="File" Binding="{Binding AttachedFileName}" Width="150"/>
                    <DataGridTemplateColumn Header="Actions" Width="250">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="EditInvoiceButton_Click"/>
                                    <Button Content="Open File" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="OpenInvoiceFileButton_Click"
                                            IsEnabled="{Binding AttachedFileName, Converter={StaticResource StringToBooleanConverter}}"/>
                                    <Button Content="Remove" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Margin="2" Padding="8,4" Click="DeleteInvoiceButton_Click"
                                            ToolTip="Remove invoice from this commitment"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- Summary -->
        <materialDesign:Card Grid.Row="3" Margin="16,0,16,16" Padding="16">
            <UniformGrid Columns="3">
                <StackPanel Margin="0,0,16,0">
                    <TextBlock Text="Total Invoice Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="TotalAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Margin="0,0,16,0">
                    <TextBlock Text="Paid Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="PaidAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="Green" Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel>
                    <TextBlock Text="Unpaid Amount" FontWeight="Medium" Opacity="0.7"/>
                    <TextBlock x:Name="UnpaidAmountText" Text="$0.00" FontSize="18" FontWeight="Bold" Foreground="Red" Margin="0,4,0,0"/>
                </StackPanel>
            </UniformGrid>
        </materialDesign:Card>
    </Grid>
</Window>
