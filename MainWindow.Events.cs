using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace FinancialTracker
{
    public partial class MainWindow : Window
    {
        // Developer info event handlers
        private void DeveloperInfo_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "https://mostafa-yassin.netlify.app/",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Could not open website: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void DeveloperInfo_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Border border)
            {
                border.Background = new SolidColorBrush(Color.FromRgb(79, 70, 229)); // Darker indigo on hover
            }
        }

        private void DeveloperInfo_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (sender is Border border)
            {
                border.Background = new SolidColorBrush(Color.FromRgb(99, 102, 241)); // Back to original indigo
            }
        }
    }
}
