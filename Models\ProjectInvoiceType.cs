#nullable enable
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    public class ProjectInvoiceType
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string TypeName { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(5,2)")]
        public decimal DefaultPercentage { get; set; } = 0; // Default percentage

        public int TypeOrder { get; set; } // For ordering
        
        public bool IsActive { get; set; } = true;
        
        // Navigation property
        [ForeignKey("ProjectId")]
        public virtual Project Project { get; set; } = null!;
    }
}
