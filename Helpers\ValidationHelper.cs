#nullable enable
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace FinancialTracker.Helpers
{
    public static class ValidationHelper
    {
        #region String Validation

        /// <summary>
        /// Validates that a string is not null, empty, or whitespace
        /// </summary>
        public static ValidationResult ValidateRequired(string? value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value))
                return ValidationResult.Error($"{fieldName} is required.");
            
            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates string length
        /// </summary>
        public static ValidationResult ValidateLength(string? value, string fieldName, int minLength = 0, int maxLength = int.MaxValue)
        {
            if (string.IsNullOrEmpty(value))
                return minLength > 0 ? ValidationResult.Error($"{fieldName} is required.") : ValidationResult.Success();

            if (value.Length < minLength)
                return ValidationResult.Error($"{fieldName} must be at least {minLength} characters long.");

            if (value.Length > maxLength)
                return ValidationResult.Error($"{fieldName} cannot exceed {maxLength} characters.");

            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates that a string contains only safe characters (prevents injection attacks)
        /// </summary>
        public static ValidationResult ValidateSafeString(string? value, string fieldName)
        {
            if (string.IsNullOrEmpty(value))
                return ValidationResult.Success();

            // Check for potentially dangerous characters
            var dangerousPatterns = new[]
            {
                @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>", // Script tags
                @"javascript:", // JavaScript protocol
                @"vbscript:", // VBScript protocol
                @"on\w+\s*=", // Event handlers
                @"expression\s*\(", // CSS expressions
                @"--", // SQL comment
                @"\/\*", // SQL block comment start
                @"\*\/", // SQL block comment end
                @";\s*(drop|delete|insert|update|create|alter|exec|execute)\s+", // SQL commands
            };

            foreach (var pattern in dangerousPatterns)
            {
                if (Regex.IsMatch(value, pattern, RegexOptions.IgnoreCase))
                    return ValidationResult.Error($"{fieldName} contains invalid characters.");
            }

            return ValidationResult.Success();
        }

        #endregion

        #region Numeric Validation

        /// <summary>
        /// Validates decimal values
        /// </summary>
        public static ValidationResult ValidateDecimal(string? value, string fieldName, decimal min = decimal.MinValue, decimal max = decimal.MaxValue, bool required = false)
        {
            if (string.IsNullOrWhiteSpace(value))
                return required ? ValidationResult.Error($"{fieldName} is required.") : ValidationResult.Success();

            if (!decimal.TryParse(value, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal result))
                return ValidationResult.Error($"{fieldName} must be a valid number.");

            if (result < min)
                return ValidationResult.Error($"{fieldName} must be at least {min:F2}.");

            if (result > max)
                return ValidationResult.Error($"{fieldName} cannot exceed {max:F2}.");

            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates integer values
        /// </summary>
        public static ValidationResult ValidateInteger(string? value, string fieldName, int min = int.MinValue, int max = int.MaxValue, bool required = false)
        {
            if (string.IsNullOrWhiteSpace(value))
                return required ? ValidationResult.Error($"{fieldName} is required.") : ValidationResult.Success();

            if (!int.TryParse(value, out int result))
                return ValidationResult.Error($"{fieldName} must be a valid whole number.");

            if (result < min)
                return ValidationResult.Error($"{fieldName} must be at least {min}.");

            if (result > max)
                return ValidationResult.Error($"{fieldName} cannot exceed {max}.");

            return ValidationResult.Success();
        }

        #endregion

        #region Date Validation

        /// <summary>
        /// Validates date values
        /// </summary>
        public static ValidationResult ValidateDate(DateTime? value, string fieldName, DateTime? minDate = null, DateTime? maxDate = null, bool required = false)
        {
            if (!value.HasValue)
                return required ? ValidationResult.Error($"{fieldName} is required.") : ValidationResult.Success();

            if (minDate.HasValue && value < minDate)
                return ValidationResult.Error($"{fieldName} cannot be earlier than {minDate.Value:yyyy-MM-dd}.");

            if (maxDate.HasValue && value > maxDate)
                return ValidationResult.Error($"{fieldName} cannot be later than {maxDate.Value:yyyy-MM-dd}.");

            return ValidationResult.Success();
        }

        #endregion

        #region File Validation

        /// <summary>
        /// Validates file paths and extensions
        /// </summary>
        public static ValidationResult ValidateFilePath(string? filePath, string fieldName, string[]? allowedExtensions = null)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return ValidationResult.Success();

            try
            {
                // Check for path traversal attacks
                var fullPath = System.IO.Path.GetFullPath(filePath);
                if (filePath.Contains("..") || filePath.Contains("~"))
                    return ValidationResult.Error($"{fieldName} contains invalid path characters.");

                // Validate extension if specified
                if (allowedExtensions != null && allowedExtensions.Length > 0)
                {
                    var extension = System.IO.Path.GetExtension(filePath).ToLowerInvariant();
                    if (!allowedExtensions.Contains(extension))
                        return ValidationResult.Error($"{fieldName} must have one of these extensions: {string.Join(", ", allowedExtensions)}");
                }

                return ValidationResult.Success();
            }
            catch
            {
                return ValidationResult.Error($"{fieldName} is not a valid file path.");
            }
        }

        #endregion

        #region Business Logic Validation

        /// <summary>
        /// Validates exchange rate
        /// </summary>
        public static ValidationResult ValidateExchangeRate(decimal rate)
        {
            if (rate <= 0)
                return ValidationResult.Error("Exchange rate must be greater than zero.");

            if (rate > 1000)
                return ValidationResult.Error("Exchange rate seems unusually high. Please verify.");

            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates percentage values
        /// </summary>
        public static ValidationResult ValidatePercentage(decimal percentage, string fieldName)
        {
            if (percentage < 0)
                return ValidationResult.Error($"{fieldName} cannot be negative.");

            if (percentage > 100)
                return ValidationResult.Error($"{fieldName} cannot exceed 100%.");

            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates that payment terms percentages sum to 100%
        /// </summary>
        public static ValidationResult ValidatePaymentTermsTotal(IEnumerable<decimal> percentages, string category)
        {
            var total = percentages.Sum();
            var tolerance = 0.01m; // Allow small rounding differences

            if (Math.Abs(total - 100) > tolerance)
                return ValidationResult.Error($"{category} payment terms must total 100%. Current total: {total:F2}%");

            return ValidationResult.Success();
        }

        #endregion

        #region Composite Validation

        /// <summary>
        /// Validates multiple conditions and returns all errors
        /// </summary>
        public static ValidationResult ValidateAll(params ValidationResult[] results)
        {
            var errors = results.Where(r => !r.IsValid).SelectMany(r => r.Errors).ToList();
            return errors.Any() ? ValidationResult.Error(errors) : ValidationResult.Success();
        }

        #endregion
    }

    /// <summary>
    /// Represents the result of a validation operation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; private set; }
        public List<string> Errors { get; private set; } = new List<string>();

        private ValidationResult(bool isValid, List<string> errors)
        {
            IsValid = isValid;
            Errors = errors;
        }

        public static ValidationResult Success() => new ValidationResult(true, new List<string>());
        
        public static ValidationResult Error(string error) => new ValidationResult(false, new List<string> { error });
        
        public static ValidationResult Error(List<string> errors) => new ValidationResult(false, errors);

        public string GetErrorMessage() => string.Join("\n", Errors);
    }
}
