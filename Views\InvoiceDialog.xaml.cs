#nullable enable
#define DEBUG_LOGGING // Comment this line to disable all debug logging

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using FinancialTracker.Models;
using FinancialTracker.Helpers;
using FinancialTracker.Services;

namespace FinancialTracker
{
    public partial class InvoiceDialog : Window
    {
        private Invoice? _invoice;
        private bool _isEdit;
        private string _selectedFilePath = string.Empty;
        private string _selectedLetterPath = string.Empty;
        private int? _preSelectedProjectId;
        private List<Commitment> _availableCommitments = new List<Commitment>();
        private int? _preSelectedCommitmentId;
        private Project? _currentProject;
        private List<ProjectSite> _projectSites = new List<ProjectSite>();
        private List<ProjectPaymentTerm> _projectPaymentTerms = new List<ProjectPaymentTerm>();
        private ProjectPaymentTerm? _selectedPaymentTerm = null;
        private List<InvoicePaymentTerm> _selectedInvoicePaymentTerms = new List<InvoicePaymentTerm>();
        private List<ProjectPaymentTerm> _availablePaymentTerms = new List<ProjectPaymentTerm>();
        private List<int> _existingPaymentTermIds = new List<int>();

        // Track original values for change detection
        private string? _originalInvoiceType = null;
        private int? _originalCommitmentId = null;

        // Track if user manually edited paid amount
        private bool _userEditedPaidAmount = false;

        // Track if type change is programmatic (not user-initiated)
        private bool _isProgrammaticTypeChange = false;

        // Step completion tracking
        private bool _step1Complete = false;
        private bool _step2Complete = false;
        private bool _step3Complete = false;
        private bool _step4Complete = false;
        private bool _step5Complete = false;
        private bool _step6Complete = false;

        public InvoiceDialog(Invoice? invoice = null, int? projectId = null, int? commitmentId = null)
        {
            InitializeComponent();

            _invoice = invoice;
            // Treat as edit only if an existing invoice (with Id) is provided
            _isEdit = invoice != null && invoice.Id > 0;
            _preSelectedProjectId = projectId;
            _preSelectedCommitmentId = commitmentId;



            // Only clear stored data for new invoices, not for editing
            if (!_isEdit)
            {
                // Clear any cached/stored data to ensure fresh start for new invoices
                ClearStoredData();
            }

            // Project is now pre-selected and fixed
            CommitmentComboBox.SelectionChanged += CommitmentComboBox_SelectionChanged;

            if (_isEdit && _invoice != null)
            {
                Title = "Edit Invoice";
                // Load data and populate fields for editing
                _ = LoadDataAndPopulateForEdit();
            }
            else
            {
                Title = "Add New Invoice";

                // Load data for new invoice
                _ = LoadData();

                // Initialize for new invoice
            }

            // Initialize step validation - but don't enforce step restrictions for editing
            ValidateAllSteps();
        }

        #region Step Validation and UI Updates

        private void ValidateAllSteps()
        {
            ValidateStep1();
            ValidateStep2();
            ValidateStep3();
            ValidateStep4();
            ValidateStep5();
            ValidateStep6();

            // Always make all steps visible and enabled
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void ValidateStep1()
        {
            _step1Complete = !string.IsNullOrWhiteSpace(InvoiceNumberTextBox?.Text) &&
                           (_preSelectedProjectId.HasValue || (_isEdit && _invoice != null));

            UpdateStepCompletionUI(1, _step1Complete);
        }

        private void ValidateStep2()
        {
            _step2Complete = TypeComboBox?.SelectedItem != null;
            UpdateStepCompletionUI(2, _step2Complete);
        }

        private void ValidateStep3()
        {
            _step3Complete = _step2Complete && (
                (AllSitesRadio?.IsChecked == true) ||
                (SingleSiteRadio?.IsChecked == true && SiteSelectionComboBox?.SelectedItem != null)
            );
            UpdateStepCompletionUI(3, _step3Complete);
        }

        private void ValidateStep4()
        {
            bool hasValidAmount = false;

            if (FixedAmountRadio?.IsChecked == true)
            {
                hasValidAmount = decimal.TryParse(AmountTextBox?.Text, out decimal amount) && amount > 0;
            }
            else if (PercentageRadio?.IsChecked == true)
            {
                hasValidAmount = _selectedInvoicePaymentTerms?.Any() == true;
            }

            _step4Complete = _step3Complete && hasValidAmount;
            UpdateStepCompletionUI(4, _step4Complete);
        }

        private void ValidateStep5()
        {
            _step5Complete = _step4Complete &&
                           decimal.TryParse(ExchangeRateTextBox?.Text, out decimal rate) && rate > 0;
            UpdateStepCompletionUI(5, _step5Complete);
        }

        private void ValidateStep6()
        {
            // Step 6 is optional, so it's always considered complete
            _step6Complete = true;
            UpdateStepCompletionUI(6, _step6Complete);
        }

        private void UpdateStepCompletionUI(int stepNumber, bool isComplete)
        {
            var icon = FindName($"Step{stepNumber}CompleteIcon") as FrameworkElement;
            var text = FindName($"Step{stepNumber}CompleteText") as FrameworkElement;

            if (icon != null && text != null)
            {
                icon.Visibility = isComplete ? Visibility.Visible : Visibility.Collapsed;
                text.Visibility = isComplete ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        private void UpdateStepVisibility()
        {
            // Make all steps available regardless of completion status
            // This removes the sequential step requirement
            UpdateStepCardState("Step2Card", true);
            UpdateStepCardState("Step3Card", true);
            UpdateStepCardState("Step4Card", true);
            UpdateStepCardState("Step5Card", true);
            UpdateStepCardState("Step6Card", true);
        }

        private void UpdateStepCardState(string cardName, bool isEnabled)
        {
            var card = FindName(cardName) as FrameworkElement;
            if (card != null)
            {
                card.Opacity = isEnabled ? 1.0 : 0.5;
                card.IsEnabled = isEnabled;
            }
        }

        private void UpdateSaveButtonState()
        {
            var saveButton = FindName("SaveButton") as Button;
            if (saveButton != null)
            {
                // Basic validation for both new and edit modes
                bool hasInvoiceNumber = !string.IsNullOrWhiteSpace(InvoiceNumberTextBox?.Text);
                bool hasType = TypeComboBox?.SelectedItem != null;
                bool hasValidAmount = decimal.TryParse(AmountTextBox?.Text, out decimal amount) && amount > 0;
                bool hasValidExchangeRate = decimal.TryParse(ExchangeRateTextBox?.Text, out decimal rate) && rate > 0;

                // For percentage-based invoices, check if payment terms are selected
                bool hasValidPaymentSettings = true;
                if (PercentageRadio?.IsChecked == true)
                {
                    hasValidPaymentSettings = _selectedInvoicePaymentTerms?.Any() == true;
                }

                bool canSave = hasInvoiceNumber && hasType && hasValidAmount && hasValidExchangeRate && hasValidPaymentSettings;

                saveButton.IsEnabled = canSave;

                // Update final validation text
                var validationText = FindName("FinalValidationText") as TextBlock;
                if (validationText != null)
                {
                    if (canSave)
                    {
                        validationText.Text = _isEdit ? "✓ Ready to save changes!" : "✓ All required information completed. Ready to save!";
                        validationText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                    }
                    else
                    {
                        var missingFields = new List<string>();
                        if (!hasInvoiceNumber) missingFields.Add("Invoice Number");
                        if (!hasType) missingFields.Add("Invoice Type");
                        if (!hasValidAmount) missingFields.Add("Valid Amount");
                        if (!hasValidExchangeRate) missingFields.Add("Exchange Rate");
                        if (!hasValidPaymentSettings) missingFields.Add("Payment Terms");

                        validationText.Text = $"⚠ Please fill required fields: {string.Join(", ", missingFields)}";
                        validationText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                    }
                }

                // Update final summary
                UpdateFinalSummaryDisplay();
            }
        }

        private void UpdateFinalSummaryDisplay()
        {
            var summaryDisplay = FindName("FinalSummaryDisplay") as TextBlock;
            if (summaryDisplay != null && _step1Complete && _step2Complete && _step4Complete)
            {
                try
                {
                    var projectName = _currentProject?.Name ?? "Selected Project";
                    var invoiceType = (TypeComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "Unknown Type";
                    var amount = AmountTextBox?.Text ?? "0";
                    var siteMode = AllSitesRadio?.IsChecked == true ? "All Sites" : "Single Site";

                    summaryDisplay.Text = $"📋 {projectName} | 🏷️ {invoiceType} | 💰 ${amount} USD | 📍 {siteMode}";
                }
                catch
                {
                    summaryDisplay.Text = "Invoice summary will be updated as you complete the form";
                }
            }
        }

        #endregion



        private async System.Threading.Tasks.Task LoadCurrentProject()
        {
            try
            {
                int? projectId = _isEdit ? _invoice?.ProjectId : _preSelectedProjectId;
                if (projectId.HasValue)
                {
                    _currentProject = await App.DataService.GetProjectByIdAsync(projectId.Value);
                    if (_currentProject == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Warning: Project with ID {projectId.Value} not found in database");
                    }
                    else
                    {


                        // If percentage radio is already checked, reload payment terms now that project is loaded
                        if (PercentageRadio?.IsChecked == true)
                        {

                            UpdatePaymentTermsVisibility();
                        }
                    }
                }
                else
                {
                    _currentProject = null;
                    System.Diagnostics.Debug.WriteLine("Warning: No project ID provided - _currentProject set to null");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading current project: {ex.Message}");
                _currentProject = null;
            }
        }



        private async void CommitmentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            await UpdateTypeFromCommitment();
        }

        private async System.Threading.Tasks.Task UpdateTypeFromCommitment()
        {
            try
            {
                if (CommitmentComboBox.SelectedValue is int commitmentId)
                {
                    var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                    if (commitment != null)
                    {
                        // Update invoice type to match commitment type (but allow user to change it)
                        SetTypeFromCommitmentType(commitment.Type);

                        // Keep type field editable - user can change it if needed
                        TypeComboBox.IsEnabled = true;
                    }
                }
                else
                {
                    // If no commitment is selected, make type field editable
                    TypeComboBox.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating type from commitment: {ex.Message}");
            }
        }

        private void SetTypeFromCommitmentType(string commitmentType)
        {
            // Mark as programmatic change to prevent confirmation dialog
            _isProgrammaticTypeChange = true;

            // Search for the type in the dropdown list
            bool typeFound = false;
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                var itemTag = item.Tag?.ToString();
                var itemContent = item.Content?.ToString();

                if (itemTag == commitmentType || itemContent == commitmentType)
                {
                    TypeComboBox.SelectedItem = item;
                    typeFound = true;
                    break;
                }
            }

            // If type not found, default to first item
            if (!typeFound && TypeComboBox.Items.Count > 0)
            {
                TypeComboBox.SelectedIndex = 0;
            }

            // Reset flag
            _isProgrammaticTypeChange = false;
        }

        private async void TypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                // Skip if this is a programmatic change (loading invoice data)
                if (_isProgrammaticTypeChange)
                {
                    return;
                }

                string newType = selectedItem.Content?.ToString() ?? "";

                // Check if this is an edit mode and type has changed
                if (_isEdit && !string.IsNullOrEmpty(_originalInvoiceType) && _originalCommitmentId.HasValue && newType != _originalInvoiceType)
                {
                    var result = MessageBox.Show(
                        $"Changing invoice type from '{_originalInvoiceType}' to '{newType}' will:\n\n" +
                        "• Unlink the invoice from the current commitment\n" +
                        "• Show available commitments for the new type\n" +
                        "• You may need to select a new appropriate commitment\n\n" +
                        "Do you want to continue?",
                        "Confirm Invoice Type Change",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.No)
                    {
                        // Revert to original type
                        _isProgrammaticTypeChange = true;
                        SetTypeFromInvoiceType(_originalInvoiceType);
                        _isProgrammaticTypeChange = false;
                        return;
                    }
                    else
                    {
                        // User confirmed - clear the commitment selection
                        CommitmentComboBox.SelectedIndex = -1;
                        _originalCommitmentId = null; // Clear original commitment since user confirmed the change

                    }
                }

                // Apply template if it's one of the new types
                ApplyInvoiceTemplate(selectedItem.Tag?.ToString() ?? "");

                // Update commitments based on selected type
                await LoadCommitmentsForProject();

                // Clear previous payment terms selection when type changes
                ClearPaymentTermsSelection();

                // Update payment terms when invoice type changes
                UpdatePaymentTermsVisibility();

                // If percentage is selected, also load payment terms directly
                if (PercentageRadio?.IsChecked == true)
                {
                    System.Diagnostics.Debug.WriteLine("Percentage is checked, loading payment terms directly");
                    _ = LoadAvailablePaymentTerms();
                }

                // Update available sites based on selected type
                _ = UpdateAvailableSites();

                // Validate steps after type selection
                ValidateStep2();
                ValidateStep3(); // Site selection might be affected
                UpdateStepVisibility();
                UpdateSaveButtonState();
            }
        }









        private void PaymentTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"PaymentTypeRadio_Checked called. Sender: {(sender as RadioButton)?.Name}");

            // Check if controls are loaded
            var fixedAmountPanel = FindName("FixedAmountPanel") as FrameworkElement;
            var percentagePanel = FindName("PercentagePanel") as FrameworkElement;

            if (fixedAmountPanel == null || percentagePanel == null) return;

            var fixedAmountRadio = FindName("FixedAmountRadio") as RadioButton;
            var percentageRadio = FindName("PercentageRadio") as RadioButton;

            if (fixedAmountRadio?.IsChecked == true)
            {
                System.Diagnostics.Debug.WriteLine("Fixed amount radio selected");
                fixedAmountPanel.Visibility = Visibility.Visible;
                percentagePanel.Visibility = Visibility.Collapsed;
            }
            else if (percentageRadio?.IsChecked == true)
            {
                System.Diagnostics.Debug.WriteLine("Percentage radio selected - loading payment terms");
                fixedAmountPanel.Visibility = Visibility.Collapsed;
                percentagePanel.Visibility = Visibility.Visible;

                // Load payment terms when percentage is selected
                UpdatePaymentTermsVisibility();

                // Update the right side summary display
                UpdateSelectedTermsSummary();
            }

            UpdateFinalSummary();
            ValidateStep4();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        // Payment Terms Details Summary Functions

        private void UpdateFixedAmountSummary()
        {
            try
            {
                var summaryTextBlock = FindName("FixedAmountSummaryText") as TextBlock;
                if (summaryTextBlock != null)
                {
                    if (decimal.TryParse(AmountTextBox?.Text, out decimal amount) && amount > 0)
                    {
                        summaryTextBlock.Text = $"Fixed amount: ${amount:F2} USD - No percentage-based payment terms";
                    }
                    else
                    {
                        summaryTextBlock.Text = "Fixed amount payment - Enter amount above";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating fixed amount summary: {ex.Message}");
            }
        }

        private void UpdateMultipleTermsSummary()
        {
            try
            {
                var summaryTextBlock = FindName("MultipleTermsSummaryText") as TextBlock;
                if (summaryTextBlock != null)
                {
                    if (_selectedInvoicePaymentTerms.Any())
                    {
                        var totalPercentage = _selectedInvoicePaymentTerms.Sum(ipt => ipt.DisplayPercentage);
                        var totalAmount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);
                        var termsCount = _selectedInvoicePaymentTerms.Count;

                        summaryTextBlock.Text = $"{termsCount} payment terms selected - Total: {totalPercentage}% (${totalAmount:F2} USD)";
                    }
                    else
                    {
                        summaryTextBlock.Text = "Select payment terms below to see calculation details";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating multiple terms summary: {ex.Message}");
            }
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateFinalSummary();
            ValidateStep4();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void FixedAmountDetailsTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateFinalSummary();
            UpdateSaveButtonState();
        }

        private void InvoiceNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateStep1();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void ExchangeRateTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateStep5();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void PaidAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Mark that user has manually edited the paid amount
            // This prevents auto-update from overwriting user's manual changes
            if (sender is TextBox textBox && !string.IsNullOrEmpty(textBox.Text))
            {
                // Only mark as user-edited if the value is different from the calculated amount
                if (_selectedInvoicePaymentTerms.Any())
                {
                    var calculatedAmount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);
                    if (decimal.TryParse(textBox.Text, out decimal paidAmount))
                    {
                        // Allow small rounding differences (0.01)
                        if (Math.Abs(paidAmount - calculatedAmount) > 0.01m)
                        {
                            _userEditedPaidAmount = true;
                        }
                    }
                }
            }
        }

        private async void CalculateAmountFromPercentage()
        {
            // Check if controls are loaded
            if (PercentageRadio == null || PercentageRadio.IsChecked != true) return;

            try
            {
                if (_currentProject == null)
                {
                    await LoadCurrentProject();
                }

                // Don't clear paid amount during edit mode - preserve existing values
                if (!_isEdit)
                {
                    // Only clear paid amount for new invoices
                    PaidAmountTextBox.Text = "";
                }
                // For edit mode, keep the existing paid amount value
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating percentage: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task ValidateProjectPercentageTotal(decimal newPercentage)
        {
            try
            {
                int? projectId = _isEdit ? _invoice?.ProjectId : _preSelectedProjectId;
                if (projectId.HasValue)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    var projectInvoices = allInvoices.Where(i => i.ProjectId == projectId.Value && i.IsPercentageBased).ToList();

                    // Exclude current invoice if editing
                    if (_isEdit && _invoice != null)
                    {
                        projectInvoices = projectInvoices.Where(i => i.Id != _invoice.Id).ToList();
                    }

                    var totalExistingPercentage = projectInvoices.Sum(i => i.PaymentPercentage);
                    var totalWithNew = totalExistingPercentage + newPercentage;

                    if (totalWithNew > 100)
                    {
                        MessageBox.Show($"Warning: Total percentage for this project will be {totalWithNew:F1}% (exceeds 100%).\n" +
                                      $"Existing invoices use {totalExistingPercentage:F1}% of the project equipment amount.",
                                      "Percentage Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating percentage total: {ex.Message}");
            }
        }

        private string GetSelectedType()
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Content?.ToString() ?? "Hardware & Software Equipment";
            }
            return "Hardware & Software Equipment";
        }

        private async Task LoadData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"LoadData called. _preSelectedProjectId: {_preSelectedProjectId}, _isEdit: {_isEdit}");

                // Load current project
                await LoadCurrentProject();

                // Set title if project is pre-selected
                if (_preSelectedProjectId.HasValue && _currentProject != null)
                {
                    Title = $"Add New Invoice - {_currentProject.Name}";
                }

                // Load invoice types based on current project
                RestoreDefaultInvoiceTypes();

                // Load commitments for the selected project
                await LoadCommitmentsForProject();

                // Pre-select commitment if provided (after commitments are loaded)
                if (_preSelectedCommitmentId.HasValue)
                {
                    CommitmentComboBox.SelectedValue = _preSelectedCommitmentId.Value;
                }

                // Load project sites and payment terms
                try
                {
                    await LoadProjectSites();
                    await LoadProjectPaymentTerms();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Could not load project data: {ex.Message}");
                }

                // Set default values for required fields to make form more user-friendly
                if (ExchangeRateTextBox != null && string.IsNullOrEmpty(ExchangeRateTextBox.Text))
                {
                    ExchangeRateTextBox.Text = "1.0000";
                }

                // Update site settings visibility
                UpdateSiteSettingsVisibility();

                // تحديث حالة Payment Terms حسب اختيار الموقع
                UpdatePaymentTermsVisibility();

                // Validate all steps after loading
                ValidateAllSteps();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async System.Threading.Tasks.Task LoadCommitmentsForProject()
        {
            try
            {
                int? selectedProjectId = _isEdit ? _invoice?.ProjectId : _preSelectedProjectId;
                if (selectedProjectId.HasValue)
                {
                    var allCommitments = await App.DataService.GetCommitmentsAsync();
                    var projectCommitments = allCommitments.Where(c => c.ProjectId == selectedProjectId.Value).ToList();

                    // Filter commitments based on selected invoice type
                    if (TypeComboBox.SelectedItem is ComboBoxItem selectedTypeItem)
                    {
                        var selectedType = selectedTypeItem.Content?.ToString();
                        if (!string.IsNullOrEmpty(selectedType))
                        {
                            projectCommitments = FilterCommitmentsByType(projectCommitments, selectedType);
                        }
                    }

                    CommitmentComboBox.ItemsSource = projectCommitments;
                }
                else
                {
                    CommitmentComboBox.ItemsSource = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading commitments: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Commitment> FilterCommitmentsByType(List<Commitment> commitments, string invoiceType)
        {
            try
            {
                return commitments.Where(c =>
                {
                    // If commitment type is not set, show it for all invoice types
                    if (string.IsNullOrWhiteSpace(c.Type))
                        return true;

                    // Match commitment type with invoice type
                    if (invoiceType == "SERVICES" || invoiceType.Contains("Service"))
                    {
                        return c.Type.Equals("SERVICES", StringComparison.OrdinalIgnoreCase) ||
                               c.Type.ToLower().Contains("service");
                    }
                    else if (invoiceType == "SW & HW (Software & Hardware)" || invoiceType.Contains("Equipment"))
                    {
                        return c.Type.Equals("SW & HW (Software & Hardware)", StringComparison.OrdinalIgnoreCase) ||
                               c.Type.ToLower().Contains("equipment") ||
                               c.Type.ToLower().Contains("task") ||
                               c.Type.ToLower().Contains("software") ||
                               c.Type.ToLower().Contains("hardware");
                    }
                    else if (invoiceType.Contains("Spare Parts"))
                    {
                        return c.Type.ToLower().Contains("spare") || c.Type.ToLower().Contains("part");
                    }

                    return false; // Default: show none if no specific match
                }).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error filtering commitments: {ex.Message}");
                return commitments; // Return all if filtering fails
            }
        }

        private async System.Threading.Tasks.Task PopulateFieldsAsync()
        {
            if (_invoice == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: Starting to populate fields for invoice {_invoice.Id}");

                // Load basic invoice data
                InvoiceNumberTextBox.Text = _invoice.InvoiceNumber;
                // Project is fixed for this invoice

                // Load current project
                await LoadCurrentProject();

                // Load commitments for the project
                await LoadCommitmentsForProject();
                CommitmentComboBox.SelectedValue = _invoice.CommitmentId;

                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: Invoice Type = {_invoice.Type}, IsPercentageBased = {_invoice.IsPercentageBased}");

                // FIRST set payment type to show correct panels
                if (_invoice.IsPercentageBased)
                {
                    System.Diagnostics.Debug.WriteLine("PopulateFieldsAsync: Setting PercentageRadio");
                    PercentageRadio.IsChecked = true;
                    PaymentTypeRadio_Checked(PercentageRadio, new RoutedEventArgs());
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PopulateFieldsAsync: Setting FixedAmountRadio");
                    FixedAmountRadio.IsChecked = true;
                    PaymentTypeRadio_Checked(FixedAmountRadio, new RoutedEventArgs());
                }

                // Wait for UI to update
                await System.Threading.Tasks.Task.Delay(100);

                // Force UI update
                Dispatcher.Invoke(() => { }, System.Windows.Threading.DispatcherPriority.Render);

                // THEN set amounts and dates after panels are visible
                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: About to set AmountTextBox.Text to {_invoice.AmountUSD:F2}");
                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: AmountTextBox is null? {AmountTextBox == null}");
                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: AmountTextBox.IsEnabled? {AmountTextBox?.IsEnabled}");
                System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: AmountTextBox.Visibility? {AmountTextBox?.Visibility}");

                if (AmountTextBox != null)
                {
                    AmountTextBox.Text = _invoice.AmountUSD.ToString("F2");
                    System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: After setting - AmountTextBox.Text = '{AmountTextBox.Text}'");
                }

                if (ExchangeRateTextBox != null)
                {
                    ExchangeRateTextBox.Text = _invoice.ExchangeRate.ToString("F4");
                    System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: After setting - ExchangeRateTextBox.Text = '{ExchangeRateTextBox.Text}'");
                }

                if (PaidAmountTextBox != null)
                {
                    PaidAmountTextBox.Text = _invoice.PaidAmount.ToString("F2");
                    System.Diagnostics.Debug.WriteLine($"PopulateFieldsAsync: After setting - PaidAmountTextBox.Text = '{PaidAmountTextBox.Text}'");
                }

                // Load existing payment terms for percentage-based invoices
                if (_invoice.IsPercentageBased)
                {
                    await LoadExistingPaymentTerms();
                }

                ArrivalDatePicker.SelectedDate = _invoice.ArrivalDate;
                SignatureDatePicker.SelectedDate = _invoice.SignatureDate;
                DescriptionTextBox.Text = _invoice.Description;
                if (FixedAmountDetailsTextBox != null)
                    FixedAmountDetailsTextBox.Text = _invoice.FixedAmountDetails;

                // Load type and file information
                await PopulateTypeFields();

                // Load and set the saved site
                if (_currentProject != null)
                {
                    await LoadProjectSites();

                    // For editing, show all sites (not just available ones)
                    // because the current invoice should be able to keep its site
                    if (_isEdit)
                    {
                        UpdateSiteSelectionComboBox(_projectSites);
                    }

                    // Set site application mode based on saved invoice
                    var singleSiteRadio = FindName("SingleSiteRadio") as RadioButton;
                    var allSitesRadio = FindName("AllSitesRadio") as RadioButton;

                    if (_invoice.AppliesAllSites)
                    {
                        allSitesRadio?.SetCurrentValue(RadioButton.IsCheckedProperty, true);
                    }
                    else
                    {
                        singleSiteRadio?.SetCurrentValue(RadioButton.IsCheckedProperty, true);

                        if (_invoice.ProjectSiteId.HasValue)
                        {
                            // Find and select the saved site
                            foreach (ComboBoxItem item in SiteSelectionComboBox.Items)
                            {
                                if (item.Tag is int siteId && siteId == _invoice.ProjectSiteId.Value)
                                {
                                    SiteSelectionComboBox.SelectedItem = item;
                                    break;
                                }
                            }
                        }
                    }

                    // Update panels visibility
                    UpdateSiteApplicationPanels();

                    // Set payment terms site application mode for percentage-based invoices
                    if (_invoice.IsPercentageBased && _invoice.InvoicePaymentTerms?.Any() == true)
                    {
                        var paymentTermsSingleSiteRadio = FindName("PaymentTermsSingleSiteRadio") as RadioButton;
                        var paymentTermsAllSitesRadio = FindName("PaymentTermsAllSitesRadio") as RadioButton;

                        if (_invoice.AppliesAllSites)
                        {
                            paymentTermsAllSitesRadio?.SetCurrentValue(RadioButton.IsCheckedProperty, true);
                        }
                        else
                        {
                            paymentTermsSingleSiteRadio?.SetCurrentValue(RadioButton.IsCheckedProperty, true);
                        }

                        UpdatePaymentTermsSiteCalculationText();
                    }
                }

                // Update payment terms details summary
                if (FixedAmountRadio.IsChecked == true)
                {
                    UpdateFixedAmountSummary();
                }
                else if (PercentageRadio.IsChecked == true)
                {
                    UpdateMultipleTermsSummary();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error populating invoice fields: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task PopulateTypeFields()
        {
            if (_invoice == null) return;

            // Store original values for change detection
            _originalInvoiceType = _invoice.Type;
            _originalCommitmentId = _invoice.CommitmentId;

            // If invoice is linked to a commitment, update type from commitment
            if (_invoice.CommitmentId.HasValue)
            {
                await UpdateTypeFromCommitment();
            }
            else
            {
                // If not linked to a commitment, display the saved type
                SetTypeFromInvoiceType(_invoice.Type);
            }

            // Always ensure type field is editable
            TypeComboBox.IsEnabled = true;

            if (!string.IsNullOrEmpty(_invoice.AttachedFileName))
            {
                AttachedFilePathTextBox.Text = _invoice.AttachedFileName;
            }

            if (!string.IsNullOrEmpty(_invoice.LetterFileName))
            {
                LetterFilePathTextBox.Text = _invoice.LetterFileName;
            }
        }

        private void SetTypeFromInvoiceType(string invoiceType)
        {
            System.Diagnostics.Debug.WriteLine($"SetTypeFromInvoiceType: Looking for type '{invoiceType}'");
            System.Diagnostics.Debug.WriteLine($"SetTypeFromInvoiceType: TypeComboBox has {TypeComboBox.Items.Count} items");

            // Mark as programmatic change to prevent confirmation dialog
            _isProgrammaticTypeChange = true;

            // Set type - simplified for new type system
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                var itemContent = item.Content?.ToString();
                System.Diagnostics.Debug.WriteLine($"SetTypeFromInvoiceType: Checking item '{itemContent}'");

                if (itemContent == invoiceType)
                {
                    TypeComboBox.SelectedItem = item;
                    System.Diagnostics.Debug.WriteLine($"SetTypeFromInvoiceType: Found and selected type '{invoiceType}'");
                    _isProgrammaticTypeChange = false;
                    return;
                }
            }

            System.Diagnostics.Debug.WriteLine($"SetTypeFromInvoiceType: Type '{invoiceType}' not found in ComboBox");

            // If type not found, try to find a reasonable default
            if (TypeComboBox.Items.Count > 0)
            {
                // Try to find "Hardware & Software Equipment" first
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "Hardware & Software Equipment")
                    {
                        TypeComboBox.SelectedItem = item;
                        _isProgrammaticTypeChange = false;
                        return;
                    }
                }

                // If not found, try "Equipment"
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "Equipment")
                    {
                        TypeComboBox.SelectedItem = item;
                        _isProgrammaticTypeChange = false;
                        return;
                    }
                }

                // Last resort: select first item
                TypeComboBox.SelectedIndex = 0;
            }

            // Reset flag in case no item was selected
            _isProgrammaticTypeChange = false;
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedFilePath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                AttachedFilePathTextBox.Text = System.IO.Path.GetFileName(_selectedFilePath);
            }
        }

        private void BrowseFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedFilePath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                AttachedFilePathTextBox.Text = System.IO.Path.GetFileName(_selectedFilePath);
                ValidateStep6();
                UpdateStepVisibility();
                UpdateSaveButtonState();
            }
        }

        private void SelectLetterButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Image Files (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedLetterPath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedLetterPath))
            {
                LetterFilePathTextBox.Text = System.IO.Path.GetFileName(_selectedLetterPath);
            }
        }

        private void ViewLetterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string letterPath = string.Empty;

                // If editing and letter exists, use the existing letter path
                if (_isEdit && _invoice != null && !string.IsNullOrEmpty(_invoice.LetterFilePath))
                {
                    letterPath = _invoice.LetterFilePath;
                }
                // If new letter selected, use the selected path
                else if (!string.IsNullOrEmpty(_selectedLetterPath))
                {
                    letterPath = _selectedLetterPath;
                }

                if (!string.IsNullOrEmpty(letterPath) && System.IO.File.Exists(letterPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = letterPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("Cannot find the letter file.", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening letter: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    MessageBox.Show("Invoice number is required.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_preSelectedProjectId == null && !_isEdit)
                {
                    MessageBox.Show("Project must be specified.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }



                decimal amount = 0;
                if (!decimal.TryParse(AmountTextBox.Text, out amount) || amount <= 0)
                {
                    // Try to auto-calculate if percentage-based
                    if (PercentageRadio?.IsChecked == true && _selectedPaymentTerm != null)
                    {
                        try
                        {
                            CalculateAmountFromPaymentTerm(_selectedPaymentTerm.Percentage);
                            if (decimal.TryParse(AmountTextBox.Text, out amount) && amount > 0)
                            {
                                // Amount was calculated successfully
                            }
                            else
                            {
                                MessageBox.Show("Please enter a valid amount.", "Validation Error",
                                    MessageBoxButton.OK, MessageBoxImage.Warning);
                                return;
                            }
                        }
                        catch
                        {
                            MessageBox.Show("Please enter a valid amount.", "Validation Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.Show("Please enter a valid amount.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
                {
                    MessageBox.Show("Please enter a valid exchange rate.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }



                if (!decimal.TryParse(PaidAmountTextBox.Text, out decimal paidAmount) || paidAmount < 0)
                {
                    paidAmount = 0;
                }



                if (paidAmount > amount)
                {
                    MessageBox.Show("Paid amount cannot be greater than total amount.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }





                // Validate percentage if percentage-based
                decimal paymentPercentage = 0;
                bool isPercentageBased = PercentageRadio.IsChecked == true;
                bool hasMultiplePaymentTerms = _selectedInvoicePaymentTerms.Any();

                if (isPercentageBased)
                {
                    if (hasMultiplePaymentTerms)
                    {
                        // Validate multiple payment terms
                        paymentPercentage = _selectedInvoicePaymentTerms.Sum(ipt => ipt.AppliedPercentage);

                        if (paymentPercentage <= 0 || paymentPercentage > 100)
                        {
                            MessageBox.Show("Total percentage must be between 1-100%.", "Validation Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        // Use the calculated amount from multiple payment terms
                        amount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);
                        AmountTextBox.Text = amount.ToString("F2");
                    }
                    else
                    {
                        // For percentage-based invoices without multiple payment terms selected,
                        // we need to use the amount from the AmountTextBox
                        if (!decimal.TryParse(AmountTextBox.Text, out amount) || amount <= 0)
                        {
                            MessageBox.Show("Please enter a valid amount or select payment terms.", "Validation Error",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        // Set a default percentage for display purposes
                        paymentPercentage = 100; // This will be overridden by the legacy system if needed
                    }
                }

                // Check if commitment has amount (if commitment is selected)
                if (CommitmentComboBox.SelectedValue is int commitmentId)
                {
                    try
                    {
                        var commitment = await App.DataService.GetCommitmentByIdAsync(commitmentId);
                        if (commitment != null && commitment.AmountUSD <= 0)
                        {
                            MessageBox.Show("Cannot add an invoice to this commitment because it does not contain a financial amount.", "Warning",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error verifying commitment: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                string? attachedFilePath = null;
                string? attachedFileName = null;
                string? letterFilePath = null;
                string? letterFileName = null;

                // Handle file attachment
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    attachedFileName = System.IO.Path.GetFileName(_selectedFilePath);
                    attachedFilePath = await App.FileService.SaveFileAsync(_selectedFilePath, "invoice", attachedFileName);
                }

                // Handle letter attachment
                if (!string.IsNullOrEmpty(_selectedLetterPath))
                {
                    letterFileName = System.IO.Path.GetFileName(_selectedLetterPath);
                    letterFilePath = await App.FileService.SaveFileAsync(_selectedLetterPath, "letters", letterFileName);
                }

                if (_isEdit && _invoice != null)
                {
                    _invoice.InvoiceNumber = InvoiceNumberTextBox.Text.Trim();
                    // ProjectId remains unchanged during edit
                    _invoice.CommitmentId = CommitmentComboBox.SelectedValue as int?;
                    _invoice.AmountUSD = amount;
                    _invoice.ExchangeRate = exchangeRate;
                    _invoice.PaidAmount = paidAmount;
                    _invoice.InvoiceDate = DateTime.Now.Date; // Set to current date automatically
                    _invoice.ArrivalDate = ArrivalDatePicker.SelectedDate?.Date;
                    _invoice.SignatureDate = SignatureDatePicker.SelectedDate?.Date;
                    _invoice.Description = DescriptionTextBox.Text.Trim();
                    _invoice.FixedAmountDetails = FixedAmountDetailsTextBox?.Text?.Trim() ?? string.Empty;
                    _invoice.IsPaid = paidAmount >= amount; // Auto-set based on paid amount
                    _invoice.PaidDate = paidAmount > 0 ? DateTime.Now : null;



                    // Set percentage data
                    _invoice.IsPercentageBased = isPercentageBased;
                    _invoice.PaymentPercentage = paymentPercentage;

                    // Set site settings
                    var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
                    bool isAllSitesMode = allSitesRadio?.IsChecked == true;

                    // For percentage-based invoices, check payment terms site application
                    if (isPercentageBased && hasMultiplePaymentTerms)
                    {
                        bool isPaymentTermsAllSitesMode = IsPaymentTermsAllSitesMode();
                        _invoice.AppliesAllSites = isPaymentTermsAllSitesMode;
                    }
                    else
                    {
                        _invoice.AppliesAllSites = isAllSitesMode;
                    }

                    if (isAllSitesMode)
                    {
                        // All Sites mode - set to null to indicate it applies to all sites
                        _invoice.ProjectSiteId = null;
                        _invoice.SiteName = "All Sites Combined";
                    }
                    else if (SiteSelectionComboBox.SelectedItem is ComboBoxItem selectedSite && selectedSite.Tag is int siteId)
                    {
                        // Single Site mode
                        _invoice.ProjectSiteId = siteId;
                        _invoice.SiteName = selectedSite.Content?.ToString() ?? "";
                    }
                    else
                    {
                        _invoice.ProjectSiteId = null;
                        _invoice.SiteName = "Default Site";
                    }

                    // Set type - simplified
                    if (TypeComboBox.SelectedItem is ComboBoxItem selectedTypeItem)
                    {
                        var selectedType = selectedTypeItem.Content?.ToString() ?? "Equipment";
                        _invoice.Type = selectedType;
                        System.Diagnostics.Debug.WriteLine($"Saving invoice with type: {selectedType}");

                        // Additional validation for debugging
                        if (selectedType == "Spare Parts")
                        {
                            System.Diagnostics.Debug.WriteLine("✓ Spare Parts invoice type confirmed");
                        }
                    }
                    else
                    {
                        _invoice.Type = "Equipment";
                        System.Diagnostics.Debug.WriteLine("No type selected, defaulting to Equipment");
                    }

                    if (attachedFilePath != null)
                    {
                        _invoice.AttachedFilePath = attachedFilePath;
                        _invoice.AttachedFileName = attachedFileName;
                    }

                    if (letterFilePath != null)
                    {
                        _invoice.LetterFilePath = letterFilePath;
                        _invoice.LetterFileName = letterFileName;
                    }

                    await App.DataService.UpdateInvoiceAsync(_invoice);

                    // Update multiple payment terms if selected
                    if (hasMultiplePaymentTerms && _selectedInvoicePaymentTerms.Any())
                    {
                        await UpdateInvoicePaymentTerms(_invoice.Id);
                    }
                    else if (isPercentageBased && !hasMultiplePaymentTerms)
                    {
                        // If switching from multiple terms to single percentage, delete existing terms
                        await App.DataService.DeleteInvoicePaymentTermsAsync(_invoice.Id);
                    }
                }
                else
                {
                    var invoiceType = GetSelectedType();
                    System.Diagnostics.Debug.WriteLine($"Saving new invoice with Type: '{invoiceType}'");

                    var newInvoice = new Invoice
                    {
                        InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
                        ProjectId = _preSelectedProjectId ?? 0,
                        CommitmentId = CommitmentComboBox.SelectedValue as int?,
                        AmountUSD = amount,
                        ExchangeRate = exchangeRate,
                        PaidAmount = paidAmount, // Use the validated paid amount
                        InvoiceDate = DateTime.Now.Date, // Set to current date automatically
                        ArrivalDate = ArrivalDatePicker.SelectedDate?.Date,
                        SignatureDate = SignatureDatePicker.SelectedDate?.Date,
                        Description = DescriptionTextBox.Text.Trim(),
                        FixedAmountDetails = FixedAmountDetailsTextBox?.Text?.Trim() ?? string.Empty,
                        IsPaid = paidAmount >= amount, // Auto-set based on paid amount
                        PaidDate = paidAmount > 0 ? DateTime.Now : null,
                        Type = invoiceType,
                        AttachedFilePath = attachedFilePath,
                        AttachedFileName = attachedFileName,
                        LetterFilePath = letterFilePath,
                        LetterFileName = letterFileName,
                        CreatedDate = DateTime.Now,

                        IsPercentageBased = isPercentageBased,
                        PaymentPercentage = paymentPercentage,

                        // Set site settings
                        AppliesAllSites = GetAppliesAllSitesForNewInvoice(isPercentageBased, hasMultiplePaymentTerms),
                        ProjectSiteId = GetSelectedSiteId(),
                        SiteName = GetSelectedSiteName()
                    };



                    var savedInvoice = await App.DataService.AddInvoiceAsync(newInvoice);

                    // Save multiple payment terms if selected
                    if (hasMultiplePaymentTerms && _selectedInvoicePaymentTerms.Any())
                    {
                        foreach (var invoicePaymentTerm in _selectedInvoicePaymentTerms)
                        {
                            // Create a clean copy to avoid tracking issues
                            var cleanPaymentTerm = new InvoicePaymentTerm
                            {
                                InvoiceId = savedInvoice.Id,
                                ProjectPaymentTermId = invoicePaymentTerm.ProjectPaymentTermId,
                                AppliedPercentage = invoicePaymentTerm.AppliedPercentage,
                                CalculatedAmount = invoicePaymentTerm.CalculatedAmount,
                                DisplayOrder = invoicePaymentTerm.DisplayOrder,
                                Notes = invoicePaymentTerm.Notes,
                                ProjectSiteId = invoicePaymentTerm.ProjectSiteId,
                                SiteName = invoicePaymentTerm.SiteName,
                                CreatedDate = DateTime.Now,
                                IsActive = true
                            };

                            // Validate the payment term before saving
                            if (cleanPaymentTerm.ProjectPaymentTermId <= 0)
                            {
                                throw new InvalidOperationException($"Invalid ProjectPaymentTermId: {cleanPaymentTerm.ProjectPaymentTermId}");
                            }

                            if (cleanPaymentTerm.InvoiceId <= 0)
                            {
                                throw new InvalidOperationException($"Invalid InvoiceId: {cleanPaymentTerm.InvoiceId}");
                            }

                            await App.DataService.AddInvoicePaymentTermAsync(cleanPaymentTerm);
                        }
                    }
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error saving invoice: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nInner Exception: {ex.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async System.Threading.Tasks.Task LoadProjectSites()
        {
            if (_currentProject == null) return;

            try
            {
                // Load project sites
                _projectSites = await App.DataService.GetProjectSitesAsync(_currentProject.Id);
                System.Diagnostics.Debug.WriteLine($"Loaded {_projectSites?.Count ?? 0} sites for project {_currentProject.Id}");

                // Update UI - will be updated when payment term is selected
                UpdateSiteSelectionComboBox(_projectSites ?? new List<ProjectSite>());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading project sites: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task UpdateAvailableSites()
        {
            if (_currentProject == null)
            {
                UpdateSiteSelectionComboBox(_projectSites);
                return;
            }

            try
            {
                // Get the currently selected invoice type
                var selectedInvoiceType = "";
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedInvoiceType = selectedItem.Content?.ToString() ?? "SW & HW";
                }
                else
                {
                    selectedInvoiceType = "SW & HW";
                }

                // Get selected payment terms
                if (!_selectedInvoicePaymentTerms.Any())
                {
                    // No payment terms selected, show all sites
                    UpdateSiteSelectionComboBox(_projectSites);
                    return;
                }

                // Get payment term descriptions
                var paymentTermDescriptions = _selectedInvoicePaymentTerms
                    .Select(pt => pt.ProjectPaymentTerm?.Description ?? "")
                    .Where(desc => !string.IsNullOrEmpty(desc))
                    .ToList();

                // Get available sites for all selected payment terms
                var availableSites = await SitePercentageCalculationService.GetAvailableSitesForPaymentTerms(
                    _currentProject.Id, selectedInvoiceType, paymentTermDescriptions);

                // Update site selection using the new smart calculation service
                UpdateSiteSelectionComboBox(availableSites);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating available sites: {ex.Message}");
                // Fallback to all sites
                UpdateSiteSelectionComboBox(_projectSites);
            }
        }

        private async void UpdateSiteSelectionComboBox(List<ProjectSite> availableSites)
        {
            // Store current selection
            var currentSelection = SiteSelectionComboBox.SelectedItem as ComboBoxItem;
            var currentSiteId = currentSelection?.Tag as int?;

            SiteSelectionComboBox.Items.Clear();

            // Get selected invoice type for percentage calculation
            var selectedInvoiceType = "";
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                selectedInvoiceType = selectedItem.Content?.ToString() ?? "SW & HW";
            }

            foreach (var site in availableSites)
            {
                // Calculate completion info for tooltip
                var completionInfo = await GetSiteCompletionInfo(site.Id, selectedInvoiceType);

                var item = new ComboBoxItem
                {
                    Content = $"{site.SiteName}",
                    Tag = site.Id,
                    ToolTip = $"الموقع: {site.SiteName}\n{completionInfo}"
                };
                SiteSelectionComboBox.Items.Add(item);
            }

            // Update info text
            var projectSiteInfoText = FindName("ProjectSiteInfoText") as TextBlock;
            if (projectSiteInfoText != null)
            {
                if (availableSites.Count == 0)
                {
                    projectSiteInfoText.Text = "All sites have invoices for this payment term";
                }
                else if (availableSites.Count < _projectSites.Count)
                {
                    var usedCount = _projectSites.Count - availableSites.Count;
                    projectSiteInfoText.Text = $"Select site for this invoice ({usedCount} site(s) already have this payment term)";
                }
                else
                {
                    projectSiteInfoText.Text = "Select the site for this invoice";
                }
            }

            // Try to restore previous selection first
            if (currentSiteId.HasValue)
            {
                foreach (ComboBoxItem item in SiteSelectionComboBox.Items)
                {
                    if (item.Tag is int siteId && siteId == currentSiteId.Value)
                    {
                        SiteSelectionComboBox.SelectedItem = item;
                        return;
                    }
                }
            }

            // Only select first available site if no previous selection and not in edit mode
            if (!_isEdit && SiteSelectionComboBox.Items.Count > 0)
            {
                SiteSelectionComboBox.SelectedIndex = 0;
            }
        }

        private async System.Threading.Tasks.Task LoadProjectPaymentTerms()
        {
            if (_currentProject == null) return;

            try
            {
                // Load payment terms for both categories
                var servicesTerms = await App.DataService.GetProjectPaymentTermsAsync(_currentProject.Id, "Services");
                var equipmentTerms = await App.DataService.GetProjectPaymentTermsAsync(_currentProject.Id, "Equipment");
                var tasksTerms = await App.DataService.GetProjectPaymentTermsAsync(_currentProject.Id, "Tasks"); // For backward compatibility

                _projectPaymentTerms.Clear();
                _projectPaymentTerms.AddRange(servicesTerms);
                _projectPaymentTerms.AddRange(equipmentTerms);
                _projectPaymentTerms.AddRange(tasksTerms); // For backward compatibility

                // Payment terms will be updated when invoice type is selected
                // Refresh payment terms display if already visible
                if (PercentageRadio?.IsChecked == true)
                {
                    UpdatePaymentTermsVisibility();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading payment terms: {ex.Message}");
                // Fallback to default types
                RestoreDefaultInvoiceTypes();
            }
        }

        private void UpdatePaymentTermsVisibility()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"UpdatePaymentTermsVisibility called. PercentageRadio.IsChecked: {PercentageRadio?.IsChecked}, _currentProject: {(_currentProject != null ? $"{_currentProject.Name} (ID: {_currentProject.Id})" : "NULL")}");

                // Check if percentage-based payment is selected
                if (PercentageRadio?.IsChecked == true && _currentProject != null)
                {
                    // Show percentage panel and load payment terms
                    if (PercentagePanel != null)
                    {
                        PercentagePanel.Visibility = Visibility.Visible;
                    }

                    // Load available payment terms based on selected invoice type
                    _ = LoadAvailablePaymentTerms();
                }
                else
                {
                    // Hide percentage panel for fixed amount
                    if (PercentagePanel != null)
                    {
                        PercentagePanel.Visibility = Visibility.Collapsed;
                    }

                    if (PercentageRadio?.IsChecked == true && _currentProject == null)
                    {
                        System.Diagnostics.Debug.WriteLine("Warning: Percentage radio is checked but _currentProject is null - payment terms cannot be loaded");
                    }
                }

                // Update panel visibility based on payment type
                if (FixedAmountRadio?.IsChecked == true)
                {
                    if (FixedAmountPanel != null) FixedAmountPanel.Visibility = Visibility.Visible;
                    if (PercentagePanel != null) PercentagePanel.Visibility = Visibility.Collapsed;
                }
                else if (PercentageRadio?.IsChecked == true)
                {
                    if (FixedAmountPanel != null) FixedAmountPanel.Visibility = Visibility.Collapsed;
                    if (PercentagePanel != null) PercentagePanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdatePaymentTermsVisibility: {ex.Message}");
            }
        }



        private void CalculateAmountFromPaymentTerm(decimal percentage)
        {
            try
            {
                if (_currentProject != null)
                {
                    // Get the selected invoice type
                    string selectedType = "";
                    if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                    {
                        selectedType = selectedItem.Content?.ToString() ?? "";
                    }

                    // Determine which amount to use based on invoice type
                    decimal baseAmount = 0;
                    string amountType = "";

                    if (selectedType == "Equipment" || selectedType == "Hardware & Software Equipment" || selectedType == "Hardware & Software Tasks")
                    {
                        baseAmount = _currentProject.DistributableTasksAmount;
                        amountType = "Equipment";
                    }
                    else if (selectedType == "Equipment (Software)" || selectedType == "Software Equipment" || selectedType == "Software Tasks")
                    {
                        baseAmount = _currentProject.SoftwareTasksAmount;
                        amountType = "Software Equipment";
                    }
                    else if (selectedType == "Equipment (Hardware)" || selectedType == "Hardware Equipment" || selectedType == "Hardware Tasks")
                    {
                        baseAmount = _currentProject.HardwareTasksAmount;
                        amountType = "Hardware Equipment";
                    }
                    else if (selectedType == "Services")
                    {
                        baseAmount = _currentProject.ServicesAmount;
                        amountType = "Services";
                    }
                    else if (selectedType == "Spare Parts")
                    {
                        baseAmount = _currentProject.SparePartsAmount;
                        amountType = "Spare Parts";
                    }
                    else
                    {
                        baseAmount = _currentProject.DistributableTasksAmount;
                        amountType = "Equipment";
                    }

                    decimal calculatedAmount = baseAmount * (percentage / 100);
                    decimal originalAmount = calculatedAmount;

                    // Debug before division
                    System.Diagnostics.Debug.WriteLine($"Before division: {calculatedAmount:F2}");
                    System.Diagnostics.Debug.WriteLine($"Number of sites: {_currentProject.NumberOfSites}");

                    // Check if "All Sites" option is selected
                    var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
                    bool isAllSitesMode = allSitesRadio?.IsChecked == true;

                    // Adjust for multiple sites only if Single Site mode is selected
                    if (_currentProject.NumberOfSites > 1 && !isAllSitesMode)
                    {
                        calculatedAmount = calculatedAmount / _currentProject.NumberOfSites;
                        System.Diagnostics.Debug.WriteLine($"After division by {_currentProject.NumberOfSites}: {calculatedAmount:F2}");
                    }
                    else if (isAllSitesMode)
                    {

                    }



                    // Update the amount textbox (not paid amount)
                    AmountTextBox.Text = calculatedAmount.ToString("F2");

                    System.Diagnostics.Debug.WriteLine($"Final calculated amount for {selectedType}: {calculatedAmount:F2} from {amountType} amount: {baseAmount:F2}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating amount: {ex.Message}");

                // Don't modify paid amount in case of error - leave it as is
                // AmountTextBox.Text = "0"; // Let user enter amount manually if calculation fails
            }
        }

        // Restore default invoice types
        private void RestoreDefaultInvoiceTypes()
        {
            try
            {
                if (TypeComboBox == null) return;

                TypeComboBox.Items.Clear();

                // Default types will be set based on project split config
                var types = new List<string>();
                if (_currentProject?.SplitTasksIntoHardwareAndSoftware == true)
                {
                    types.Add("Equipment (Software)");
                    types.Add("Equipment (Hardware)");
                }
                else
                {
                    types.Add("Equipment");
                }
                types.Add("Services");

                // Add Spare Parts only if the project has spare parts amount > 0
                System.Diagnostics.Debug.WriteLine($"Project: {_currentProject?.Name ?? "NULL"}, ManualSparePartsAmount: {_currentProject?.ManualSparePartsAmount}");

                if (_currentProject?.ManualSparePartsAmount > 0)
                {
                    types.Add("Spare Parts");
                    System.Diagnostics.Debug.WriteLine("Added Spare Parts to invoice types");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Spare Parts NOT added - project has no spare parts amount");
                }

                // Add Extra only if the project has extra amount > 0
                System.Diagnostics.Debug.WriteLine($"Project: {_currentProject?.Name ?? "NULL"}, ManualExtraAmount: {_currentProject?.ManualExtraAmount}");

                if (_currentProject?.ManualExtraAmount > 0)
                {
                    // Use custom name if available, otherwise use "Extra"
                    string extraName = !string.IsNullOrWhiteSpace(_currentProject.ExtraCategoryName)
                        ? _currentProject.ExtraCategoryName
                        : "Extra";
                    types.Add(extraName);
                    System.Diagnostics.Debug.WriteLine($"Added {extraName} to invoice types");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Extra NOT added - project has no extra amount");
                }

                types.Add("Other");

                foreach (var t in types)
                {
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = t });
                }

                System.Diagnostics.Debug.WriteLine($"Loaded {types.Count} invoice types for project: {_currentProject?.Name ?? "Unknown"}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading invoice types: {ex.Message}");
            }
        }

        private void UpdateSiteSettingsVisibility()
        {
            try
            {
                if (_currentProject?.NumberOfSites > 1 && PercentageRadio?.IsChecked == true)
                {
                    // Check if "All Sites Combined" is selected in Payment Terms
                    bool isPaymentTermsAllSitesMode = IsPaymentTermsAllSitesMode();

                    if (isPaymentTermsAllSitesMode)
                    {

                    }
                    else
                    {

                        var projectSiteInfoText = FindName("ProjectSiteInfoText") as TextBlock;
                        if (projectSiteInfoText != null && _currentProject != null)
                        {
                            projectSiteInfoText.Text = $"Project '{_currentProject.Name}' has {_currentProject.NumberOfSites} sites";
                        }
                        UpdateSiteApplicationPanels();
                    }
                }
                else
                {

                }

                // Update payment terms visibility when project changes
                UpdatePaymentTermsVisibility();

                // Update payment terms site calculation text
                UpdatePaymentTermsSiteCalculationText();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateSiteSettingsVisibility: {ex.Message}");
            }
        }

        private void SiteApplicationRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSiteApplicationPanels();
            UpdateFinalSummary();
            ValidateStep2();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void SiteRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSiteApplicationPanels();
            UpdateFinalSummary();
            ValidateStep2();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        private void UpdateFinalSummary()
        {
            try
            {
                var finalSummaryText = FindName("FinalSummaryText") as TextBlock;
                var amountCalculationText = FindName("AmountCalculationText") as TextBlock;
                var percentageCalculationText = FindName("PercentageCalculationText") as TextBlock;

                if (finalSummaryText == null) return;

                var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
                var fixedAmountRadio = FindName("FixedAmountRadio") as RadioButton;
                var amountTextBox = FindName("AmountTextBox") as TextBox;

                bool isAllSites = allSitesRadio?.IsChecked == true;
                bool isFixedAmount = fixedAmountRadio?.IsChecked == true;

                if (decimal.TryParse(amountTextBox?.Text, out decimal amount) && amount > 0)
                {
                    if (isAllSites)
                    {
                        var sitesCount = _currentProject?.NumberOfSites ?? 1;
                        var amountPerSite = amount / sitesCount;
                        finalSummaryText.Text = $"💰 Total: ${amount:N0} | 🏗️ All {sitesCount} Sites | 📊 ${amountPerSite:N0} per site";

                        if (amountCalculationText != null)
                            amountCalculationText.Text = $"Amount will be divided: ${amountPerSite:N0} per site × {sitesCount} sites";
                    }
                    else
                    {
                        finalSummaryText.Text = $"💰 Amount: ${amount:N0} | 🏗️ Single Site | 📋 {(isFixedAmount ? "Fixed Amount" : "Multiple Terms")}";

                        if (amountCalculationText != null)
                            amountCalculationText.Text = "Full amount applies to selected site";
                    }
                }
                else
                {
                    finalSummaryText.Text = "Enter amount to see calculation";
                    if (amountCalculationText != null)
                        amountCalculationText.Text = "Enter the total amount for this invoice";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateFinalSummary: {ex.Message}");
            }
        }



        private void UpdateSiteApplicationPanels()
        {
            if (_currentProject == null) return;

            // Get references to the panels
            var singleSitePanel = FindName("SingleSiteSelectionPanel") as StackPanel;
            var allSitesPanel = FindName("AllSitesInfoPanel") as StackPanel;
            var singleSiteRadio = FindName("SingleSiteRadio") as RadioButton;
            var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
            var siteCalculationText = FindName("SiteCalculationText") as TextBlock;
            var allSitesCalculationText = FindName("AllSitesCalculationText") as TextBlock;
            var siteSelectionComboBox = FindName("SiteSelectionComboBox") as ComboBox;

            if (singleSiteRadio?.IsChecked == true)
            {
                // Single Site Mode - Show site selection ComboBox
                if (singleSitePanel != null) singleSitePanel.Visibility = Visibility.Visible;
                if (allSitesPanel != null) allSitesPanel.Visibility = Visibility.Collapsed;
                if (siteSelectionComboBox != null) siteSelectionComboBox.Visibility = Visibility.Visible;

                var percentage = 100.0m / _currentProject.NumberOfSites;
                if (siteCalculationText != null)
                    siteCalculationText.Text = $"Per Site: {percentage:F1}% of the total percentage (divided by {_currentProject.NumberOfSites} sites)";
            }
            else if (allSitesRadio?.IsChecked == true)
            {
                // All Sites Mode - Hide site selection ComboBox
                if (singleSitePanel != null) singleSitePanel.Visibility = Visibility.Collapsed;
                if (allSitesPanel != null) allSitesPanel.Visibility = Visibility.Visible;
                if (siteSelectionComboBox != null) siteSelectionComboBox.Visibility = Visibility.Collapsed;

                if (allSitesCalculationText != null)
                    allSitesCalculationText.Text = $"Full percentage will be applied to all {_currentProject.NumberOfSites} sites combined";
            }
        }

        private int? GetSelectedSiteId()
        {
            var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
            bool isAllSitesMode = allSitesRadio?.IsChecked == true;

            if (isAllSitesMode)
            {
                return null; // All Sites mode
            }
            else if (SiteSelectionComboBox.SelectedItem is ComboBoxItem selectedSite && selectedSite.Tag is int siteId)
            {
                return siteId; // Single Site mode
            }

            return null;
        }

        private string GetSelectedSiteName()
        {
            // Check if multiple sites are used in payment terms
            if (_selectedInvoicePaymentTerms.Any())
            {
                var uniqueSites = _selectedInvoicePaymentTerms
                    .Where(ipt => !string.IsNullOrEmpty(ipt.SiteName))
                    .Select(ipt => ipt.SiteName)
                    .Distinct()
                    .ToList();

                // If multiple sites are used, return "All Sites"
                if (uniqueSites.Count > 1)
                {
                    return "All Sites";
                }
                else if (uniqueSites.Count == 1)
                {
                    return uniqueSites.First()!;
                }
            }

            var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
            bool isAllSitesMode = allSitesRadio?.IsChecked == true;

            if (isAllSitesMode)
            {
                return "All Sites Combined";
            }
            else if (SiteSelectionComboBox.SelectedItem is ComboBoxItem selectedSite)
            {
                return selectedSite.Content?.ToString() ?? "Default Site";
            }

            return "Default Site";
        }

        private bool IsAllSitesMode()
        {
            var allSitesRadio = FindName("AllSitesRadio") as RadioButton;
            return allSitesRadio?.IsChecked == true;
        }

        private void PaymentTermsSiteApplicationRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdatePaymentTermsSiteCalculationText();
            CalculateAmountsFromMultiplePaymentTerms();

            // Update Site Selection visibility based on Payment Terms choice
            UpdateSiteSettingsVisibility();
        }

        private void UpdatePaymentTermsSiteCalculationText()
        {
            if (_currentProject == null) return;

            try
            {
                var paymentTermsSingleSiteRadio = FindName("PaymentTermsSingleSiteRadio") as RadioButton;
                var paymentTermsAllSitesRadio = FindName("PaymentTermsAllSitesRadio") as RadioButton;
                var paymentTermsSiteCalculationText = FindName("PaymentTermsSiteCalculationText") as TextBlock;

                if (paymentTermsSiteCalculationText == null) return;

                if (paymentTermsAllSitesRadio?.IsChecked == true)
                {
                    paymentTermsSiteCalculationText.Text = $"Full percentages will be applied to all {_currentProject.NumberOfSites} sites combined";
                }
                else if (paymentTermsSingleSiteRadio?.IsChecked == true)
                {
                    paymentTermsSiteCalculationText.Text = $"Percentages will be divided by {_currentProject.NumberOfSites} sites";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdatePaymentTermsSiteCalculationText: {ex.Message}");
            }
        }

        private bool IsPaymentTermsAllSitesMode()
        {
            try
            {
                var paymentTermsAllSitesRadio = FindName("PaymentTermsAllSitesRadio") as RadioButton;
                return paymentTermsAllSitesRadio?.IsChecked == true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in IsPaymentTermsAllSitesMode: {ex.Message}");
                return false;
            }
        }

        private bool GetAppliesAllSitesForNewInvoice(bool isPercentageBased, bool hasMultiplePaymentTerms)
        {
            // For percentage-based invoices with multiple payment terms, check payment terms mode
            if (isPercentageBased && hasMultiplePaymentTerms)
            {
                return IsPaymentTermsAllSitesMode();
            }

            // For other cases, use the general site setting
            return IsAllSitesMode();
        }

        private void SiteSelectionComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Recalculate amount when site selection changes
            CalculateAmountFromPercentage();

            // Validate steps after site selection
            ValidateStep3();
            UpdateStepVisibility();
            UpdateSaveButtonState();
        }

        #region Multiple Payment Terms Support

        private async Task LoadAvailablePaymentTerms()
        {
            if (_currentProject == null)
            {
                System.Diagnostics.Debug.WriteLine("LoadAvailablePaymentTerms: _currentProject is null - cannot load payment terms");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"LoadAvailablePaymentTerms: Loading payment terms for project '{_currentProject.Name}' (ID: {_currentProject.Id})");

            try
            {
                // Get the selected invoice type
                string selectedType = "";
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedType = selectedItem.Content?.ToString() ?? "";
                }

                // Map invoice type to payment term category
                // IMPORTANT: When project is split, use exact category names (Software Equipment, Hardware Equipment)
                // When project is NOT split, use "Equipment" category
                string category = selectedType switch
                {
                    // New naming convention
                    "Equipment (Software)" => "Software Equipment",
                    "Equipment (Hardware)" => "Hardware Equipment",
                    "Equipment" => "Equipment",

                    // Legacy Split Equipment terminology - use exact names to match payment terms
                    "Software Equipment" => "Software Equipment",
                    "Hardware Equipment" => "Hardware Equipment",
                    "Hardware & Software Equipment" => "Equipment",

                    // Spare Parts - has its own category
                    "Spare Parts" => "Spare Parts",

                    // Services
                    "Services" => "Services",
                    "SERVICES" => "Services",
                    "Professional Services" => "Services",

                    // Legacy support for old terminology - map to Equipment
                    "Dell HW Sizing (VMware Enterprise Plus)" => "Equipment",
                    "SW License and Operating System" => "Equipment",
                    "SW & HW (Software & Hardware)" => "Equipment",
                    "Tasks" => "Equipment",
                    "Software Tasks" => "Software Equipment", // Map to Software Equipment if split
                    "Hardware Tasks" => "Hardware Equipment", // Map to Hardware Equipment if split
                    "Hardware & Software Tasks" => "Equipment",

                    // Check if it matches the custom extra category name
                    _ when !string.IsNullOrWhiteSpace(_currentProject?.ExtraCategoryName) &&
                           selectedType.Equals(_currentProject.ExtraCategoryName, StringComparison.OrdinalIgnoreCase) => _currentProject.ExtraCategoryName,

                    // Default Extra
                    "Extra" => "Extra",

                    _ => "Equipment" // Default fallback to Equipment
                };

                System.Diagnostics.Debug.WriteLine($"Loading payment terms for type: '{selectedType}' -> category: '{category}'");

                if (!string.IsNullOrEmpty(category) && _currentProject != null)
                {
                    _availablePaymentTerms = await App.DataService.GetAvailablePaymentTermsForInvoiceAsync(_currentProject.Id, category);

                    System.Diagnostics.Debug.WriteLine($"Found {_availablePaymentTerms?.Count ?? 0} payment terms for category '{category}'");

                    // لا ننشئ أي شروط دفع افتراضية - المستخدم يجب أن يدخل شروط الدفع بنفسه
                    if (_availablePaymentTerms?.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"No payment terms found for category: {category}. User must add payment terms manually.");
                    }

                    await PopulatePaymentTermsForNewDesign(); // Updated for new design
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"No category mapping found for type: '{selectedType}'");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading payment terms: {ex.Message}");
                MessageBox.Show($"Error loading payment terms: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task CreateDefaultPaymentTermsIfNeeded(string category)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Creating default payment terms for category: {category}");

                // لا ننشئ شروط دفع تلقائية - كل مشروع له شروط خاصة
                var defaultTerms = new List<(string Description, decimal Percentage, string TriggerCondition)>();

                // لا نضيف أي شروط دفع افتراضية
                System.Diagnostics.Debug.WriteLine($"No default payment terms will be created for category: {category}");

                // Add the default terms to the project
                int termOrder = 1;
                foreach (var (description, percentage, triggerCondition) in defaultTerms)
                {
                    var paymentTerm = new ProjectPaymentTerm
                    {
                        ProjectId = _currentProject!.Id,
                        Description = description,
                        Percentage = percentage,
                        TriggerCondition = triggerCondition,
                        Category = category,
                        TermOrder = termOrder++,
                        IsActive = true
                    };

                    await App.DataService.AddProjectPaymentTermAsync(paymentTerm);
                }

                System.Diagnostics.Debug.WriteLine($"Created {defaultTerms.Count} default payment terms for category: {category}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating default payment terms: {ex.Message}");
            }
        }

        private void CreateDemoPaymentTerms()
        {
            // لا ننشئ أي بيانات افتراضية - المستخدم يجب أن يدخل شروط الدفع بنفسه
            _availablePaymentTerms = new List<ProjectPaymentTerm>();
            System.Diagnostics.Debug.WriteLine("No demo payment terms created - user must add their own payment terms");
        }

        private async Task PopulatePaymentTermsForNewDesign()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"PopulatePaymentTermsForNewDesign called. PaymentTermsStackPanel: {PaymentTermsStackPanel != null}, _availablePaymentTerms: {_availablePaymentTerms?.Count ?? 0}");
                System.Diagnostics.Debug.WriteLine($"Current project: {(_currentProject != null ? $"{_currentProject.Name} (ID: {_currentProject.Id})" : "NULL")}");

                if (PaymentTermsStackPanel == null || _availablePaymentTerms == null)
                {
                    System.Diagnostics.Debug.WriteLine("Exiting early - missing components");
                    return;
                }

                PaymentTermsStackPanel.Children.Clear();

                foreach (var paymentTerm in _availablePaymentTerms)
                {
                    System.Diagnostics.Debug.WriteLine($"Processing payment term: '{paymentTerm.Description}' ({paymentTerm.Percentage}%)");

                    // Calculate available percentage for this term
                    var availablePercentage = await GetAvailablePercentageForTerm(paymentTerm);

                    // Check if this term has remaining percentage available for the current site
                    var remainingPercentage = await GetRemainingPercentageForTermAndSite(paymentTerm);

                    // Check if this payment term is fully paid
                    var isFullyPaid = await IsPaymentTermFullyPaid(paymentTerm);

                    System.Diagnostics.Debug.WriteLine($"Payment term '{paymentTerm.Description}': availablePercentage={availablePercentage}, remainingPercentage={remainingPercentage}, isFullyPaid={isFullyPaid}");



                    // Create a card for each payment term
                    var termCard = new MaterialDesignThemes.Wpf.Card
                    {
                        Margin = new Thickness(0, 0, 0, 8),
                        Padding = new Thickness(12),
                        Background = isFullyPaid ?
                            new SolidColorBrush(Color.FromRgb(240, 240, 240)) : // Darker background for paid terms
                            new SolidColorBrush(Color.FromRgb(250, 250, 250))   // Normal background
                    };

                    var termPanel = new StackPanel();

                    // Checkbox for selection
                    var contentText = $"({paymentTerm.Percentage}%) {paymentTerm.Description}";

                    // Show usage status with percentage details
                    if (isFullyPaid)
                    {
                        contentText += " ✓ FULLY PAID";
                    }
                    else if (remainingPercentage < paymentTerm.Percentage)
                    {
                        // Partially used - show how much is left
                        var usedPercentage = paymentTerm.Percentage - remainingPercentage;
                        var usedPercent = (usedPercentage / paymentTerm.Percentage) * 100;
                        contentText += $" ({usedPercent:F1}% used, {remainingPercentage:F2}% left)";
                    }

                    if (paymentTerm.IgnoreSiteDivision)
                    {
                        contentText += " 🌐"; // Globe icon to indicate all sites
                    }
                    else if (_currentProject?.NumberOfSites > 1)
                    {
                        var perSitePercentage = paymentTerm.Percentage / _currentProject.NumberOfSites;
                        contentText += $" 📍 ({perSitePercentage:F1}% per site)";
                    }

                    var checkBox = new CheckBox
                    {
                        Content = contentText,
                        FontWeight = isFullyPaid ? FontWeights.Bold : FontWeights.Medium,
                        FontSize = 13,
                        Tag = paymentTerm,
                        Margin = new Thickness(0, 0, 0, 4),
                        IsEnabled = !isFullyPaid, // Disable checkbox if fully paid
                        Foreground = isFullyPaid ?
                            new SolidColorBrush(Color.FromRgb(100, 100, 100)) : // Gray for paid terms
                            new SolidColorBrush(Color.FromRgb(33, 33, 33))      // Normal black for unpaid
                    };
                    checkBox.Checked += PaymentTermCheckBox_Checked;
                    checkBox.Unchecked += PaymentTermCheckBox_Unchecked;

                    // Payment status info for current site
                    var availableText = new TextBlock();
                    var currentSiteId = GetSelectedSiteId();

                    if (isFullyPaid)
                    {
                        // Get information about which sites have used this payment term
                        var usedSitesInfo = await GetUsedSitesInfo(paymentTerm);
                        availableText.Text = $"This payment term is fully paid for this site. {usedSitesInfo}";
                        availableText.Foreground = new SolidColorBrush(Color.FromRgb(100, 100, 100)); // Gray for paid terms
                        availableText.FontStyle = FontStyles.Italic;
                    }
                    else if (_currentProject?.NumberOfSites > 1 && !paymentTerm.IgnoreSiteDivision && currentSiteId.HasValue)
                    {
                        var percentagePerSite = paymentTerm.Percentage / _currentProject.NumberOfSites;
                        availableText.Text = $"Available for this site: {remainingPercentage:F1}% (of {percentagePerSite:F1}% per site)";

                        if (remainingPercentage < percentagePerSite)
                        {
                            var usedPercentage = percentagePerSite - remainingPercentage;
                            availableText.Text += $" | Used: {usedPercentage:F1}%";
                            availableText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange for partially used
                        }
                    }
                    else
                    {
                        availableText.Text = $"Available: {remainingPercentage:F1}% (of {paymentTerm.Percentage}%)";

                        if (remainingPercentage < paymentTerm.Percentage)
                        {
                            var usedPercentage = paymentTerm.Percentage - remainingPercentage;
                            availableText.Text += $" | Used: {usedPercentage:F1}%";
                            availableText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange for partially used
                        }
                    }

                    if (availableText.Foreground == null)
                    {
                        availableText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green color
                    }
                    availableText.FontSize = 10;
                    availableText.FontWeight = FontWeights.Medium;
                    availableText.Margin = new Thickness(20, 0, 0, 2);

                    // Partial percentage input
                    var percentagePanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(20, 4, 0, 4),
                        Visibility = Visibility.Collapsed
                    };

                    var percentageLabel = new TextBlock
                    {
                        Text = CustomPctOfTermCheckBox?.IsChecked == true ? "% of term:" : "% of project:",
                        FontSize = 11,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(0, 0, 8, 0)
                    };

                    var percentageTextBox = new TextBox
                    {
                        Width = 80,
                        Height = 30,
                        FontSize = 12,
                        Tag = paymentTerm,
                        VerticalAlignment = VerticalAlignment.Center,
                        HorizontalContentAlignment = HorizontalAlignment.Center,
                        Padding = new Thickness(5)
                    };

                    // Set initial value and hint based on mode
                    if (UseFullTermCheckBox?.IsChecked == true)
                    {
                        // Use full payment term mode - show remaining percentage instead of full term
                        percentageTextBox.Text = "100.00";
                        MaterialDesignThemes.Wpf.HintAssist.SetHint(percentageTextBox, $"% of {remainingPercentage:F2}% (remaining)");
                    }
                    else if (CustomPctOfTermCheckBox?.IsChecked == true)
                    {
                        // Percentage of payment term mode (divided by sites)
                        var perSitePercentage = paymentTerm.Percentage / (_currentProject?.NumberOfSites ?? 1);
                        percentageTextBox.Text = "100.00";
                        MaterialDesignThemes.Wpf.HintAssist.SetHint(percentageTextBox, $"% of {perSitePercentage:F2}%");
                    }
                    else
                    {
                        // Direct percentage mode
                        percentageTextBox.Text = availablePercentage.ToString("F2");
                        MaterialDesignThemes.Wpf.HintAssist.SetHint(percentageTextBox, "% of project");
                    }

                    percentageTextBox.TextChanged += PartialPercentageTextBox_TextChanged;

                    var percentageSymbol = new TextBlock
                    {
                        Text = "%",
                        FontSize = 11,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(4, 0, 0, 0)
                    };

                    percentagePanel.Children.Add(percentageLabel);
                    percentagePanel.Children.Add(percentageTextBox);
                    percentagePanel.Children.Add(percentageSymbol);


                    ComboBox? siteComboBox = null;

                    // Store references for later use
                    checkBox.Tag = new { PaymentTerm = paymentTerm, PercentagePanel = percentagePanel, PercentageTextBox = percentageTextBox, SiteComboBox = siteComboBox };

                    // Description text
                    var descriptionText = new TextBlock
                    {
                        Text = paymentTerm.TriggerCondition ?? "No additional conditions",
                        FontSize = 11,
                        Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(20, 0, 0, 0)
                    };

                    termPanel.Children.Add(checkBox);
                    termPanel.Children.Add(availableText);
                    termPanel.Children.Add(percentagePanel);
                    termPanel.Children.Add(descriptionText);
                    termCard.Content = termPanel;

                    PaymentTermsStackPanel.Children.Add(termCard);
                }

                // Payment terms loaded successfully
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error populating payment terms: {ex.Message}");
            }
        }

        private class PaymentTermStatus
        {
            public bool HasUnpaidAmount { get; set; }
            public decimal UnpaidAmount { get; set; }
            public decimal TotalUsedAmount { get; set; }
            public decimal TotalPaidAmount { get; set; }
        }

        private async System.Threading.Tasks.Task<PaymentTermStatus> GetPaymentTermStatus(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_currentProject == null)
                {
                    System.Diagnostics.Debug.WriteLine("Warning: _currentProject is null in GetPaymentTermStatus - payment term will be considered unpaid");
                    // If project is not loaded, we should consider the payment term as unpaid/available
                    // This prevents showing all payment terms as "fully paid" when project data isn't loaded
                    return new PaymentTermStatus { HasUnpaidAmount = true, UnpaidAmount = 0 };
                }

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                decimal totalUsedAmount = 0;
                decimal totalPaidAmount = 0;

                foreach (var invoice in projectInvoices)
                {
                    // Skip current invoice if editing
                    if (_isEdit && _invoice != null && invoice.Id == _invoice.Id) continue;

                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    if (usedTerm != null)
                    {
                        totalUsedAmount += usedTerm.CalculatedAmount;
                        // Calculate paid amount for this payment term based on invoice paid percentage
                        if (invoice.AmountUSD > 0)
                        {
                            var invoicePaidPercentage = invoice.PaidAmount / invoice.AmountUSD;
                            totalPaidAmount += usedTerm.CalculatedAmount * invoicePaidPercentage;
                        }
                    }
                }

                var unpaidAmount = totalUsedAmount - totalPaidAmount;

                // If the payment term has never been used (totalUsedAmount = 0), it should be available (not fully paid)
                // If it has been used but not fully paid, it should also be available
                // Only if it has been used and fully paid should it be considered "fully paid"
                bool hasUnpaidAmount = totalUsedAmount == 0 || unpaidAmount > 0.01m;

                System.Diagnostics.Debug.WriteLine($"Payment term '{paymentTerm.Description}' status calculation: " +
                    $"totalUsedAmount={totalUsedAmount}, totalPaidAmount={totalPaidAmount}, " +
                    $"unpaidAmount={unpaidAmount}, hasUnpaidAmount={hasUnpaidAmount}");

                return new PaymentTermStatus
                {
                    HasUnpaidAmount = hasUnpaidAmount,
                    UnpaidAmount = Math.Max(0, unpaidAmount),
                    TotalUsedAmount = totalUsedAmount,
                    TotalPaidAmount = totalPaidAmount
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting payment term status: {ex.Message}");
                return new PaymentTermStatus { HasUnpaidAmount = false, UnpaidAmount = 0 };
            }
        }

        private async System.Threading.Tasks.Task<decimal> GetRemainingPercentageForTermAndSite(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_currentProject == null) return paymentTerm.Percentage;

                // Check if this is an "All Sites" invoice
                bool isAllSitesInvoice = AllSitesRadio?.IsChecked == true;

                // Get the currently selected site
                var currentSiteId = GetSelectedSiteId();

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                decimal usedPercentage = 0;

                System.Diagnostics.Debug.WriteLine($"\n=== Calculating remaining percentage for '{paymentTerm.Description}' ({paymentTerm.Percentage}%) ===");
                System.Diagnostics.Debug.WriteLine($"Current mode: {(isAllSitesInvoice ? "All Sites" : "Site-specific")}");

                foreach (var invoice in projectInvoices)
                {
                    // Skip current invoice if editing
                    if (_isEdit && _invoice != null && invoice.Id == _invoice.Id) continue;

                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    if (usedTerm != null)
                    {
                        bool invoiceIsAllSites = invoice.AppliesAllSites || invoice.SiteName.Contains("All Sites");

                        System.Diagnostics.Debug.WriteLine($"Invoice {invoice.InvoiceNumber} ({(invoiceIsAllSites ? "All Sites" : "Site-specific")}):");
                        System.Diagnostics.Debug.WriteLine($"  Applied Percentage: {usedTerm.AppliedPercentage:F2}%");

                        // IMPORTANT: Count the ACTUAL percentage used, not based on invoice payment status
                        if (invoiceIsAllSites)
                        {
                            // For "All Sites" invoices, count the actual applied percentage
                            usedPercentage += usedTerm.AppliedPercentage;
                            System.Diagnostics.Debug.WriteLine($"  Added (All Sites): {usedTerm.AppliedPercentage:F2}%");
                        }
                        else
                        {
                            // Site-specific invoice - only count if it's for the same site we're checking
                            if (currentSiteId.HasValue && invoice.ProjectSiteId == currentSiteId.Value)
                            {
                                usedPercentage += usedTerm.AppliedPercentage;
                                System.Diagnostics.Debug.WriteLine($"  Added (same site): {usedTerm.AppliedPercentage:F2}%");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"  Skipped (different site)");
                            }
                        }
                    }
                }

                // Calculate remaining percentage
                decimal totalPercentageForTerm = paymentTerm.Percentage;
                decimal remainingPercentage = 0;

                // If this is an "All Sites" invoice, it needs the full remaining percentage
                if (isAllSitesInvoice)
                {
                    remainingPercentage = totalPercentageForTerm - usedPercentage;
                    System.Diagnostics.Debug.WriteLine($"\nAll Sites calculation:");
                    System.Diagnostics.Debug.WriteLine($"  Total: {totalPercentageForTerm:F2}% - Used: {usedPercentage:F2}% = Remaining: {remainingPercentage:F2}%");
                }
                else
                {
                    // For site-specific invoices, calculate per-site percentage
                    var perSitePercentage = totalPercentageForTerm / _currentProject.NumberOfSites;
                    remainingPercentage = perSitePercentage - usedPercentage;
                    System.Diagnostics.Debug.WriteLine($"\nSite-specific calculation:");
                    System.Diagnostics.Debug.WriteLine($"  Per-site: {perSitePercentage:F2}% - Used: {usedPercentage:F2}% = Remaining: {remainingPercentage:F2}%");
                }

                System.Diagnostics.Debug.WriteLine($"=== Remaining percentage: {Math.Max(0, remainingPercentage):F2}% ===\n");

                return Math.Max(0, remainingPercentage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating remaining percentage for term and site: {ex.Message}");
                return paymentTerm.Percentage;
            }
        }



        private async System.Threading.Tasks.Task<bool> IsPaymentTermFullyPaid(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_currentProject == null) return false;

                // Check if this is an "All Sites" invoice
                bool isAllSitesInvoice = AllSitesRadio?.IsChecked == true;

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                // Calculate total used percentage for this payment term
                decimal totalUsedPercentage = 0;
                int invoiceCount = 0;

                System.Diagnostics.Debug.WriteLine($"\n=== Checking if payment term '{paymentTerm.Description}' ({paymentTerm.Percentage}%) is fully paid ===");
                System.Diagnostics.Debug.WriteLine($"Current invoice mode: {(isAllSitesInvoice ? "All Sites" : "Site-specific")}");
                System.Diagnostics.Debug.WriteLine($"Project has {_currentProject.NumberOfSites} sites");

                foreach (var invoice in projectInvoices)
                {
                    // Skip current invoice if editing
                    if (_isEdit && _invoice != null && invoice.Id == _invoice.Id)
                    {
                        System.Diagnostics.Debug.WriteLine($"Skipping current invoice (ID: {invoice.Id}) - editing mode");
                        continue;
                    }

                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    if (usedTerm != null)
                    {
                        invoiceCount++;
                        bool invoiceIsAllSites = invoice.AppliesAllSites || (invoice.SiteName?.Contains("All Sites") ?? false);

                        System.Diagnostics.Debug.WriteLine($"Invoice #{invoiceCount} (ID: {invoice.Id}, {invoice.InvoiceNumber}):");
                        System.Diagnostics.Debug.WriteLine($"  - Type: {(invoiceIsAllSites ? "All Sites" : "Site-specific")}");
                        System.Diagnostics.Debug.WriteLine($"  - Site: {invoice.SiteName ?? "N/A"}");
                        System.Diagnostics.Debug.WriteLine($"  - Applied Percentage: {usedTerm.AppliedPercentage:F2}%");

                        if (invoiceIsAllSites)
                        {
                            // For "All Sites" invoices, add the used percentage to the total
                            // Don't mark as fully paid here - we'll check the total at the end
                            totalUsedPercentage += usedTerm.AppliedPercentage;
                            System.Diagnostics.Debug.WriteLine($"  - Added to total (All Sites): {usedTerm.AppliedPercentage:F2}%");
                        }
                        else
                        {
                            // For site-specific invoices, only count if it's for the current site
                            var currentSiteId = GetSelectedSiteId();
                            if (currentSiteId.HasValue && invoice.ProjectSiteId == currentSiteId.Value)
                            {
                                totalUsedPercentage += usedTerm.AppliedPercentage;
                                System.Diagnostics.Debug.WriteLine($"  - Added to total (same site): {usedTerm.AppliedPercentage:F2}%");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"  - Skipped (different site): Invoice site={invoice.ProjectSiteId}, Current site={currentSiteId}");
                            }
                        }
                    }
                }

                // Check if the total used percentage equals or exceeds the payment term percentage
                bool isFullyUsed = false;

                // IMPORTANT: A payment term is only "fully used" if >= 99% of its percentage is consumed
                // For example, if payment term is 30%:
                //   - If 29.7% or more is used (99% of 30%) → FULLY USED
                //   - If 26.69% is used (88.96% of 30%) → NOT FULLY USED (still 3.31% available)

                if (isAllSitesInvoice)
                {
                    // For "All Sites" invoices, check if >= 99% of the full term percentage is used
                    var usagePercentage = (totalUsedPercentage / paymentTerm.Percentage) * 100m;
                    isFullyUsed = usagePercentage >= 99.0m;
                    System.Diagnostics.Debug.WriteLine($"\nAll Sites mode check:");
                    System.Diagnostics.Debug.WriteLine($"  Total used: {totalUsedPercentage:F2}% / Term: {paymentTerm.Percentage:F2}% = {usagePercentage:F2}%");
                    System.Diagnostics.Debug.WriteLine($"  Is >= 99%? {isFullyUsed}");
                    System.Diagnostics.Debug.WriteLine($"  Remaining: {paymentTerm.Percentage - totalUsedPercentage:F2}% ({100 - usagePercentage:F2}% of term)");
                }
                else
                {
                    // For site-specific invoices, check if >= 99% of the per-site percentage is used
                    var perSitePercentage = paymentTerm.Percentage / (_currentProject.NumberOfSites > 0 ? _currentProject.NumberOfSites : 1);
                    var usagePercentage = (totalUsedPercentage / perSitePercentage) * 100m;
                    isFullyUsed = usagePercentage >= 99.0m;
                    System.Diagnostics.Debug.WriteLine($"\nSite-specific mode check:");
                    System.Diagnostics.Debug.WriteLine($"  Per-site percentage: {perSitePercentage:F2}%");
                    System.Diagnostics.Debug.WriteLine($"  Total used: {totalUsedPercentage:F2}% / Per-site: {perSitePercentage:F2}% = {usagePercentage:F2}%");
                    System.Diagnostics.Debug.WriteLine($"  Is >= 99%? {isFullyUsed}");
                    System.Diagnostics.Debug.WriteLine($"  Remaining: {perSitePercentage - totalUsedPercentage:F2}% ({100 - usagePercentage:F2}% of per-site term)");
                }

                System.Diagnostics.Debug.WriteLine($"\n=== Result: Payment term is {(isFullyUsed ? "FULLY PAID" : "AVAILABLE")} ===\n");

                return isFullyUsed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking if payment term is fully paid: {ex.Message}");
                // If there's an error, assume the payment term is NOT fully paid (safer default)
                return false;
            }
        }

        private async System.Threading.Tasks.Task<string> GetUsedSitesInfo(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_currentProject == null) return "";

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                var usedSites = new List<string>();

                foreach (var invoice in projectInvoices)
                {
                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    if (usedTerm != null && invoice.ProjectSiteId.HasValue)
                    {
                        var site = _projectSites.FirstOrDefault(s => s.Id == invoice.ProjectSiteId.Value);
                        if (site != null && !usedSites.Contains(site.SiteName))
                        {
                            usedSites.Add(site.SiteName);
                        }
                    }
                }

                if (usedSites.Any())
                {
                    return $"Used in: {string.Join(", ", usedSites)}";
                }

                return "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting used sites info: {ex.Message}");
                return "";
            }
        }

        private async System.Threading.Tasks.Task<decimal> GetAvailablePercentageForTerm(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                if (_currentProject == null) return paymentTerm.Percentage;

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                decimal usedPercentage = 0;
                var usedSites = new HashSet<int>(); // Track which sites have used this payment term

                foreach (var invoice in projectInvoices)
                {
                    // Skip current invoice if editing
                    if (_isEdit && _invoice != null && invoice.Id == _invoice.Id) continue;

                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);

                    if (usedTerm != null)
                    {
                        usedPercentage += usedTerm.AppliedPercentage;

                        // Track which site used this payment term
                        if (invoice.ProjectSiteId.HasValue)
                        {
                            usedSites.Add(invoice.ProjectSiteId.Value);
                        }
                    }
                }

                // Calculate available percentage based on sites usage
                decimal availablePercentage;

                if (paymentTerm.IgnoreSiteDivision)
                {
                    // This term applies to the whole project regardless of sites
                    availablePercentage = paymentTerm.Percentage - usedPercentage;
                }
                else
                {
                    // For multi-site projects, each site gets (paymentTerm.Percentage / numberOfSites)
                    // Available percentage = (percentage per site) × (unused sites count)
                    var percentagePerSite = paymentTerm.Percentage / _currentProject.NumberOfSites;
                    var unusedSitesCount = _currentProject.NumberOfSites - usedSites.Count;
                    availablePercentage = percentagePerSite * unusedSitesCount;

                    System.Diagnostics.Debug.WriteLine($"Payment term {paymentTerm.Description}: {percentagePerSite:F2}% per site × {unusedSitesCount} unused sites = {availablePercentage:F2}% available");
                }

                return Math.Max(0, availablePercentage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating available percentage: {ex.Message}");
                return paymentTerm.Percentage;
            }
        }

        private async void PartialPercentageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox && textBox.Tag is ProjectPaymentTerm paymentTerm)
            {
                try
                {
                    var invoicePaymentTerm = _selectedInvoicePaymentTerms?.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);
                    if (invoicePaymentTerm != null)
                    {
                        // Only process if text is not empty and is a valid number
                        if (!string.IsNullOrWhiteSpace(textBox.Text) && decimal.TryParse(textBox.Text, out decimal inputValue) && inputValue >= 0)
                        {
                            if (UseFullTermCheckBox?.IsChecked == true)
                            {
                                // Use full payment term mode - calculate based on REMAINING percentage
                                // Example: Payment term 30% with 10% already used = 20% remaining
                                // Input 50% means 50% of 20% = 10% of total project
                                var remainingPercentage = await GetRemainingPercentageForTermAndSite(paymentTerm);
                                var effectivePercentage = remainingPercentage * (inputValue / 100m);
                                invoicePaymentTerm.AppliedPercentage = effectivePercentage;

                                System.Diagnostics.Debug.WriteLine($"Full term mode: {inputValue}% of {remainingPercentage:F2}% (remaining) = {effectivePercentage}%");
                            }
                            else if (CustomPctOfTermCheckBox?.IsChecked == true)
                            {
                                // Percentage of payment term mode (divided by sites)
                                // Example: Payment term 30% for 2 sites = 15% per site
                                // Input 50% means 50% of the 15% = 7.5% of total project
                                var perSitePercentage = paymentTerm.Percentage / (_currentProject?.NumberOfSites ?? 1);
                                var effectivePercentage = perSitePercentage * (inputValue / 100m);
                                invoicePaymentTerm.AppliedPercentage = effectivePercentage;

                                System.Diagnostics.Debug.WriteLine($"Percentage of term: {paymentTerm.Percentage}% ÷ {_currentProject?.NumberOfSites ?? 1} = {perSitePercentage}%, {inputValue}% of {perSitePercentage}% = {effectivePercentage}%");
                            }
                            else
                            {
                                // Direct percentage of total project
                                invoicePaymentTerm.AppliedPercentage = inputValue;
                                System.Diagnostics.Debug.WriteLine($"Direct percentage: {inputValue}% of total project");
                            }

                            // Reset user-edited flag when percentage changes
                            _userEditedPaidAmount = false;

                            CalculateAmountsFromMultiplePaymentTerms();
                            UpdateCalculationSummary();
                            UpdateSelectedTermsSummary(); // Update right panel
                            AutoFillFinancialFields();
                        }
                        else if (string.IsNullOrWhiteSpace(textBox.Text))
                        {
                            // If text is empty, set percentage to 0
                            invoicePaymentTerm.AppliedPercentage = 0;
                            CalculateAmountsFromMultiplePaymentTerms();
                            UpdateCalculationSummary();
                            UpdateSelectedTermsSummary(); // Update right panel
                            AutoFillFinancialFields();
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error updating partial percentage: {ex.Message}");
                }
            }
        }

        private void CustomPctOfTermCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            // Uncheck the other option
            if (UseFullTermCheckBox != null)
                UseFullTermCheckBox.IsChecked = false;

            // Reset all selected payment terms and refresh display
            _selectedInvoicePaymentTerms.Clear();
            RefreshPaymentTermsUI();
        }

        private void CustomPctOfTermCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            // Reset all selected payment terms and refresh display
            _selectedInvoicePaymentTerms.Clear();
            RefreshPaymentTermsUI();
        }

        private void UseFullTermCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            // Uncheck the other option
            if (CustomPctOfTermCheckBox != null)
                CustomPctOfTermCheckBox.IsChecked = false;

            // Reset all selected payment terms and refresh display
            _selectedInvoicePaymentTerms.Clear();
            RefreshPaymentTermsUI();
        }

        private void UseFullTermCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            // Reset all selected payment terms and refresh display
            _selectedInvoicePaymentTerms.Clear();
            RefreshPaymentTermsUI();
        }

        private void RefreshPaymentTermsUI()
        {
            try
            {
                // Clear and reload payment terms
                PaymentTermsStackPanel.Children.Clear();
                _ = LoadAvailablePaymentTerms();

                // Update calculations
                CalculateAmountsFromMultiplePaymentTerms();
                UpdateCalculationSummary();
                AutoFillFinancialFields();

                // Update UI state
                ValidateStep4();
                UpdateStepVisibility();
                UpdateSaveButtonState();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing payment terms UI: {ex.Message}");
            }
        }

        private void UpdatePaymentTermsDisplay()
        {
            try
            {
                // Update all existing percentage textboxes with appropriate values
                foreach (var child in PaymentTermsStackPanel.Children)
                {
                    if (child is MaterialDesignThemes.Wpf.Card card && card.Content is StackPanel panel)
                    {
                        foreach (var panelChild in panel.Children)
                        {
                            if (panelChild is StackPanel percentagePanel && percentagePanel.Visibility == Visibility.Visible)
                            {
                                foreach (var percentageChild in percentagePanel.Children)
                                {
                                    if (percentageChild is TextBox textBox && textBox.Tag is ProjectPaymentTerm paymentTerm)
                                    {
                                        // Update textbox placeholder/hint based on mode
                                        if (CustomPctOfTermCheckBox?.IsChecked == true)
                                        {
                                            // Show percentage of the payment term
                                            var perSitePercentage = paymentTerm.Percentage / (_currentProject?.NumberOfSites ?? 1);
                                            textBox.Text = "100.00"; // Default to 100% of the term
                                            MaterialDesignThemes.Wpf.HintAssist.SetHint(textBox, $"% of {perSitePercentage:F2}%");
                                        }
                                        else
                                        {
                                            // Show direct percentage of project
                                            var availablePercentage = GetAvailablePercentageForTerm(paymentTerm).Result;
                                            textBox.Text = availablePercentage.ToString("F2");
                                            MaterialDesignThemes.Wpf.HintAssist.SetHint(textBox, "% of project");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating payment terms display: {ex.Message}");
            }
        }

        private void UpdateCalculationSummary()
        {
            try
            {
                var calculationSummaryCard = FindName("CalculationSummaryCard") as MaterialDesignThemes.Wpf.Card;
                var calculationDetailsText = FindName("CalculationDetailsText") as TextBlock;

                if (calculationSummaryCard == null || calculationDetailsText == null) return;

                if (_selectedInvoicePaymentTerms.Any() && _currentProject != null)
                {
                    calculationSummaryCard.Visibility = Visibility.Visible;

                    var summaryLines = new List<string>();
                    decimal totalAmount = 0;

                    // Get base amount for calculation
                    string selectedType = "";
                    if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                    {
                        selectedType = selectedItem.Content?.ToString() ?? "";
                    }

                    decimal baseAmount = _currentProject != null ? selectedType switch
                    {
                        // New naming convention
                        "Equipment (Software)" => _currentProject.SoftwareTasksAmount,
                        "Equipment (Hardware)" => _currentProject.HardwareTasksAmount,
                        "Equipment" => _currentProject.DistributableTasksAmount,

                        // Legacy Split Equipment types
                        "Software Equipment" => _currentProject.SoftwareTasksAmount,
                        "Hardware Equipment" => _currentProject.HardwareTasksAmount,
                        "Hardware & Software Equipment" => _currentProject.DistributableTasksAmount,

                        // Legacy task types
                        "Hardware & Software Tasks" => _currentProject.DistributableTasksAmount,
                        "Software Tasks" => _currentProject.SoftwareTasksAmount,
                        "Hardware Tasks" => _currentProject.HardwareTasksAmount,

                        // Other categories
                        "Services" => _currentProject.ServicesAmount,
                        "Spare Parts" => _currentProject.SparePartsAmount,
                        "Extra" => _currentProject.ExtraAmount,

                        // Check if it matches the custom extra category name
                        _ when !string.IsNullOrWhiteSpace(_currentProject.ExtraCategoryName) &&
                               selectedType.Equals(_currentProject.ExtraCategoryName, StringComparison.OrdinalIgnoreCase) => _currentProject.ExtraAmount,

                        _ => _currentProject.DistributableTasksAmount
                    } : 0;

                    // Simple summary without detailed explanation

                    foreach (var term in _selectedInvoicePaymentTerms)
                    {
                        var paymentTerm = _availablePaymentTerms?.FirstOrDefault(pt => pt.Id == term.ProjectPaymentTermId);
                        if (paymentTerm != null)
                        {
                            summaryLines.Add($"{paymentTerm.Description}: ${term.CalculatedAmount:N0}");
                            totalAmount += term.CalculatedAmount;
                        }
                    }

                    summaryLines.Add($"Total: ${totalAmount:N0}");

                    calculationDetailsText.Text = string.Join("\n", summaryLines);
                }
                else
                {
                    calculationSummaryCard.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating calculation summary: {ex.Message}");
            }
        }

        private void AutoFillFinancialFields()
        {
            try
            {
                if (_selectedInvoicePaymentTerms.Any())
                {
                    var totalAmount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);

                    // Auto-fill Exchange Rate if empty
                    var exchangeRateTextBox = FindName("ExchangeRateTextBox") as TextBox;
                    if (exchangeRateTextBox != null && string.IsNullOrWhiteSpace(exchangeRateTextBox.Text))
                    {
                        exchangeRateTextBox.Text = "1.00"; // Default to 1.00 for USD
                    }

                    // Auto-fill Paid Amount with the calculated total amount
                    // Update it every time the amount changes (unless user has manually edited it)
                    var paidAmountTextBox = FindName("PaidAmountTextBox") as TextBox;
                    if (paidAmountTextBox != null && !_userEditedPaidAmount)
                    {
                        // Temporarily disable the TextChanged event to prevent marking as user-edited
                        paidAmountTextBox.TextChanged -= PaidAmountTextBox_TextChanged;
                        paidAmountTextBox.Text = totalAmount.ToString("F2");
                        paidAmountTextBox.TextChanged += PaidAmountTextBox_TextChanged;
                    }

                    // Auto-fill dates if empty
                    var arrivalDatePicker = FindName("ArrivalDatePicker") as DatePicker;
                    if (arrivalDatePicker != null && !arrivalDatePicker.SelectedDate.HasValue)
                    {
                        arrivalDatePicker.SelectedDate = DateTime.Today;
                    }

                    // Don't auto-fill signature date - user will enter it manually
                    // var signatureDatePicker = FindName("SignatureDatePicker") as DatePicker;
                    // Leave signature date empty for manual entry

                    // Auto-generate invoice number if empty
                    var invoiceNumberTextBox = FindName("InvoiceNumberTextBox") as TextBox;
                    if (invoiceNumberTextBox != null && string.IsNullOrWhiteSpace(invoiceNumberTextBox.Text))
                    {
                        var prefix = "INV";
                        var dateStr = DateTime.Today.ToString("yyyyMMdd");
                        var randomSuffix = new Random().Next(100, 999);
                        invoiceNumberTextBox.Text = $"{prefix}-{dateStr}-{randomSuffix}";
                    }

                    // Auto-fill description based on selected payment terms
                    var descriptionTextBox = FindName("DescriptionTextBox") as TextBox;
                    if (descriptionTextBox != null && string.IsNullOrWhiteSpace(descriptionTextBox.Text))
                    {
                        var descriptions = new List<string>();
                        foreach (var term in _selectedInvoicePaymentTerms)
                        {
                            var paymentTerm = _availablePaymentTerms?.FirstOrDefault(pt => pt.Id == term.ProjectPaymentTermId);
                            if (paymentTerm != null)
                            {
                                descriptions.Add(paymentTerm.Description);
                            }
                        }

                        if (descriptions.Any())
                        {
                            descriptionTextBox.Text = string.Join(", ", descriptions);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error auto-filling financial fields: {ex.Message}");
            }
        }

        private async Task PopulateAvailablePaymentTermsList()
        {
            // Legacy function - kept for compatibility
            await PopulatePaymentTermsForNewDesign();

            // If we have existing payment terms, select them after the UI is loaded
            if (_existingPaymentTermIds.Any())
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    _ = SelectExistingPaymentTerms();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
        }

        private async Task SelectExistingPaymentTerms()
        {
            // Clear current selections first
            _selectedInvoicePaymentTerms.Clear();

            try
            {
                // Load existing invoice payment terms with their saved percentages
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                var existingTerms = await App.DataService.GetInvoicePaymentTermsAsync(_invoice.Id);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

                foreach (var existingTerm in existingTerms)
                {
                    var paymentTerm = _availablePaymentTerms.FirstOrDefault(pt => pt.Id == existingTerm.ProjectPaymentTermId);
                    if (paymentTerm != null)
                    {
                        // Add to selected terms with the saved percentage and site info
                        var invoicePaymentTerm = new InvoicePaymentTerm
                        {
                            InvoiceId = _invoice.Id,
                            ProjectPaymentTermId = paymentTerm.Id,
                            AppliedPercentage = existingTerm.AppliedPercentage,
                            DisplayOrder = existingTerm.DisplayOrder,
                            SiteName = existingTerm.SiteName,
                            CalculatedAmount = existingTerm.CalculatedAmount,
                            ProjectPaymentTerm = paymentTerm
                        };

                        _selectedInvoicePaymentTerms.Add(invoicePaymentTerm);

                        // Find the checkbox in the PaymentTermsStackPanel and check it
                        foreach (var child in PaymentTermsStackPanel.Children)
                        {
                            if (child is MaterialDesignThemes.Wpf.Card card && card.Content is StackPanel panel)
                            {
                                foreach (var panelChild in panel.Children)
                                {
                                    if (panelChild is CheckBox checkBox && checkBox.Tag is ProjectPaymentTerm term && term.Id == existingTerm.ProjectPaymentTermId)
                                    {
                                        checkBox.IsChecked = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                // Update the summary and calculations
                UpdateSelectedTermsSummary();
                UpdateMultipleTermsSummary();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error selecting existing payment terms: {ex.Message}");
            }

            // Clear the existing terms list as they are now loaded
            _existingPaymentTermIds.Clear();
        }



        private void PaymentTermCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox)
            {
                // Handle new tag structure
                if (checkBox.Tag is { } tagObj)
                {
                    var tagType = tagObj.GetType();
                    if (tagType.GetProperty("PaymentTerm")?.GetValue(tagObj) is ProjectPaymentTerm paymentTerm &&
                        tagType.GetProperty("PercentagePanel")?.GetValue(tagObj) is StackPanel percentagePanel)
                    {
                        // Show percentage input panel
                        percentagePanel.Visibility = Visibility.Visible;

                        // Reset user-edited flag when adding a new payment term
                        _userEditedPaidAmount = false;

                        AddPaymentTermToSelection(paymentTerm);
                        UpdateSelectedTermsSummary();
                        ValidateStep4();
                        UpdateStepVisibility();
                        UpdateSaveButtonState();
                    }
                    else if (tagObj is ProjectPaymentTerm directPaymentTerm)
                    {
                        // Handle old tag structure for compatibility
                        // Reset user-edited flag when adding a new payment term
                        _userEditedPaidAmount = false;

                        AddPaymentTermToSelection(directPaymentTerm);
                        UpdateSelectedTermsSummary();
                        ValidateStep4();
                        UpdateStepVisibility();
                        UpdateSaveButtonState();
                    }
                }
            }
        }

        private void PaymentTermCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox)
            {
                // Handle new tag structure
                if (checkBox.Tag is { } tagObj)
                {
                    var tagType = tagObj.GetType();
                    if (tagType.GetProperty("PaymentTerm")?.GetValue(tagObj) is ProjectPaymentTerm paymentTerm &&
                        tagType.GetProperty("PercentagePanel")?.GetValue(tagObj) is StackPanel percentagePanel)
                    {
                        // Hide percentage input panel
                        percentagePanel.Visibility = Visibility.Collapsed;

                        // Reset user-edited flag when removing a payment term
                        _userEditedPaidAmount = false;

                        RemovePaymentTermFromSelection(paymentTerm);
                        UpdateSelectedTermsSummary();
                        ValidateStep4();
                        UpdateStepVisibility();
                        UpdateSaveButtonState();

                        // Update the right side summary display
                        UpdateSelectedTermsSummary();
                    }
                    else if (tagObj is ProjectPaymentTerm directPaymentTerm)
                    {
                        // Handle old tag structure for compatibility
                        // Reset user-edited flag when removing a payment term
                        _userEditedPaidAmount = false;

                        RemovePaymentTermFromSelection(directPaymentTerm);
                        UpdateSelectedTermsSummary();
                        ValidateStep4();
                        UpdateStepVisibility();
                        UpdateSaveButtonState();

                        // Update the right side summary display
                        UpdateSelectedTermsSummary();
                    }
                }
            }
        }

        private void UpdateSelectedTermsSummary()
        {
            try
            {
                // Update the right side panel with enhanced details
                var rightSideCard = FindName("RightSideSelectedTermsCard") as MaterialDesignThemes.Wpf.Card;
                var termsDetailsPanel = FindName("RightSideTermsDetailsPanel") as StackPanel;
                var totalTermsCountText = FindName("TotalTermsCountText") as TextBlock;
                var totalPercentageText = FindName("TotalPercentageText") as TextBlock;
                var totalAmountText = FindName("TotalAmountText") as TextBlock;

                if (rightSideCard != null && termsDetailsPanel != null)
                {
                    if (_selectedInvoicePaymentTerms?.Any() == true)
                    {
                        rightSideCard.Visibility = Visibility.Visible;
                        termsDetailsPanel.Children.Clear();

                        var totalPercentage = _selectedInvoicePaymentTerms.Sum(ipt => ipt.AppliedPercentage);
                        var totalAmount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);
                        var termsCount = _selectedInvoicePaymentTerms.Count;

                        // Create professional detailed cards for each payment term
                        foreach (var ipt in _selectedInvoicePaymentTerms.OrderBy(x => x.DisplayOrder))
                        {
                            var termCard = new MaterialDesignThemes.Wpf.Card
                            {
                                Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                                Margin = new Thickness(0, 0, 0, 12),
                                Padding = new Thickness(0)
                            };

                            var termPanel = new StackPanel();

                            // Header with gradient background
                            var headerBorder = new Border
                            {
                                Background = new LinearGradientBrush(
                                    Color.FromRgb(25, 118, 210),
                                    Color.FromRgb(33, 150, 243),
                                    90),
                                Padding = new Thickness(16, 12, 16, 12),
                                CornerRadius = new CornerRadius(4)
                            };

                            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };

                            var termIcon = new MaterialDesignThemes.Wpf.PackIcon
                            {
                                Kind = MaterialDesignThemes.Wpf.PackIconKind.CheckCircle,
                                Width = 16,
                                Height = 16,
                                Foreground = new SolidColorBrush(Colors.White),
                                VerticalAlignment = VerticalAlignment.Center,
                                Margin = new Thickness(0, 0, 8, 0)
                            };
                            headerPanel.Children.Add(termIcon);

                            var titleText = new TextBlock
                            {
                                Text = ipt.ProjectPaymentTerm?.Description ?? "Payment Term",
                                FontWeight = FontWeights.SemiBold,
                                FontSize = 13,
                                Foreground = new SolidColorBrush(Colors.White)
                            };
                            headerPanel.Children.Add(titleText);

                            // Add site info if available
                            if (!string.IsNullOrEmpty(ipt.SiteName))
                            {
                                var siteText = new TextBlock
                                {
                                    Text = $"📍 {ipt.SiteName}",
                                    FontSize = 11,
                                    Foreground = new SolidColorBrush(Colors.White),
                                    Margin = new Thickness(8, 0, 0, 0),
                                    VerticalAlignment = VerticalAlignment.Center
                                };
                                headerPanel.Children.Add(siteText);
                            }

                            headerBorder.Child = headerPanel;
                            termPanel.Children.Add(headerBorder);

                            // Content area
                            var contentPanel = new StackPanel { Margin = new Thickness(16, 12, 16, 12) };

                            // Percentage info with icon
                            var percentagePanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 8) };
                            var percentIcon = new MaterialDesignThemes.Wpf.PackIcon
                            {
                                Kind = MaterialDesignThemes.Wpf.PackIconKind.Percent,
                                Width = 14,
                                Height = 14,
                                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                                VerticalAlignment = VerticalAlignment.Center,
                                Margin = new Thickness(0, 0, 6, 0)
                            };
                            percentagePanel.Children.Add(percentIcon);

                            var percentageText = new TextBlock
                            {
                                Text = $"Applied: {ipt.AppliedPercentage:F2}%",
                                FontSize = 12,
                                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                                FontWeight = FontWeights.Medium
                            };
                            percentagePanel.Children.Add(percentageText);
                            contentPanel.Children.Add(percentagePanel);

                            // Amount info with icon
                            var amountPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 8) };
                            var dollarIcon = new MaterialDesignThemes.Wpf.PackIcon
                            {
                                Kind = MaterialDesignThemes.Wpf.PackIconKind.CurrencyUsd,
                                Width = 14,
                                Height = 14,
                                Foreground = new SolidColorBrush(Color.FromRgb(233, 30, 99)),
                                VerticalAlignment = VerticalAlignment.Center,
                                Margin = new Thickness(0, 0, 6, 0)
                            };
                            amountPanel.Children.Add(dollarIcon);

                            // Calculate the correct amount for display
                            var displayAmount = CalculateDisplayAmountForTerm(ipt);
                            var amountText = new TextBlock
                            {
                                Text = $"Amount: ${displayAmount:N2}",
                                FontSize = 12,
                                FontWeight = FontWeights.Bold,
                                Foreground = new SolidColorBrush(Color.FromRgb(233, 30, 99))
                            };
                            amountPanel.Children.Add(amountText);
                            contentPanel.Children.Add(amountPanel);

                            // Detailed payment breakdown info
                            var infoPanel = new StackPanel { Margin = new Thickness(0, 8, 0, 0) };

                            if (ipt.ProjectPaymentTerm != null)
                            {
                                var originalPercentage = ipt.ProjectPaymentTerm.Percentage;
                                var appliedPercentage = ipt.AppliedPercentage;
                                var sitesCount = _currentProject?.NumberOfSites ?? 1;
                                var isAllSites = AllSitesRadio?.IsChecked == true;

                                // Original term info
                                var originalText = new TextBlock
                                {
                                    Text = $"Original Term: {originalPercentage:F2}%",
                                    FontSize = 10,
                                    Foreground = new SolidColorBrush(Color.FromRgb(117, 117, 117)),
                                    Margin = new Thickness(0, 0, 0, 4)
                                };
                                infoPanel.Children.Add(originalText);

                                // Payment breakdown explanation
                                var breakdownText = new TextBlock
                                {
                                    FontSize = 10,
                                    FontWeight = FontWeights.Medium,
                                    Margin = new Thickness(0, 0, 0, 4)
                                };

                                if (appliedPercentage == originalPercentage)
                                {
                                    // Full payment term
                                    if (isAllSites && sitesCount > 1)
                                    {
                                        breakdownText.Text = $"💡 Full term: {originalPercentage:F2}% applied to all {sitesCount} sites";
                                        breakdownText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                                    }
                                    else
                                    {
                                        var perSitePercentage = originalPercentage / sitesCount;
                                        breakdownText.Text = $"💡 Full term: {perSitePercentage:F2}% per site × {sitesCount} sites = {originalPercentage:F2}%";
                                        breakdownText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                                    }
                                }
                                else
                                {
                                    // Partial payment term - show the math clearly
                                    var partialPercentage = (appliedPercentage / originalPercentage) * 100;
                                    if (isAllSites && sitesCount > 1)
                                    {
                                        breakdownText.Text = $"⚡ {partialPercentage:F1}% of {originalPercentage:F2}% = {appliedPercentage:F2}% for all {sitesCount} sites";
                                        breakdownText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                                    }
                                    else
                                    {
                                        var perSiteAmount = appliedPercentage / sitesCount;
                                        breakdownText.Text = $"⚡ {partialPercentage:F1}% of {originalPercentage:F2}% = {perSiteAmount:F2}% per site × {sitesCount} sites";
                                        breakdownText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                                    }
                                }
                                infoPanel.Children.Add(breakdownText);

                                // Check for previous usage of this payment term
                                _ = AddPreviousUsageInfoAsync(infoPanel, ipt);

                                // Site application info
                                var siteText = new TextBlock
                                {
                                    FontSize = 10,
                                    Foreground = new SolidColorBrush(Color.FromRgb(96, 125, 139)),
                                    FontStyle = FontStyles.Italic,
                                    Margin = new Thickness(0, 4, 0, 0)
                                };

                                if (isAllSites && sitesCount > 1)
                                {
                                    siteText.Text = $"📍 Applied to all {sitesCount} sites combined";
                                }
                                else if (sitesCount > 1)
                                {
                                    siteText.Text = $"📍 Applied to single site (Project has {sitesCount} sites total)";
                                }
                                else
                                {
                                    siteText.Text = "📍 Applied to single site project";
                                }
                                infoPanel.Children.Add(siteText);
                            }

                            contentPanel.Children.Add(infoPanel);
                            termPanel.Children.Add(contentPanel);

                            termCard.Content = termPanel;
                            termsDetailsPanel.Children.Add(termCard);
                        }

                        // Update totals
                        if (totalTermsCountText != null)
                            totalTermsCountText.Text = $"Total Terms: {termsCount}";
                        if (totalPercentageText != null)
                            totalPercentageText.Text = $"Total Percentage: {totalPercentage:F2}%";
                        if (totalAmountText != null)
                            totalAmountText.Text = $"Total Amount: ${totalAmount:F2}";

                        // Update comprehensive site info
                        var siteInfoText = FindName("PaymentSiteInfoText") as TextBlock;
                        if (siteInfoText != null)
                        {
                            var sitesCount = _currentProject?.NumberOfSites ?? 1;
                            var isAllSites = AllSitesRadio?.IsChecked == true;

                            // Calculate total percentage breakdown
                            var fullTermsTotal = _selectedInvoicePaymentTerms
                                .Where(ipt => ipt.AppliedPercentage == ipt.ProjectPaymentTerm?.Percentage)
                                .Sum(ipt => ipt.AppliedPercentage);
                            var partialTermsTotal = _selectedInvoicePaymentTerms
                                .Where(ipt => ipt.AppliedPercentage != ipt.ProjectPaymentTerm?.Percentage)
                                .Sum(ipt => ipt.AppliedPercentage);

                            var infoText = "";
                            if (isAllSites && sitesCount > 1)
                            {
                                infoText = $"📊 Coverage: {totalPercentage:F2}% across all {sitesCount} sites";
                            }
                            else if (sitesCount > 1)
                            {
                                var perSiteTotal = totalPercentage / sitesCount;
                                infoText = $"📊 Coverage: {perSiteTotal:F2}% per site (Project has {sitesCount} sites)";
                            }
                            else
                            {
                                infoText = $"📊 Coverage: {totalPercentage:F2}% for single site project";
                            }

                            if (partialTermsTotal > 0)
                            {
                                infoText += $" | Partial terms: {partialTermsTotal:F2}%";
                            }

                            siteInfoText.Text = infoText;
                        }

                        // Update amount textbox with calculated total
                        if (AmountTextBox != null)
                        {
                            AmountTextBox.Text = totalAmount.ToString("F2");
                        }
                    }
                    else
                    {
                        rightSideCard.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating selected terms summary: {ex.Message}");
            }
        }



        private void RemovePaymentTermFromSelection(ProjectPaymentTerm paymentTerm)
        {
            try
            {
                var existingTerm = _selectedInvoicePaymentTerms?.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);
                if (existingTerm != null && _selectedInvoicePaymentTerms != null)
                {
                    _selectedInvoicePaymentTerms.Remove(existingTerm);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing payment term from selection: {ex.Message}");
            }
        }

        private async void AddPaymentTermToSelection(ProjectPaymentTerm paymentTerm)
        {
            // Check if already selected
            if (_selectedInvoicePaymentTerms.Any(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id))
                return;

            // Base applied percentage - depends on mode
            decimal appliedPercentage;

            // Check if "Use full payment term" mode is enabled
            if (UseFullTermCheckBox?.IsChecked == true)
            {
                // In "Use full payment term" mode, use the REMAINING percentage only
                appliedPercentage = await GetRemainingPercentageForTermAndSite(paymentTerm);
                System.Diagnostics.Debug.WriteLine($"Use full term mode: Using remaining percentage {appliedPercentage:F2}% instead of full {paymentTerm.Percentage}%");
            }
            else
            {
                // Normal mode: use the full term percentage
                appliedPercentage = paymentTerm.Percentage;
            }

            // Get site information from the UI
            int? selectedSiteId = null;
            string? selectedSiteName = null;

            // Use main site selection instead of individual payment term site selection
            if (_currentProject?.NumberOfSites > 1)
            {
                selectedSiteId = GetSelectedSiteId();
                selectedSiteName = GetSelectedSiteName();
            }

            // If user enabled "Custom % of term" and a custom value is typed for this row, try to read it
            try
            {

            }
            catch { /* ignore UI lookups */ }

            var invoicePaymentTerm = new InvoicePaymentTerm
            {
                ProjectPaymentTermId = paymentTerm.Id,
                ProjectPaymentTerm = paymentTerm,
                AppliedPercentage = appliedPercentage,
                DisplayOrder = _selectedInvoicePaymentTerms.Count + 1,
                CreatedDate = DateTime.Now,
                IsActive = true,
                ProjectSiteId = selectedSiteId,
                SiteName = selectedSiteName
            };

            // Update display percentage based on number of sites
            if (_currentProject != null)
            {
                invoicePaymentTerm.UpdateDisplayPercentage(_currentProject.NumberOfSites);
            }

            _selectedInvoicePaymentTerms.Add(invoicePaymentTerm);
            UpdateSelectedPaymentTermsDisplay();
            CalculateAmountsFromMultiplePaymentTerms();
            AutoFillFinancialFields();

            // Update available sites based on new payment terms selection
            _ = UpdateAvailableSites();

            // Auto-suggest next available site if payment terms have been used before
            _ = AutoSuggestNextSiteAsync();

            // Update the right side summary display
            UpdateSelectedTermsSummary();
        }



        private void UpdateSelectedPaymentTermsDisplay()
        {
            // Implementation removed - using new payment terms UI
        }

        private void CalculateAmountsFromMultiplePaymentTerms()
        {
            if (_currentProject == null || !_selectedInvoicePaymentTerms.Any())
                return;

            try
            {
                // Get the selected invoice type
                string selectedType = "";
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedType = selectedItem.Content?.ToString() ?? "";
                }

                // Determine which amount to use based on invoice type
                decimal baseAmount = selectedType switch
                {
                    // New naming convention
                    "Equipment (Software)" => _currentProject.SoftwareTasksAmount,
                    "Equipment (Hardware)" => _currentProject.HardwareTasksAmount,
                    "Equipment" => _currentProject.DistributableTasksAmount,

                    // Legacy Split Equipment types (for backward compatibility)
                    "Software Equipment" => _currentProject.SoftwareTasksAmount,
                    "Hardware Equipment" => _currentProject.HardwareTasksAmount,
                    "Hardware & Software Equipment" => _currentProject.DistributableTasksAmount,

                    // Legacy task types (for backward compatibility)
                    "Hardware & Software Tasks" => _currentProject.DistributableTasksAmount,
                    "Software Tasks" => _currentProject.SoftwareTasksAmount,
                    "Hardware Tasks" => _currentProject.HardwareTasksAmount,

                    // Other categories
                    "Services" => _currentProject.ServicesAmount,
                    "Spare Parts" => _currentProject.SparePartsAmount,
                    "Extra" => _currentProject.ExtraAmount,

                    // Check if it matches the custom extra category name
                    _ when !string.IsNullOrWhiteSpace(_currentProject.ExtraCategoryName) &&
                           selectedType.Equals(_currentProject.ExtraCategoryName, StringComparison.OrdinalIgnoreCase) => _currentProject.ExtraAmount,

                    _ => _currentProject.DistributableTasksAmount
                };

                // Check if "All Sites" option is selected for payment terms
                bool isPaymentTermsAllSitesMode = IsPaymentTermsAllSitesMode();

                // Calculate amounts for each selected payment term
                foreach (var invoicePaymentTerm in _selectedInvoicePaymentTerms)
                {
                    if (UseFullTermCheckBox?.IsChecked == true)
                    {
                        // Use full payment term mode - no site division at all
                        invoicePaymentTerm.CalculateAmount(baseAmount, 1);
                        invoicePaymentTerm.UpdateDisplayPercentage(1);
                        System.Diagnostics.Debug.WriteLine($"Full term mode: {invoicePaymentTerm.AppliedPercentage}% of {baseAmount} = {invoicePaymentTerm.CalculatedAmount}");
                    }
                    else if (CustomPctOfTermCheckBox?.IsChecked == true)
                    {
                        // Percentage of payment term mode - no site division
                        invoicePaymentTerm.CalculateAmount(baseAmount, 1);
                        invoicePaymentTerm.UpdateDisplayPercentage(1);
                        System.Diagnostics.Debug.WriteLine($"Percentage of term mode: {invoicePaymentTerm.AppliedPercentage}% of {baseAmount} = {invoicePaymentTerm.CalculatedAmount}");
                    }
                    else if (isPaymentTermsAllSitesMode)
                    {
                        // All Sites mode - use full percentage without division
                        invoicePaymentTerm.CalculateAmount(baseAmount, 1);
                        invoicePaymentTerm.UpdateDisplayPercentage(1);

                    }
                    else
                    {
                        // Normal site-specific calculation with division
                        invoicePaymentTerm.CalculateAmount(baseAmount, _currentProject.NumberOfSites);
                        invoicePaymentTerm.UpdateDisplayPercentage(_currentProject.NumberOfSites);
                        System.Diagnostics.Debug.WriteLine($"Site-specific mode: {invoicePaymentTerm.AppliedPercentage}% of {baseAmount} ÷ {_currentProject.NumberOfSites} = {invoicePaymentTerm.CalculatedAmount}");
                    }
                }

                // Update the main amount field
                var totalAmount = _selectedInvoicePaymentTerms.Sum(ipt => ipt.CalculatedAmount);
                AmountTextBox.Text = totalAmount.ToString("F2");

                UpdateSelectedPaymentTermsDisplay();
                UpdateCalculationSummary();
                AutoFillFinancialFields();

                // Update multiple terms summary
                if (PercentageRadio?.IsChecked == true)
                {
                    UpdateMultipleTermsSummary();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating amounts: {ex.Message}");
            }
        }

        private void CustomPercentageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox && textBox.DataContext is ProjectPaymentTerm paymentTerm)
            {
                var invoicePaymentTerm = _selectedInvoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == paymentTerm.Id);
                if (invoicePaymentTerm != null)
                {
                    // Apply custom percentage directly
                    if (decimal.TryParse(textBox.Text, out decimal customPercentage) && customPercentage > 0 && customPercentage <= 100)
                    {
                        invoicePaymentTerm.AppliedPercentage = customPercentage; // treat as absolute %
                    }

                    CalculateAmountsFromMultiplePaymentTerms();
                }
            }
        }

        private void ClearPaymentTermsButton_Click(object sender, RoutedEventArgs e)
        {
            _selectedInvoicePaymentTerms.Clear();
            UpdateSelectedPaymentTermsDisplay();
            AmountTextBox.Text = "0.00";


        }

        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        #endregion



        #region Load Existing Payment Terms

        private async System.Threading.Tasks.Task LoadExistingPaymentTerms()
        {
            if (_invoice?.Id == null) return;

            try
            {
                // Load existing invoice payment terms
                var existingTerms = await App.DataService.GetInvoicePaymentTermsAsync(_invoice.Id);

                if (existingTerms.Any())
                {
                    // Store the existing terms for later use
                    _existingPaymentTermIds = existingTerms.Select(t => t.ProjectPaymentTermId).ToList();

                    // Load available payment terms first, then select existing ones
                    await LoadAvailablePaymentTerms();

                    // Use a delay to ensure UI is loaded before selecting
                    await System.Threading.Tasks.Task.Delay(100);
                    await PopulateAvailablePaymentTermsList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading existing payment terms: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Load Data for Edit

        private async System.Threading.Tasks.Task LoadDataAndPopulateForEdit()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("LoadDataAndPopulateForEdit: Starting data load for edit mode");

                // Temporarily disable event handlers to prevent interference
                CommitmentComboBox.SelectionChanged -= CommitmentComboBox_SelectionChanged;

                // Load basic data first
                await LoadBasicData();

                // Populate all fields with saved data
                await PopulateFieldsAsync();

                // Force all steps to be enabled for editing
                UpdateStepVisibility();

                // Ensure type field is always editable in edit mode
                if (TypeComboBox != null)
                {
                    TypeComboBox.IsEnabled = true;
                    System.Diagnostics.Debug.WriteLine("LoadDataAndPopulateForEdit: TypeComboBox enabled for editing");
                }

                // Update save button state for editing mode
                UpdateSaveButtonState();

                // Re-enable event handlers
                CommitmentComboBox.SelectionChanged += CommitmentComboBox_SelectionChanged;

                System.Diagnostics.Debug.WriteLine("LoadDataAndPopulateForEdit: Data load completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadDataAndPopulateForEdit: Error - {ex.Message}");
                MessageBox.Show($"Error loading invoice data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadBasicData()
        {
            // Load current project
            await LoadCurrentProject();

            // Load invoice types based on current project
            RestoreDefaultInvoiceTypes();

            // Set title if project is available
            if (_currentProject != null)
            {
                if (_isEdit)
                {
                    Title = $"Edit Invoice - {_currentProject.Name}";
                }
                else
                {
                    Title = $"Add New Invoice - {_currentProject.Name}";
                }
            }
        }

        #endregion

        #region Update Payment Terms

        private async System.Threading.Tasks.Task UpdateInvoicePaymentTerms(int invoiceId)
        {
            try
            {
                // Get existing payment terms
                var existingTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoiceId);

                // Get current selected terms IDs
                var selectedTermIds = _selectedInvoicePaymentTerms.Select(t => t.ProjectPaymentTermId).ToList();
                var existingTermIds = existingTerms.Select(t => t.ProjectPaymentTermId).ToList();

                // Debug logging
                System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Existing terms: [{string.Join(", ", existingTermIds)}]");
                System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Selected terms: [{string.Join(", ", selectedTermIds)}]");

                // Check if the payment terms have actually changed
                bool termsChanged = !selectedTermIds.OrderBy(x => x).SequenceEqual(existingTermIds.OrderBy(x => x));


                System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Terms changed: {termsChanged}");

                if (termsChanged)
                {
                    System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Deleting {existingTerms.Count} existing terms");

                    // Delete existing payment terms only if they changed
                    await App.DataService.DeleteInvoicePaymentTermsAsync(invoiceId);

                    System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Adding {_selectedInvoicePaymentTerms.Count} new terms");

                    // Add new payment terms
                    foreach (var invoicePaymentTerm in _selectedInvoicePaymentTerms)
                    {
                        // Create a clean copy to avoid tracking issues
                        var cleanPaymentTerm = new InvoicePaymentTerm
                        {
                            InvoiceId = invoiceId,
                            ProjectPaymentTermId = invoicePaymentTerm.ProjectPaymentTermId,
                            AppliedPercentage = invoicePaymentTerm.AppliedPercentage,
                            CalculatedAmount = invoicePaymentTerm.CalculatedAmount,
                            DisplayOrder = invoicePaymentTerm.DisplayOrder,
                            Notes = invoicePaymentTerm.Notes,
                            CreatedDate = DateTime.Now,
                            IsActive = true
                        };

                        // Validate the payment term before saving
                        if (cleanPaymentTerm.ProjectPaymentTermId <= 0)
                        {
                            throw new InvalidOperationException($"Invalid ProjectPaymentTermId: {cleanPaymentTerm.ProjectPaymentTermId}");
                        }

                        if (cleanPaymentTerm.InvoiceId <= 0)
                        {
                            throw new InvalidOperationException($"Invalid InvoiceId: {cleanPaymentTerm.InvoiceId}");
                        }

                        await App.DataService.AddInvoicePaymentTermAsync(cleanPaymentTerm);
                        System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - Added payment term {cleanPaymentTerm.ProjectPaymentTermId}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Invoice {invoiceId} - No changes to payment terms, skipping update");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating payment terms for invoice {invoiceId}: {ex.Message}");
                MessageBox.Show($"Error updating payment terms: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        #endregion

        /// <summary>
        /// تطبيق قالب الفاتورة تلقائياً عند اختيار النوع - تم إلغاؤه
        /// </summary>
        private void ApplyInvoiceTemplate(string invoiceType)
        {
            // لا نطبق أي قوالب افتراضية - المستخدم يدخل البيانات بنفسه
            System.Diagnostics.Debug.WriteLine($"No template applied for invoice type: {invoiceType}");
        }

        /// <summary>
        /// الحصول على معلومات إكمال الموقع
        /// </summary>
        private async Task<string> GetSiteCompletionInfo(int siteId, string invoiceType)
        {
            try
            {
                if (!_selectedInvoicePaymentTerms.Any())
                {
                    return "لم يتم اختيار شروط دفع";
                }

                var completionInfo = new List<string>();

                foreach (var paymentTerm in _selectedInvoicePaymentTerms)
                {
                    var paymentTermDescription = paymentTerm.ProjectPaymentTerm?.Description ?? "";
                    var completedPercentage = await SitePercentageCalculationService.GetCompletedPercentageForSite(
                        _currentProject?.Id ?? 0, siteId, invoiceType, paymentTermDescription);

                    var remainingPercentage = 100 - completedPercentage;
                    completionInfo.Add($"• {paymentTermDescription}: متبقي {remainingPercentage:F1}%");
                }

                return string.Join("\n", completionInfo);
            }
            catch
            {
                return "خطأ في حساب المعلومات";
            }
        }

        /// <summary>
        /// عرض نافذة حالة النسب للمواقع
        /// </summary>
        private void ViewSiteStatusButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("يرجى اختيار مشروع أولاً", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!_selectedInvoicePaymentTerms.Any())
                {
                    MessageBox.Show("يرجى اختيار شروط الدفع أولاً", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var selectedInvoiceType = "";
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedInvoiceType = selectedItem.Content?.ToString() ?? "SW & HW";
                }

                // Show status for the first selected payment term
                var firstPaymentTerm = _selectedInvoicePaymentTerms.First();
                var paymentTermDescription = firstPaymentTerm.ProjectPaymentTerm?.Description ?? "";

                var statusWindow = new Views.SitePercentageStatusWindow(
                    _currentProject.Id, selectedInvoiceType, paymentTermDescription);
                statusWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error displaying site status: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Clear all stored and temporary data to ensure a fresh start
        /// </summary>
        private void ClearStoredData()
        {
            try
            {
                // Clear all form fields
                ClearAllFormFields();

                // Clear internal collections
                _selectedInvoicePaymentTerms.Clear();
                _availablePaymentTerms.Clear();
                _projectPaymentTerms.Clear();
                _projectSites.Clear();
                _availableCommitments.Clear();
                _existingPaymentTermIds.Clear();

                // Reset completion flags
                _step1Complete = false;
                _step2Complete = false;
                _step3Complete = false;
                _step4Complete = false;
                _step5Complete = false;
                _step6Complete = false;

                // Clear cached project reference
                _currentProject = null;
                _selectedPaymentTerm = null;

                System.Diagnostics.Debug.WriteLine("All stored data cleared for fresh start");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing stored data: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear all form fields
        /// </summary>
        private void ClearAllFormFields()
        {
            try
            {
                // Clear text fields
                if (InvoiceNumberTextBox != null) InvoiceNumberTextBox.Text = "";
                if (AmountTextBox != null) AmountTextBox.Text = "";
                if (DescriptionTextBox != null) DescriptionTextBox.Text = "";
                if (FixedAmountDetailsTextBox != null) FixedAmountDetailsTextBox.Text = "";
                if (AttachedFilePathTextBox != null) AttachedFilePathTextBox.Text = "";
                if (LetterFilePathTextBox != null) LetterFilePathTextBox.Text = "";
                if (ExchangeRateTextBox != null) ExchangeRateTextBox.Text = "";
                if (PaidAmountTextBox != null) PaidAmountTextBox.Text = "";

                // Reset combo boxes (project is fixed)
                if (TypeComboBox != null) TypeComboBox.SelectedIndex = -1;
                if (CommitmentComboBox != null) CommitmentComboBox.SelectedIndex = -1;
                if (SiteSelectionComboBox != null) SiteSelectionComboBox.SelectedIndex = -1;

                // Reset radio buttons to defaults
                if (FixedAmountRadio != null) FixedAmountRadio.IsChecked = true;
                if (PercentageRadio != null) PercentageRadio.IsChecked = false;
                if (SingleSiteRadio != null) SingleSiteRadio.IsChecked = true;
                if (AllSitesRadio != null) AllSitesRadio.IsChecked = false;

                // Reset checkboxes
                if (CustomPctOfTermCheckBox != null) CustomPctOfTermCheckBox.IsChecked = false;
                if (UseFullTermCheckBox != null) UseFullTermCheckBox.IsChecked = false;

                // Clear date pickers
                if (ArrivalDatePicker != null) ArrivalDatePicker.SelectedDate = null;
                if (SignatureDatePicker != null) SignatureDatePicker.SelectedDate = null;

                // Clear dynamic panels
                if (PaymentTermsStackPanel != null) PaymentTermsStackPanel.Children.Clear();

                // Reset file paths
                _selectedFilePath = string.Empty;
                _selectedLetterPath = string.Empty;

                System.Diagnostics.Debug.WriteLine("All form fields cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing form fields: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculate the correct display amount for a payment term
        /// </summary>
        private decimal CalculateDisplayAmountForTerm(InvoicePaymentTerm ipt)
        {
            if (_currentProject == null) return 0;

            try
            {
                // Get the selected invoice type
                string selectedType = "";
                if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedType = selectedItem.Content?.ToString() ?? "";
                }

                // Determine which amount to use based on invoice type
                decimal baseAmount = selectedType switch
                {
                    // New naming convention
                    "Equipment (Software)" => _currentProject.SoftwareTasksAmount,
                    "Equipment (Hardware)" => _currentProject.HardwareTasksAmount,
                    "Equipment" => _currentProject.DistributableTasksAmount,

                    // Legacy naming
                    "Software Equipment" => _currentProject.SoftwareTasksAmount,
                    "Hardware Equipment" => _currentProject.HardwareTasksAmount,
                    "Hardware & Software Equipment" => _currentProject.DistributableTasksAmount,
                    "Hardware & Software Tasks" => _currentProject.DistributableTasksAmount,
                    "Software Tasks" => _currentProject.SoftwareTasksAmount,
                    "Hardware Tasks" => _currentProject.HardwareTasksAmount,

                    // Other categories
                    "Services" => _currentProject.ServicesAmount,
                    "Spare Parts" => _currentProject.SparePartsAmount,
                    "Extra" => _currentProject.ExtraAmount,

                    // Check if it matches the custom extra category name
                    _ when !string.IsNullOrWhiteSpace(_currentProject.ExtraCategoryName) &&
                           selectedType.Equals(_currentProject.ExtraCategoryName, StringComparison.OrdinalIgnoreCase) => _currentProject.ExtraAmount,

                    _ => _currentProject.DistributableTasksAmount
                };

                // Calculate amount: base amount × applied percentage
                var calculatedAmount = baseAmount * (ipt.AppliedPercentage / 100);

                // Apply site division if needed (same logic as main calculation)
                bool isAllSitesMode = AllSitesRadio?.IsChecked == true;

                // Check for special modes
                if (UseFullTermCheckBox?.IsChecked == true)
                {
                    // Full term mode - no site division
                    return calculatedAmount;
                }
                else if (CustomPctOfTermCheckBox?.IsChecked == true)
                {
                    // Custom percentage mode - no site division
                    return calculatedAmount;
                }
                else if (_currentProject.NumberOfSites > 1 && !isAllSitesMode)
                {
                    // Normal site-specific mode with division
                    calculatedAmount = calculatedAmount / _currentProject.NumberOfSites;
                }
                // For All Sites mode, no division is applied (calculatedAmount remains full)

                return calculatedAmount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating display amount: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Auto-suggest next available site when payment terms have been used before
        /// </summary>
        private async System.Threading.Tasks.Task AutoSuggestNextSiteAsync()
        {
            try
            {
                if (_currentProject == null || !_selectedInvoicePaymentTerms.Any()) return;

                // Get all project sites
                var allSites = await App.DataService.GetProjectSitesAsync(_currentProject.Id);
                if (allSites.Count <= 1) return; // No need for auto-suggestion in single site projects

                // Check which sites have been used for the selected payment terms
                var usedSiteIds = new HashSet<int>();

                foreach (var selectedTerm in _selectedInvoicePaymentTerms)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                    foreach (var invoice in projectInvoices)
                    {
                        // Skip current invoice if editing
                        if (_isEdit && _invoice != null && invoice.Id == _invoice.Id) continue;

                        var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                        var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt => ipt.ProjectPaymentTermId == selectedTerm.ProjectPaymentTermId);

                        if (usedTerm != null && invoice.ProjectSiteId.HasValue)
                        {
                            usedSiteIds.Add(invoice.ProjectSiteId.Value);
                        }
                    }
                }

                // Find the next available site
                var availableSites = allSites.Where(s => !usedSiteIds.Contains(s.Id)).ToList();
                if (availableSites.Any())
                {
                    var nextSite = availableSites.First();

                    // Auto-select the next available site
                    var siteComboBox = FindName("SiteComboBox") as ComboBox;
                    if (siteComboBox != null && siteComboBox.SelectedValue == null)
                    {
                        siteComboBox.SelectedValue = nextSite.Id;
                        System.Diagnostics.Debug.WriteLine($"Auto-suggested next site: {nextSite.SiteName}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error auto-suggesting next site: {ex.Message}");
            }
        }

        /// <summary>
        /// Add previous usage information for a payment term
        /// </summary>
        private async System.Threading.Tasks.Task AddPreviousUsageInfoAsync(StackPanel infoPanel, InvoicePaymentTerm ipt)
        {
            try
            {
                if (_currentProject == null || ipt.ProjectPaymentTerm == null) return;

                // Get all invoices for this project that use this payment term
                var allInvoices = await App.DataService.GetInvoicesAsync();
                var projectInvoices = allInvoices.Where(i => i.ProjectId == _currentProject.Id).ToList();

                decimal totalUsedPercentage = 0;
                var usedSites = new List<string>();

                foreach (var invoice in projectInvoices)
                {
                    // Skip current invoice if editing
                    if (_isEdit && _invoice != null && invoice.Id == _invoice.Id) continue;

                    var invoicePaymentTerms = await App.DataService.GetInvoicePaymentTermsAsync(invoice.Id);
                    var usedTerm = invoicePaymentTerms.FirstOrDefault(ipt2 => ipt2.ProjectPaymentTermId == ipt.ProjectPaymentTermId);

                    if (usedTerm != null)
                    {
                        totalUsedPercentage += usedTerm.AppliedPercentage;

                        // Get site name
                        if (invoice.ProjectSiteId.HasValue)
                        {
                            var sites = await App.DataService.GetProjectSitesAsync(_currentProject.Id);
                            var site = sites.FirstOrDefault(s => s.Id == invoice.ProjectSiteId.Value);
                            if (site != null)
                            {
                                usedSites.Add($"{site.SiteName} ({usedTerm.AppliedPercentage:F2}%)");
                            }
                        }
                    }
                }

                if (totalUsedPercentage > 0)
                {
                    var usageText = new TextBlock
                    {
                        FontSize = 10,
                        Foreground = new SolidColorBrush(Color.FromRgb(156, 39, 176)), // Purple
                        FontWeight = FontWeights.Medium,
                        Margin = new Thickness(0, 4, 0, 0)
                    };

                    var remainingPercentage = ipt.ProjectPaymentTerm.Percentage - totalUsedPercentage;
                    if (usedSites.Any())
                    {
                        usageText.Text = $"📋 Previously used: {totalUsedPercentage:F2}% in {string.Join(", ", usedSites)}";
                    }
                    else
                    {
                        usageText.Text = $"📋 Previously used: {totalUsedPercentage:F2}% | Remaining: {remainingPercentage:F2}%";
                    }

                    infoPanel.Children.Add(usageText);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting previous usage info: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear payment terms selection when project changes
        /// </summary>
        private void ClearPaymentTermsSelection()
        {
            try
            {
                // Clear selected payment terms
                _selectedInvoicePaymentTerms.Clear();

                // Clear the right side display
                var rightSideCard = FindName("RightSideSelectedTermsCard") as MaterialDesignThemes.Wpf.Card;
                if (rightSideCard != null)
                {
                    rightSideCard.Visibility = Visibility.Collapsed;
                }

                // Clear the payment terms details panel
                var termsDetailsPanel = FindName("RightSideTermsDetailsPanel") as StackPanel;
                if (termsDetailsPanel != null)
                {
                    termsDetailsPanel.Children.Clear();
                }

                // Reset amount textbox
                if (AmountTextBox != null)
                {
                    AmountTextBox.Text = "";
                }

                System.Diagnostics.Debug.WriteLine("Payment terms selection cleared due to project change");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing payment terms selection: {ex.Message}");
            }
        }

    }
}