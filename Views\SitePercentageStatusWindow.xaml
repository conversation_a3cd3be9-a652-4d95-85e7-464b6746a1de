<Window x:Class="FinancialTracker.Views.SitePercentageStatusWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="حالة النسب للمواقع" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock x:Name="WindowTitle" Text="📊 حالة النسب للمواقع" 
                       FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center" Margin="0,0,0,8"/>
            <TextBlock x:Name="WindowSubtitle" Text="عرض النسب المكتملة والمتبقية لكل موقع" 
                       FontSize="14" Opacity="0.7" 
                       HorizontalAlignment="Center"/>
        </StackPanel>

        <!-- Sites Status Grid -->
        <DataGrid x:Name="SitesStatusDataGrid" Grid.Row="1"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  SelectionMode="Single"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  AlternatingRowBackground="#F5F5F5"
                  FontSize="14">
            
            <DataGrid.Columns>
                <!-- Site Name -->
                <DataGridTextColumn Header="اسم الموقع" 
                                    Binding="{Binding SiteName}" 
                                    Width="200"
                                    ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
                
                <!-- Completed Percentage -->
                <DataGridTextColumn Header="النسبة المكتملة" 
                                    Width="150"
                                    ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                    <DataGridTextColumn.Binding>
                        <Binding Path="CompletedPercentage" StringFormat="{}{0:F1}%"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>
                
                <!-- Remaining Percentage -->
                <DataGridTextColumn Header="النسبة المتبقية" 
                                    Width="150"
                                    ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                    <DataGridTextColumn.Binding>
                        <Binding Path="RemainingPercentage" StringFormat="{}{0:F1}%"/>
                    </DataGridTextColumn.Binding>
                </DataGridTextColumn>
                
                <!-- Status -->
                <DataGridTextColumn Header="الحالة" 
                                    Binding="{Binding StatusDisplay}" 
                                    Width="*"
                                    ElementStyle="{StaticResource MaterialDesignDataGridTextColumnStyle}"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Summary Panel -->
        <Border Grid.Row="2" Background="#E3F2FD" CornerRadius="8" Padding="16" Margin="0,16,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Sites -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="MapMarker" Width="24" Height="24" 
                                             Foreground="#1976D2" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TotalSitesText" Text="0" FontSize="20" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <TextBlock Text="إجمالي المواقع" FontSize="12" Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Available Sites -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" 
                                             Foreground="#4CAF50" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="AvailableSitesText" Text="0" FontSize="20" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <TextBlock Text="مواقع متاحة" FontSize="12" Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Complete Sites -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Lock" Width="24" Height="24" 
                                             Foreground="#FF9800" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="CompleteSitesText" Text="0" FontSize="20" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <TextBlock Text="مواقع مكتملة" FontSize="12" Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Average Completion -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Percent" Width="24" Height="24" 
                                             Foreground="#9C27B0" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="AverageCompletionText" Text="0%" FontSize="20" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    <TextBlock Text="متوسط الإكمال" FontSize="12" Opacity="0.7" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
