#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using FinancialTracker.Services;

namespace FinancialTracker
{
    public partial class ProjectDialog : Window
    {
        // Removed PaymentTermsListView_SizeChanged as columns are now fixed width
        private Project? _project;
        private bool _isEdit;
        private string _selectedPOFilePath = string.Empty;
        private List<ProjectPaymentTerm> _paymentTerms = new List<ProjectPaymentTerm>();

        private void SplitTasksCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            UpdateSplitTasksUI();
            CalculatePOTotal(); // Recalculate when split option changes
        }

        private void UpdateSplitTasksUI()
        {
            var split = SplitTasksCheckBox.IsChecked == true;
            CombinedTasksPanel.Visibility = split ? Visibility.Collapsed : Visibility.Visible;
            SplitTasksPanel.Visibility = split ? Visibility.Visible : Visibility.Collapsed;

            // Handle services amount between combined and split modes
            if (split)
            {
                // Copy services amount from combined to split if needed
                if (!string.IsNullOrWhiteSpace(ManualServicesAmountTextBox?.Text) &&
                    string.IsNullOrWhiteSpace(ManualServicesAmountSplitTextBox?.Text))
                {
                    ManualServicesAmountSplitTextBox!.Text = ManualServicesAmountTextBox.Text;
                }
            }
            else
            {
                // Copy services amount from split to combined if needed
                if (!string.IsNullOrWhiteSpace(ManualServicesAmountSplitTextBox?.Text) &&
                    string.IsNullOrWhiteSpace(ManualServicesAmountTextBox?.Text))
                {
                    ManualServicesAmountTextBox!.Text = ManualServicesAmountSplitTextBox.Text;
                }
            }

            // Update category choices based on split option
            UpdatePaymentCategoryComboBox();
        }


        // Event handlers for automatic calculation
        private void NameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // No validation needed - just keep it simple
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // No validation needed - just keep it simple
        }

        private void PODatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // No validation needed - just keep it simple
        }

        private void POAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // No validation needed - just keep it simple
        }

        public ProjectDialog(Project? project = null)
        {
            try
            {
                InitializeComponent();
                _project = project;
                _isEdit = project != null;

                if (_isEdit && _project != null)
                {
                    Title = "Edit Project";
                    NameTextBox.Text = _project.Name;
                    DescriptionTextBox.Text = _project.Description;
                    PODatePicker.SelectedDate = _project.PODate;
                    POAmountTextBox.Text = _project.POAmount > 0 ? _project.POAmount.ToString("F2") : "";

                    // Set split tasks option
                    SplitTasksCheckBox.IsChecked = _project.SplitTasksIntoHardwareAndSoftware;

                    // Load amounts based on split option
                    if (_project.SplitTasksIntoHardwareAndSoftware)
                    {
                        ManualSoftwareTasksAmountTextBox.Text = _project.ManualSoftwareTasksAmount > 0 ? _project.ManualSoftwareTasksAmount.ToString("F2") : "";
                        ManualHardwareTasksAmountTextBox.Text = _project.ManualHardwareTasksAmount > 0 ? _project.ManualHardwareTasksAmount.ToString("F2") : "";
                        ManualServicesAmountSplitTextBox.Text = _project.ManualServicesAmount > 0 ? _project.ManualServicesAmount.ToString("F2") : "";
                    }
                    else
                    {
                        ManualTasksAmountTextBox.Text = _project.ManualTasksAmount > 0 ? _project.ManualTasksAmount.ToString("F2") : "";
                        ManualServicesAmountTextBox.Text = _project.ManualServicesAmount > 0 ? _project.ManualServicesAmount.ToString("F2") : "";
                    }

                    // Load Spare Parts
                    SparePartsAmountTextBox.Text = _project.ManualSparePartsAmount > 0 ? _project.ManualSparePartsAmount.ToString("F2") : "";

                    // Load Extra
                    ExtraNameTextBox.Text = _project.ExtraCategoryName ?? "";
                    ExtraAmountTextBox.Text = _project.ManualExtraAmount > 0 ? _project.ManualExtraAmount.ToString("F2") : "";

                    // Load Down Payment
                    HasDownPaymentCheckBox.IsChecked = _project.HasDownPayment;
                    DownPaymentPercentageTextBox.Text = _project.DownPaymentPercentage > 0 ? _project.DownPaymentPercentage.ToString("F2") : "";
                    DownPaymentPercentageTextBox.IsEnabled = _project.HasDownPayment;

                    // Set status - map to English
                    var statusMapping = new System.Collections.Generic.Dictionary<string, int>
                    {
                        { "Active", 0 },
                        { "On Hold", 1 },
                        { "Completed", 2 },
                        { "Cancelled", 3 }
                    };

                    if (statusMapping.ContainsKey(_project.Status))
                    {
                        StatusComboBox.SelectedIndex = statusMapping[_project.Status];
                    }
                    else
                    {
                        StatusComboBox.SelectedIndex = 0; // Default to Active
                    }

                    // Set site configuration
                    NumberOfSitesTextBox.Text = _project.NumberOfSites.ToString();
                    // Sites will be loaded automatically when needed

                    // Load PO file information
                    if (!string.IsNullOrEmpty(_project.POFileName))
                    {
                        POFileTextBox.Text = _project.POFileName;
                    }
                }
                else
                {
                    Title = "Add New Project";
                    StatusComboBox.SelectedIndex = 0; // Default to Active

                    // Initialize spare parts and extra for new projects
                    SparePartsAmountTextBox.Text = "";
                    ExtraNameTextBox.Text = "";
                    ExtraAmountTextBox.Text = "";
                }

                // Initialize payment terms UI defaults
                UpdatePaymentCategoryComboBox();
                PaymentTermsListView.ItemsSource = _paymentTerms;
                UpdatePaymentTermsTotals();

                // Initialize split tasks UI
                SplitTasksCheckBox.IsChecked = _project?.SplitTasksIntoHardwareAndSoftware ?? false;
                UpdateSplitTasksUI();

                // If editing, load existing payment terms
                if (_isEdit && _project != null)
                {
                    _ = LoadExistingPaymentTerms();
                }

                // Initialize UI
                Loaded += (s, e) => {
                    // All steps are now visible by default
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ProjectDialog constructor: {ex.Message}");
                MessageBox.Show($"Error initializing project dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("Project name is required.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate PO Amount
                decimal poAmount = 0;
                if (!string.IsNullOrWhiteSpace(POAmountTextBox.Text))
                {
                    if (!decimal.TryParse(POAmountTextBox.Text, out poAmount) || poAmount < 0)
                    {
                        MessageBox.Show("PO value must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Manual Tasks Amount
                decimal manualTasksAmount = 0;
                if (!string.IsNullOrWhiteSpace(ManualTasksAmountTextBox.Text))
                {
                    if (!decimal.TryParse(ManualTasksAmountTextBox.Text, out manualTasksAmount) || manualTasksAmount < 0)
                    {
                        MessageBox.Show("Tasks amount must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Manual Services Amount based on current mode
                decimal manualServicesAmount = 0;
                string servicesAmountText = "";

                if (SplitTasksCheckBox.IsChecked == true)
                {
                    servicesAmountText = ManualServicesAmountSplitTextBox?.Text ?? "";
                }
                else
                {
                    servicesAmountText = ManualServicesAmountTextBox?.Text ?? "";
                }

                if (!string.IsNullOrWhiteSpace(servicesAmountText))
                {
                    if (!decimal.TryParse(servicesAmountText, out manualServicesAmount) || manualServicesAmount < 0)
                    {
                        MessageBox.Show("Services amount must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Spare Parts Amount
                decimal sparePartsAmount = 0;
                if (!string.IsNullOrWhiteSpace(SparePartsAmountTextBox.Text))
                {
                    if (!decimal.TryParse(SparePartsAmountTextBox.Text, out sparePartsAmount) || sparePartsAmount < 0)
                    {
                        MessageBox.Show("Spare Parts amount must be a valid number greater than or equal to zero.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Validate Down Payment Percentage
                decimal downPaymentPercent = 0;
                if (HasDownPaymentCheckBox.IsChecked == true)
                {
                    if (!decimal.TryParse(DownPaymentPercentageTextBox.Text, out downPaymentPercent) || downPaymentPercent < 0 || downPaymentPercent > 100)
                    {
                        MessageBox.Show("Down Payment percentage must be a valid number between 0 and 100.", "Validation Error",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Get status mapping
                var statusOptions = new[] { "Active", "On Hold", "Completed", "Cancelled" };
                var selectedStatus = StatusComboBox.SelectedIndex >= 0 && StatusComboBox.SelectedIndex < statusOptions.Length
                    ? statusOptions[StatusComboBox.SelectedIndex]
                    : "Active";

                // Handle PO file attachment
                string? poFilePath = null;
                string? poFileName = null;
                if (!string.IsNullOrEmpty(_selectedPOFilePath))
                {
                    poFileName = System.IO.Path.GetFileName(_selectedPOFilePath);
                    string projectName = NameTextBox.Text.Trim();
                    poFilePath = await App.FileService.SavePOFileAsync(_selectedPOFilePath, projectName, poFileName);
                }

                if (_isEdit && _project != null)
                {
                    _project.Name = NameTextBox.Text.Trim();
                    _project.Description = DescriptionTextBox.Text.Trim();
                    _project.Status = selectedStatus;
                    _project.PODate = PODatePicker.SelectedDate;
                    _project.POAmount = poAmount;
                    _project.SplitTasksIntoHardwareAndSoftware = SplitTasksCheckBox.IsChecked == true;

                    if (_project.SplitTasksIntoHardwareAndSoftware)
                    {
                        _project.ManualSoftwareTasksAmount = decimal.TryParse(ManualSoftwareTasksAmountTextBox.Text, out var sw) ? sw : 0;
                        _project.ManualHardwareTasksAmount = decimal.TryParse(ManualHardwareTasksAmountTextBox.Text, out var hw) ? hw : 0;
                        _project.ManualTasksAmount = 0; // ignore combined when split
                    }
                    else
                    {
                        _project.ManualTasksAmount = manualTasksAmount;
                        _project.ManualSoftwareTasksAmount = 0;
                        _project.ManualHardwareTasksAmount = 0;
                    }

                    _project.ManualServicesAmount = manualServicesAmount;

                    // Set Spare Parts
                    _project.HasSpareParts = sparePartsAmount > 0;
                    _project.ManualSparePartsAmount = sparePartsAmount;

                    System.Diagnostics.Debug.WriteLine($"Saving project - HasSpareParts: {_project.HasSpareParts}, Amount: {_project.ManualSparePartsAmount}");

                    // Set Extra
                    decimal extraAmount = decimal.TryParse(ExtraAmountTextBox.Text, out var ea) ? ea : 0;
                    _project.HasExtra = extraAmount > 0;
                    _project.ExtraCategoryName = !string.IsNullOrWhiteSpace(ExtraNameTextBox.Text) ? ExtraNameTextBox.Text.Trim() : null;
                    _project.ManualExtraAmount = extraAmount;

                    System.Diagnostics.Debug.WriteLine($"Saving project - HasExtra: {_project.HasExtra}, Name: {_project.ExtraCategoryName}, Amount: {_project.ManualExtraAmount}");

                    // Set Down Payment
                    _project.HasDownPayment = HasDownPaymentCheckBox.IsChecked == true;
                    _project.DownPaymentPercentage = _project.HasDownPayment ? downPaymentPercent : 0;

                    // Set site configuration
                    if (int.TryParse(NumberOfSitesTextBox.Text, out int numberOfSites))
                    {
                        _project.NumberOfSites = numberOfSites;
                    }

                    // Set PO file data if new file was selected
                    if (poFilePath != null)
                    {
                        _project.POFilePath = poFilePath;
                        _project.POFileName = poFileName;
                    }

                    // Update CreatedDate to match PODate if PODate is set
                    if (_project.PODate.HasValue)
                    {
                        _project.CreatedDate = _project.PODate.Value;
                    }

                    await App.DataService.UpdateProjectAsync(_project);

                    // Save payment terms for the updated project
                    await SaveProjectPaymentTerms(_project.Id);

                    // Save sites
                    try
                    {
                        await SaveProjectSites(_project.Id);
                    }
                    catch (Exception ex)
                    {
                        // Don't fail the whole operation if sites fail to save
                        System.Diagnostics.Debug.WriteLine($"Warning: Could not save sites: {ex.Message}");
                    }
                }
                else
                {
                    // Validate payment terms totals before saving
                    var equipmentTotal = _paymentTerms.Where(t => t.Category == "Equipment").Sum(t => t.Percentage);
                    var servicesTotal = _paymentTerms.Where(t => t.Category == "Services").Sum(t => t.Percentage);
                    if (equipmentTotal > 100 || servicesTotal > 100)
                    {
                        MessageBox.Show("Total percentage for each category (Equipment/Services) must not exceed 100%.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    var newProject = new Project
                    {
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text.Trim(),
                        Status = selectedStatus,
                        CreatedDate = PODatePicker.SelectedDate ?? DateTime.Now, // Use PO Date as Created Date
                        PODate = PODatePicker.SelectedDate,
                        POAmount = poAmount,
                        SplitTasksIntoHardwareAndSoftware = SplitTasksCheckBox.IsChecked == true,
                        ManualTasksAmount = SplitTasksCheckBox.IsChecked == true ? 0 : manualTasksAmount,
                        ManualSoftwareTasksAmount = SplitTasksCheckBox.IsChecked == true && decimal.TryParse(ManualSoftwareTasksAmountTextBox.Text, out var ns) ? ns : 0,
                        ManualHardwareTasksAmount = SplitTasksCheckBox.IsChecked == true && decimal.TryParse(ManualHardwareTasksAmountTextBox.Text, out var nh) ? nh : 0,
                        ManualServicesAmount = manualServicesAmount,
                        HasSpareParts = sparePartsAmount > 0,
                        ManualSparePartsAmount = sparePartsAmount,
                        HasExtra = decimal.TryParse(ExtraAmountTextBox.Text, out var extraAmt) && extraAmt > 0,
                        ExtraCategoryName = !string.IsNullOrWhiteSpace(ExtraNameTextBox.Text) ? ExtraNameTextBox.Text.Trim() : null,
                        ManualExtraAmount = decimal.TryParse(ExtraAmountTextBox.Text, out var extraAmt2) ? extraAmt2 : 0,
                        HasDownPayment = HasDownPaymentCheckBox.IsChecked == true,
                        DownPaymentPercentage = HasDownPaymentCheckBox.IsChecked == true ? downPaymentPercent : 0,
                        NumberOfSites = int.TryParse(NumberOfSitesTextBox.Text, out int sites) ? sites : 1,
                        POFilePath = poFilePath,
                        POFileName = poFileName
                    };

                    System.Diagnostics.Debug.WriteLine($"Creating new project - HasSpareParts: {newProject.HasSpareParts}, Amount: {newProject.ManualSparePartsAmount}");

                    var savedProject = await App.DataService.AddProjectAsync(newProject);

                    // Save payment terms for the project
                    await SaveProjectPaymentTerms(savedProject.Id);

                    // Save sites
                    try
                    {
                        await SaveProjectSites(savedProject.Id);
                    }
                    catch (Exception ex)
                    {
                        // Don't fail the whole operation if sites fail to save
                        System.Diagnostics.Debug.WriteLine($"Warning: Could not save sites: {ex.Message}");
                    }
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                var msg = $"Error saving project: {ex.Message}";
                if (ex.InnerException != null)
                    msg += $"\n\nInner: {ex.InnerException.Message}";
                MessageBox.Show(msg, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdatePaymentCategoryComboBox()
        {
            PaymentCategoryComboBox.Items.Clear();

            bool isSplit = SplitTasksCheckBox?.IsChecked == true;
            bool hasSpareParts = !string.IsNullOrWhiteSpace(SparePartsAmountTextBox?.Text) &&
                                 decimal.TryParse(SparePartsAmountTextBox.Text, out var spAmount) && spAmount > 0;
            bool hasExtra = !string.IsNullOrWhiteSpace(ExtraAmountTextBox?.Text) &&
                           decimal.TryParse(ExtraAmountTextBox.Text, out var exAmount) && exAmount > 0;

            if (isSplit)
            {
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Software Equipment" });
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Hardware Equipment" });
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Services" });
            }
            else
            {
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Equipment" });
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Services" });
            }

            // Only add Spare Parts if amount is entered
            if (hasSpareParts)
            {
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = "Spare Parts" });
            }

            // Only add Extra if amount is entered
            if (hasExtra)
            {
                string extraName = !string.IsNullOrWhiteSpace(ExtraNameTextBox?.Text)
                    ? ExtraNameTextBox.Text.Trim()
                    : "Extra";
                PaymentCategoryComboBox.Items.Add(new ComboBoxItem { Content = extraName });
            }

            if (PaymentCategoryComboBox.Items.Count > 0)
            {
                PaymentCategoryComboBox.SelectedIndex = 0;
            }

            UpdatePaymentTermsTotals();
        }

        private void UpdatePaymentTermsTotals()
        {
            var equipmentTotal = _paymentTerms.Where(t => t.Category == "Equipment").Sum(t => t.Percentage);
            var softwareTotal = _paymentTerms.Where(t => t.Category == "Software Equipment").Sum(t => t.Percentage);
            var hardwareTotal = _paymentTerms.Where(t => t.Category == "Hardware Equipment").Sum(t => t.Percentage);
            var servicesTotal = _paymentTerms.Where(t => t.Category == "Services").Sum(t => t.Percentage);
            var sparePartsTotal = _paymentTerms.Where(t => t.Category == "Spare Parts").Sum(t => t.Percentage);
            var extraTotal = _paymentTerms.Where(t => t.Category == "Extra").Sum(t => t.Percentage);

            if (SplitTasksCheckBox.IsChecked == true)
            {
                PaymentTermsTotalsTextBlock.Text = $"Totals: Software Equipment {softwareTotal:F2}% | Hardware Equipment {hardwareTotal:F2}% | Services {servicesTotal:F2}% | Spare Parts {sparePartsTotal:F2}% | Extra {extraTotal:F2}% (Each category must be ≤ 100%)";
            }
            else
            {
                PaymentTermsTotalsTextBlock.Text = $"Totals: Equipment {equipmentTotal:F2}% | Services {servicesTotal:F2}% | Spare Parts {sparePartsTotal:F2}% | Extra {extraTotal:F2}% (Each category must be ≤ 100%)";
            }
        }



        private void RemovePaymentTermButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if ((sender as Button)?.DataContext is ProjectPaymentTerm term)
                {
                    _paymentTerms.Remove(term);

                    // Set Project reference for remaining terms
                    var currentProject = GetCurrentProjectForCalculation();
                    foreach (var paymentTerm in _paymentTerms)
                    {
                        paymentTerm.Project = currentProject;
                    }

                    PaymentTermsListView.ItemsSource = null;
                    PaymentTermsListView.ItemsSource = _paymentTerms.OrderBy(t => t.Category).ThenBy(t => t.TermOrder).ToList();
                    UpdatePaymentTermsTotals();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing payment term: {ex.Message}");
                MessageBox.Show($"Error removing payment term: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private Project GetCurrentProjectForCalculation()
        {
            // Create a temporary project with current values for calculation
            var tempProject = new Project
            {
                Id = _project?.Id ?? 0,
                SplitTasksIntoHardwareAndSoftware = SplitTasksCheckBox.IsChecked ?? false,
                HasDownPayment = HasDownPaymentCheckBox.IsChecked ?? false,
                HasSpareParts = !string.IsNullOrWhiteSpace(SparePartsAmountTextBox.Text) && decimal.TryParse(SparePartsAmountTextBox.Text, out var sparePartsValue) && sparePartsValue > 0
            };

            // Set amounts from UI
            if (SplitTasksCheckBox.IsChecked == true)
            {
                // Split mode - use individual software/hardware amounts
                if (decimal.TryParse(ManualSoftwareTasksAmountTextBox.Text, out var software))
                    tempProject.ManualSoftwareTasksAmount = software;

                if (decimal.TryParse(ManualHardwareTasksAmountTextBox.Text, out var hardware))
                    tempProject.ManualHardwareTasksAmount = hardware;

                if (decimal.TryParse(ManualServicesAmountSplitTextBox.Text, out var servicesSplit))
                    tempProject.ManualServicesAmount = servicesSplit;
            }
            else
            {
                // Normal mode - use total equipment amount
                if (decimal.TryParse(ManualTasksAmountTextBox.Text, out var equipment))
                    tempProject.ManualTasksAmount = equipment;

                if (decimal.TryParse(ManualServicesAmountTextBox.Text, out var services))
                    tempProject.ManualServicesAmount = services;
            }

            if (decimal.TryParse(SparePartsAmountTextBox.Text, out var spareParts))
                tempProject.ManualSparePartsAmount = spareParts;

            if (decimal.TryParse(ExtraAmountTextBox.Text, out var extra))
            {
                tempProject.HasExtra = extra > 0;
                tempProject.ManualExtraAmount = extra;
            }

            if (decimal.TryParse(DownPaymentPercentageTextBox.Text, out var downPaymentPercentage))
                tempProject.DownPaymentPercentage = downPaymentPercentage;

            return tempProject;
        }

        private void PaymentTermsListView_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                if (sender is ListView listView && listView.View is GridView gridView)
                {
                    var availableWidth = listView.ActualWidth - 40; // Account for padding and scrollbar

                    if (availableWidth > 0 && gridView.Columns.Count >= 5)
                    {
                        // Calculate proportional widths
                        var categoryWidth = availableWidth * 0.20; // 20%
                        var descriptionWidth = availableWidth * 0.35; // 35%
                        var percentageWidth = availableWidth * 0.15; // 15%
                        var amountWidth = availableWidth * 0.15; // 15%
                        var actionsWidth = availableWidth * 0.15; // 15%

                        // Apply the widths
                        gridView.Columns[0].Width = categoryWidth;
                        gridView.Columns[1].Width = descriptionWidth;
                        gridView.Columns[2].Width = percentageWidth;
                        gridView.Columns[3].Width = amountWidth;
                        gridView.Columns[4].Width = actionsWidth;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in PaymentTermsListView_SizeChanged: {ex.Message}");
            }
        }

        private void AddPaymentTermButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get selected category
                var selectedCategory = (PaymentCategoryComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                if (string.IsNullOrEmpty(selectedCategory))
                {
                    MessageBox.Show("Please select a category.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var description = PaymentTermDescriptionTextBox.Text.Trim();
                if (string.IsNullOrWhiteSpace(description))
                {
                    MessageBox.Show("Description is required.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(PaymentTermPercentageTextBox.Text, out var percentage) || percentage <= 0)
                {
                    MessageBox.Show("Percentage must be a valid number greater than 0.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Validate category totals
                var currentTotal = _paymentTerms.Where(t => t.Category == selectedCategory).Sum(t => t.Percentage);
                if (currentTotal + percentage > 100)
                {
                    MessageBox.Show($"Total percentage for {selectedCategory} cannot exceed 100%. Current total: {currentTotal:F2}%",
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var term = new ProjectPaymentTerm
                {
                    Category = selectedCategory,
                    Description = description,
                    Percentage = percentage,
                    TriggerCondition = PaymentTermDetailsTextBox.Text.Trim(), // النص الإضافي
                    TermOrder = _paymentTerms.Where(t => t.Category == selectedCategory).Count() + 1,
                    IsActive = true
                };

                _paymentTerms.Add(term);

                // Set Project reference for all terms to enable amount calculation
                foreach (var paymentTerm in _paymentTerms)
                {
                    paymentTerm.Project = GetCurrentProjectForCalculation();
                }

                PaymentTermsListView.ItemsSource = null;
                PaymentTermsListView.ItemsSource = _paymentTerms.OrderBy(t => t.Category).ThenBy(t => t.TermOrder).ToList();

                // Clear input fields
                PaymentTermDescriptionTextBox.Text = string.Empty;
                PaymentTermPercentageTextBox.Text = string.Empty;
                PaymentTermDetailsTextBox.Text = string.Empty;

                UpdatePaymentTermsTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding payment term: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        private void SelectPOFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|Excel Files (*.xls;*.xlsx)|*.xls;*.xlsx|All Files (*.*)|*.*";
            _selectedPOFilePath = App.FileService.SelectFile(filter);

            if (!string.IsNullOrEmpty(_selectedPOFilePath))
            {
                POFileTextBox.Text = System.IO.Path.GetFileName(_selectedPOFilePath);
            }
        }

        private void NumberOfSitesTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Auto-update sites when number changes
            try
            {
                if (int.TryParse(NumberOfSitesTextBox.Text, out int numberOfSites) && numberOfSites > 0 && numberOfSites <= 10)
                {
                    // Sites will be created automatically during save with default names
                    // No need for immediate UI update to keep it simple
                }
                // No validation needed
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in NumberOfSitesTextBox_TextChanged: {ex.Message}");
            }
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculatePOTotal();

            // Update payment category combo box when amounts change (for Spare Parts and Extra)
            UpdatePaymentCategoryComboBox();

            // Update payment terms calculations when amounts change
            if (_paymentTerms.Any())
            {
                foreach (var paymentTerm in _paymentTerms)
                {
                    paymentTerm.Project = GetCurrentProjectForCalculation();
                }

                // Refresh the ListView to update the Amount column
                PaymentTermsListView.ItemsSource = null;
                PaymentTermsListView.ItemsSource = _paymentTerms.OrderBy(t => t.Category).ThenBy(t => t.TermOrder).ToList();
            }
        }



        private void HasDownPaymentCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            DownPaymentPercentageTextBox.IsEnabled = true;
        }

        private void HasDownPaymentCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            DownPaymentPercentageTextBox.IsEnabled = false;
            DownPaymentPercentageTextBox.Text = "";
        }

        private void CalculatePOTotal()
        {
            try
            {
                decimal totalAmount = 0;

                // Check if tasks are split
                if (SplitTasksCheckBox?.IsChecked == true)
                {
                    // Split tasks: Software + Hardware + Services
                    if (decimal.TryParse(ManualSoftwareTasksAmountTextBox?.Text, out var softwareAmount))
                        totalAmount += softwareAmount;

                    if (decimal.TryParse(ManualHardwareTasksAmountTextBox?.Text, out var hardwareAmount))
                        totalAmount += hardwareAmount;
                }
                else
                {
                    // Combined tasks: Tasks + Services
                    if (decimal.TryParse(ManualTasksAmountTextBox?.Text, out var tasksAmount))
                        totalAmount += tasksAmount;
                }

                // Add services amount based on current mode
                decimal servicesAmount = 0;
                if (SplitTasksCheckBox?.IsChecked == true)
                {
                    // In split mode, use the split services textbox
                    decimal.TryParse(ManualServicesAmountSplitTextBox?.Text, out servicesAmount);
                }
                else
                {
                    // In combined mode, use the combined services textbox
                    decimal.TryParse(ManualServicesAmountTextBox?.Text, out servicesAmount);
                }
                totalAmount += servicesAmount;

                // Add spare parts amount if entered
                if (!string.IsNullOrWhiteSpace(SparePartsAmountTextBox?.Text))
                {
                    if (decimal.TryParse(SparePartsAmountTextBox.Text, out var sparePartsAmount))
                        totalAmount += sparePartsAmount;
                }

                // Add extra amount if entered
                if (!string.IsNullOrWhiteSpace(ExtraAmountTextBox?.Text))
                {
                    if (decimal.TryParse(ExtraAmountTextBox.Text, out var extraAmount))
                        totalAmount += extraAmount;
                }

                // Update PO Amount field
                if (POAmountTextBox != null)
                {
                    POAmountTextBox.Text = totalAmount > 0 ? totalAmount.ToString("F2") : "";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating PO total: {ex.Message}");
            }
        }

        // Removed LoadProjectSites - sites are now created automatically with default names

        // Removed UpdateSitesList and SiteNameTextBox_TextChanged - sites are now created automatically



        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async System.Threading.Tasks.Task SaveProjectSites(int projectId)
        {
            try
            {
                // Get number of sites from the textbox
                if (!int.TryParse(NumberOfSitesTextBox.Text, out int numberOfSites) || numberOfSites <= 0)
                    return;

                // Check if sites already exist for this project
                var existingSites = await App.DataService.GetProjectSitesAsync(projectId);
                if (existingSites.Count >= numberOfSites)
                {
                    // Sites already exist, no need to create more
                    return;
                }

                // Create only the missing sites
                for (int i = existingSites.Count + 1; i <= numberOfSites; i++)
                {
                    var site = new ProjectSite
                    {
                        ProjectId = projectId,
                        SiteName = $"NN{i}",
                        SiteOrder = i,
                        IsActive = true
                    };

                    try
                    {
                        await App.DataService.AddProjectSiteAsync(site);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Warning: Could not save site {i}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SaveProjectSites: {ex.Message}");
                // Don't throw - let the main operation continue
            }
        }

        private async System.Threading.Tasks.Task LoadExistingPaymentTerms()
        {
            try
            {
                if (_project == null) return;
                var servicesTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Services");
                var equipmentTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Equipment");
                var swTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Software Equipment");
                var hwTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Hardware Equipment");
                var sparePartsTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Spare Parts");
                // Also load old categories for backward compatibility
                var oldTasksTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Tasks");
                var oldSwTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Software Tasks");
                var oldHwTerms = await App.DataService.GetProjectPaymentTermsAsync(_project.Id, "Hardware Tasks");

                _paymentTerms = servicesTerms.Concat(equipmentTerms).Concat(swTerms).Concat(hwTerms)
                    .Concat(sparePartsTerms).Concat(oldTasksTerms).Concat(oldSwTerms).Concat(oldHwTerms)
                    .OrderBy(t => t.Category).ThenBy(t => t.TermOrder).ToList();

                // Set Project reference for all terms to enable amount calculation
                foreach (var paymentTerm in _paymentTerms)
                {
                    paymentTerm.Project = GetCurrentProjectForCalculation();
                }

                PaymentTermsListView.ItemsSource = _paymentTerms;
                UpdatePaymentTermsTotals();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading payment terms: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task SaveProjectPaymentTerms(int projectId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"SaveProjectPaymentTerms called for project {projectId} with {_paymentTerms.Count} terms");

                // Remove existing terms to replace with the new list (safe approach)
                // This will handle the case where terms were deleted - it won't fail if there are no terms to delete
                var deleteResult = await App.DataService.DeleteProjectPaymentTermsAsync(projectId);
                System.Diagnostics.Debug.WriteLine($"Delete existing terms result: {deleteResult}");

                // If there are no new terms to add, we're done (user deleted all terms)
                if (!_paymentTerms.Any())
                {
                    System.Diagnostics.Debug.WriteLine("No payment terms to save - all terms were deleted");
                    return;
                }

                int tasksOrder = 1;
                int servicesOrder = 1;
                int swOrder = 1;
                int hwOrder = 1;
                int sparePartsOrder = 1;
                int extraOrder = 1;

                foreach (var term in _paymentTerms)
                {
                    // Create a completely new instance to avoid ID conflicts
                    // IMPORTANT: Do NOT copy the Id - let the database generate a new one
                    var newTerm = new ProjectPaymentTerm
                    {
                        Id = 0, // Explicitly set to 0 to ensure it's treated as a new entity
                        ProjectId = projectId,
                        Category = term.Category,
                        Description = term.Description,
                        Percentage = term.Percentage,
                        TriggerCondition = term.TriggerCondition,
                        IsActive = term.IsActive,
                        IgnoreSiteDivision = term.IgnoreSiteDivision,
                        TermOrder = term.Category switch
                        {
                            "Equipment" => tasksOrder++,
                            "Tasks" => tasksOrder++, // Backward compatibility
                            "Services" => servicesOrder++,
                            "Software Equipment" => swOrder++,
                            "Software Tasks" => swOrder++, // Backward compatibility
                            "Hardware Equipment" => hwOrder++,
                            "Hardware Tasks" => hwOrder++, // Backward compatibility
                            "Spare Parts" => sparePartsOrder++,
                            "Extra" => extraOrder++,
                            _ when !string.IsNullOrWhiteSpace(term.Category) => extraOrder++, // Custom extra category
                            _ => term.TermOrder
                        }
                    };

                    System.Diagnostics.Debug.WriteLine($"Saving payment term: Category={newTerm.Category}, Description={newTerm.Description}, Percentage={newTerm.Percentage}, Id={newTerm.Id}");

                    await App.DataService.AddProjectPaymentTermAsync(newTerm);
                }

                System.Diagnostics.Debug.WriteLine($"Successfully saved {_paymentTerms.Count} payment terms");
            }
            catch (Exception ex)
            {
                var errorMsg = $"Error saving payment terms: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMsg += $"\n\nInner: {ex.InnerException.Message}";
                }
                System.Diagnostics.Debug.WriteLine(errorMsg);
                MessageBox.Show(errorMsg, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }



    }
}
