#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;
using FinancialTracker.Helpers;

namespace FinancialTracker
{
    public partial class CommitmentDialog : Window
    {
        private Commitment? _commitment;
        private bool _isEdit;
        private string _selectedFilePath = string.Empty;
        private int? _preSelectedProjectId;
        private List<Invoice> _availableInvoices = new List<Invoice>();
        private List<Invoice> _linkedInvoices = new List<Invoice>();


        public CommitmentDialog(Commitment? commitment = null, int? projectId = null)
        {
            InitializeComponent();
            _commitment = commitment;
            _isEdit = commitment != null;
            _preSelectedProjectId = projectId;

            // Project is now pre-selected and fixed

            LoadData();
            _ = LoadAvailableInvoices();

            // Add event handler for selection changed
            AvailableInvoicesListBox.SelectionChanged += (s, e) => UpdateSelectedInvoicesCount();

            if (_isEdit && _commitment != null)
            {
                Title = "Edit Commitment";
                PopulateFields();
                _ = LoadLinkedInvoices();
                UpdateRemainingAmountDisplay();

                // Load commitment types for editing mode
                if (_commitment.ProjectId > 0)
                {
                    _ = LoadCommitmentTypesForProject(_commitment.ProjectId);
                }
            }
            else
            {
                Title = "Add New Commitment";

                // Set default creation date to today
                CreatedDatePicker.SelectedDate = DateTime.Today;

                // Load commitment types for pre-selected project if provided
                if (_preSelectedProjectId.HasValue)
                {
                    _ = LoadCommitmentTypesForProject(_preSelectedProjectId.Value);
                }
            }

            // Ensure types are loaded after UI is ready
            Loaded += async (s, e) => {
                if (_preSelectedProjectId.HasValue)
                {
                    await LoadCommitmentTypesForProject(_preSelectedProjectId.Value);
                }
                else if (_isEdit && _commitment != null && _commitment.ProjectId > 0)
                {
                    await LoadCommitmentTypesForProject(_commitment.ProjectId);
                }
            };

            // Update EGP amount when amount or exchange rate changes (after UI is loaded)
            Loaded += (s, e) => {
                UpdateEGPAmount();
                ValidateAllSteps();
            };
        }

        private async void LoadData()
        {
            try
            {
                // Load commitment types for the pre-selected project if available
                if (_preSelectedProjectId.HasValue)
                {
                    await LoadCommitmentTypesForProject(_preSelectedProjectId.Value);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async System.Threading.Tasks.Task LoadCommitmentTypesForProject(int projectId)
        {
            try
            {
                // Check if UI elements are loaded
                if (TypeComboBox == null)
                    return;

                var project = await App.DataService.GetProjectByIdAsync(projectId);
                if (project == null) return;

                TypeComboBox.Items.Clear();

                // Add commitment types based on project configuration
                if (project.SplitTasksIntoHardwareAndSoftware)
                {
                    // Split equipment - options
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Software Equipment", Tag = "Software Equipment" });
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Hardware Equipment", Tag = "Hardware Equipment" });
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Services", Tag = "Services" });
                    if (project.HasSpareParts)
                    {
                        TypeComboBox.Items.Add(new ComboBoxItem { Content = "Spare Parts", Tag = "Spare Parts" });
                    }
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Other", Tag = "Other" });
                }
                else
                {
                    // Combined equipment - options
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Equipment", Tag = "Equipment" });
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Services", Tag = "Services" });
                    if (project.HasSpareParts)
                    {
                        TypeComboBox.Items.Add(new ComboBoxItem { Content = "Spare Parts", Tag = "Spare Parts" });
                    }
                    TypeComboBox.Items.Add(new ComboBoxItem { Content = "Other", Tag = "Other" });
                }

                // Select first item if available
                if (TypeComboBox.Items.Count > 0)
                {
                    TypeComboBox.SelectedIndex = 0;
                }

                System.Diagnostics.Debug.WriteLine($"Loaded {TypeComboBox.Items.Count} commitment types for project {projectId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading commitment types: {ex.Message}");
                MessageBox.Show($"Error loading commitment types: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void TypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                if (selectedItem.Content?.ToString() == "Other")
                {
                    CustomTypeTextBox.Visibility = Visibility.Visible;
                }
                else
                {
                    CustomTypeTextBox.Visibility = Visibility.Collapsed;
                    CustomTypeTextBox.Text = "";
                }
            }
        }

        private string GetSelectedType()
        {
            if (TypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var selectedType = selectedItem.Tag?.ToString() ?? selectedItem.Content?.ToString() ?? "SW & HW";
                if (selectedType == "Other")
                {
                    return !string.IsNullOrWhiteSpace(CustomTypeTextBox.Text) ? CustomTypeTextBox.Text.Trim() : "Other";
                }
                return selectedType;
            }
            return "SW & HW";
        }

        private async void PopulateFields()
        {
            if (_commitment == null) return;

            TitleTextBox.Text = _commitment.Title;
            // Project is fixed for this commitment

            // Load commitment types for this project first
            try
            {
                await LoadCommitmentTypesForProject(_commitment.ProjectId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading commitment types: {ex.Message}");
            }

            // Set type
            bool typeFound = false;
            foreach (ComboBoxItem item in TypeComboBox.Items)
            {
                var itemTag = item.Tag?.ToString();
                var itemContent = item.Content?.ToString();

                if (itemTag == _commitment.Type || itemContent == _commitment.Type)
                {
                    TypeComboBox.SelectedItem = item;
                    typeFound = true;
                    break;
                }
            }

            // If type not found in predefined options, select "Other" and set custom text
            if (!typeFound && !string.IsNullOrEmpty(_commitment.Type))
            {
                foreach (ComboBoxItem item in TypeComboBox.Items)
                {
                    if (item.Content?.ToString() == "Other")
                    {
                        TypeComboBox.SelectedItem = item;
                        CustomTypeTextBox.Text = _commitment.Type;
                        CustomTypeTextBox.Visibility = Visibility.Visible;
                        break;
                    }
                }
            }

            AmountTextBox.Text = _commitment.AmountUSD.ToString("F0");
            ExchangeRateTextBox.Text = _commitment.ExchangeRate.ToString("F4");
            DescriptionTextBox.Text = _commitment.Description;
            CreatedDatePicker.SelectedDate = _commitment.CreatedDate;
            // IsActive is always true for commitments (removed checkbox)

            if (!string.IsNullOrEmpty(_commitment.AttachedFileName))
            {
                AttachedFileTextBox.Text = _commitment.AttachedFileName;
                OpenFileButton.IsEnabled = true;
            }
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.doc;*.docx)|*.doc;*.docx|All Files (*.*)|*.*";
            _selectedFilePath = App.FileService.SelectFile(filter);
            
            if (!string.IsNullOrEmpty(_selectedFilePath))
            {
                AttachedFileTextBox.Text = System.IO.Path.GetFileName(_selectedFilePath);
                OpenFileButton.IsEnabled = true;
            }
        }

        private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    await App.FileService.OpenFileAsync(_selectedFilePath);
                }
                else if (_commitment != null && !string.IsNullOrEmpty(_commitment.AttachedFilePath))
                {
                    await App.FileService.OpenFileAsync(_commitment.AttachedFilePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening file: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
                {
                    MessageBox.Show("Commitment title is required.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_preSelectedProjectId == null && !_isEdit)
                {
                    MessageBox.Show("Project must be specified.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }



                if (TypeComboBox.SelectedIndex == -1)
                {
                    MessageBox.Show("Please select a commitment type.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    MessageBox.Show("Please enter a valid amount.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }



                if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
                {
                    MessageBox.Show("Please enter a valid exchange rate.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CreatedDatePicker.SelectedDate == null)
                {
                    MessageBox.Show("Please select a creation date.", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                string? attachedFilePath = null;
                string? attachedFileName = null;

                // Handle file attachment
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    attachedFileName = System.IO.Path.GetFileName(_selectedFilePath);
                    attachedFilePath = await App.FileService.SaveFileAsync(_selectedFilePath, "commitment", attachedFileName);
                }

                if (_isEdit && _commitment != null)
                {
                    _commitment.Title = TitleTextBox.Text.Trim();
                    // ProjectId remains unchanged during edit
                    _commitment.Type = GetSelectedType();
                    _commitment.AmountUSD = amount;
                    _commitment.ExchangeRate = exchangeRate;
                    _commitment.Description = DescriptionTextBox.Text.Trim();
                    _commitment.CreatedDate = CreatedDatePicker.SelectedDate.Value;
                    _commitment.IsActive = true; // Always active
                    
                    if (attachedFilePath != null)
                    {
                        _commitment.AttachedFilePath = attachedFilePath;
                        _commitment.AttachedFileName = attachedFileName;
                    }
                    
                    await App.DataService.UpdateCommitmentAsync(_commitment);
                }
                else
                {
                    var newCommitment = new Commitment
                    {
                        Title = TitleTextBox.Text.Trim(),
                        ProjectId = _preSelectedProjectId ?? 0,
                        Type = GetSelectedType(),
                        AmountUSD = amount,
                        ExchangeRate = exchangeRate,
                        Description = DescriptionTextBox.Text.Trim(),
                        IsActive = true, // Always active
                        AttachedFilePath = attachedFilePath,
                        AttachedFileName = attachedFileName,
                        CreatedDate = CreatedDatePicker.SelectedDate.Value
                    };
                    
                    await App.DataService.AddCommitmentAsync(newCommitment);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving commitment: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Invoice linking functionality
        private async System.Threading.Tasks.Task LoadAvailableInvoices()
        {
            try
            {
                int? projectId = _isEdit ? _commitment?.ProjectId : _preSelectedProjectId;
                if (projectId.HasValue)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    _availableInvoices = allInvoices.Where(i => i.ProjectId == projectId.Value && i.CommitmentId == null).ToList();
                    AvailableInvoicesListBox.ItemsSource = _availableInvoices;

                    // Update selection count
                    UpdateSelectedInvoicesCount();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSelectedInvoicesCount()
        {
            var selectedCount = AvailableInvoicesListBox.SelectedItems.Count;
            if (selectedCount == 0)
            {
                SelectedCountText.Text = "No invoices selected";
            }
            else if (selectedCount == 1)
            {
                SelectedCountText.Text = "1 invoice selected";
            }
            else
            {
                SelectedCountText.Text = $"{selectedCount} invoices selected";
            }
        }

        private async System.Threading.Tasks.Task LoadLinkedInvoices()
        {
            try
            {
                if (_commitment != null)
                {
                    var allInvoices = await App.DataService.GetInvoicesAsync();
                    _linkedInvoices = allInvoices.Where(i => i.CommitmentId == _commitment.Id).ToList();
                    LinkedInvoicesListBox.ItemsSource = _linkedInvoices;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading linked invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }





        private async void RefreshInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadAvailableInvoices();
        }

        private async void LinkInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_commitment == null)
                {
                    MessageBox.Show("You must save the commitment first before linking invoices.", "Warning",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var selectedInvoices = AvailableInvoicesListBox.SelectedItems.Cast<Invoice>().ToList();

                if (!selectedInvoices.Any())
                {
                    MessageBox.Show("Please select at least one invoice to link.", "Warning",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Confirm linking multiple invoices
                var confirmMessage = selectedInvoices.Count == 1
                    ? $"Do you want to link invoice '{selectedInvoices[0].InvoiceNumber}' to this commitment?"
                    : $"Do you want to link {selectedInvoices.Count} invoices to this commitment?";

                var result = MessageBox.Show(confirmMessage, "Confirm Link",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                // Link all selected invoices
                int successCount = 0;
                int failCount = 0;
                string errorMessages = "";

                foreach (var invoice in selectedInvoices)
                {
                    try
                    {
                        invoice.CommitmentId = _commitment.Id;
                        await App.DataService.UpdateInvoiceAsync(invoice);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        errorMessages += $"\n• {invoice.InvoiceNumber}: {ex.Message}";
                    }
                }

                // Refresh the lists
                await LoadAvailableInvoices();
                await LoadLinkedInvoices();

                // Show result message
                if (failCount == 0)
                {
                    var successMessage = successCount == 1
                        ? "Invoice linked successfully!"
                        : $"{successCount} invoices linked successfully!";

                    MessageBox.Show(successMessage, "Success",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var mixedMessage = $"Linked: {successCount}\nFailed: {failCount}\n\nErrors:{errorMessages}";
                    MessageBox.Show(mixedMessage, "Partial Success",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error linking invoices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void UnlinkInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    var result = MessageBox.Show($"Do you want to unlink the invoice '{invoice.InvoiceNumber}'?",
                        "Confirm", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // Remove the link
                        invoice.CommitmentId = null;
                        await App.DataService.UpdateInvoiceAsync(invoice);

                        // Refresh the lists
                        await LoadAvailableInvoices();
                        await LoadLinkedInvoices();

                        MessageBox.Show("Invoice unlinked successfully!", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error unlinking invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AmountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateEGPAmount();
            ValidateStep2();
        }

        private void ExchangeRateTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateEGPAmount();
            ValidateStep2();
        }

        private void UpdateEGPAmount()
        {
            try
            {
                // Check if UI elements are loaded
                if (AmountTextBox == null || ExchangeRateTextBox == null || AmountEGPText == null)
                    return;

                if (decimal.TryParse(AmountTextBox.Text, out decimal amount) &&
                    decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate))
                {
                    var amountEGP = amount * exchangeRate;
                    AmountEGPText.Text = $"EGP {amountEGP:N0}";
                }
                else
                {
                    AmountEGPText.Text = "EGP 0";
                }
            }
            catch (Exception)
            {
                if (AmountEGPText != null)
                    AmountEGPText.Text = "EGP 0";
            }
        }

        private void UpdateRemainingAmountDisplay()
        {
            try
            {
                // Check if UI elements are loaded
                if (RemainingAmountPanel == null || RemainingAmountUSDText == null || RemainingAmountEGPText == null)
                    return;

                if (_commitment != null && _isEdit)
                {
                    RemainingAmountPanel.Visibility = Visibility.Visible;
                    RemainingAmountUSDText.Text = $"${_commitment.RemainingCommitmentAmountCorrected:N2}";

                    var remainingEGP = _commitment.AmountEGP - _commitment.TotalInvoicedAmountEGP;
                    RemainingAmountEGPText.Text = $"EGP {remainingEGP:N0}";
                }
                else
                {
                    RemainingAmountPanel.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating remaining amount display: {ex.Message}");
                if (RemainingAmountPanel != null)
                    RemainingAmountPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void TitleTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            ValidateStep1();
        }

        private void ValidateStep1()
        {
            bool step1Complete = !string.IsNullOrWhiteSpace(TitleTextBox?.Text) &&
                               TypeComboBox?.SelectedItem != null;

            UpdateStepCompletionUI(1, step1Complete);
            EnableNextStep(2, step1Complete);
        }

        private void ValidateStep2()
        {
            bool step2Complete = !string.IsNullOrWhiteSpace(AmountTextBox?.Text) &&
                               decimal.TryParse(AmountTextBox?.Text, out decimal amount) && amount > 0 &&
                               !string.IsNullOrWhiteSpace(ExchangeRateTextBox?.Text) &&
                               decimal.TryParse(ExchangeRateTextBox?.Text, out decimal rate) && rate > 0;

            UpdateStepCompletionUI(2, step2Complete);
            EnableNextStep(3, step2Complete);
        }

        private void ValidateStep3()
        {
            bool step3Complete = CreatedDatePicker?.SelectedDate != null;

            UpdateStepCompletionUI(3, step3Complete);
            EnableNextStep(4, step3Complete);
        }

        private void ValidateAllSteps()
        {
            ValidateStep1();
            ValidateStep2();
            ValidateStep3();

            // Step 4 is always optional
            UpdateStepCompletionUI(4, true);
            UpdateFinalSummary();
        }

        private void UpdateStepCompletionUI(int stepNumber, bool isComplete)
        {
            try
            {
                var icon = FindName($"Step{stepNumber}CompleteIcon") as FrameworkElement;
                var text = FindName($"Step{stepNumber}CompleteText") as FrameworkElement;

                if (icon != null && text != null)
                {
                    icon.Visibility = isComplete ? Visibility.Visible : Visibility.Collapsed;
                    text.Visibility = isComplete ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating step {stepNumber} UI: {ex.Message}");
            }
        }

        private void EnableNextStep(int stepNumber, bool enable)
        {
            try
            {
                var stepCard = FindName($"Step{stepNumber}Card") as FrameworkElement;
                if (stepCard != null)
                {
                    stepCard.Opacity = enable ? 1.0 : 0.5;
                    stepCard.IsEnabled = enable;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error enabling step {stepNumber}: {ex.Message}");
            }
        }

        private void UpdateFinalSummary()
        {
            try
            {
                var summaryCard = FindName("FinalSummaryCard") as FrameworkElement;
                var summaryDisplay = FindName("FinalSummaryDisplay") as TextBlock;

                if (summaryCard != null && summaryDisplay != null)
                {
                    bool allStepsComplete = !string.IsNullOrWhiteSpace(TitleTextBox?.Text) &&
                                          TypeComboBox?.SelectedItem != null &&
                                          !string.IsNullOrWhiteSpace(AmountTextBox?.Text) &&
                                          CreatedDatePicker?.SelectedDate != null;

                    if (allStepsComplete)
                    {
                        summaryCard.Visibility = Visibility.Visible;

                        var title = TitleTextBox?.Text ?? "Untitled";
                        var type = (TypeComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "Unknown Type";
                        var amount = AmountTextBox?.Text ?? "0";

                        summaryDisplay.Text = $"📋 {title} | 🏷️ {type} | 💰 ${amount} USD | 📅 {CreatedDatePicker?.SelectedDate?.ToString("dd/MM/yyyy")}";
                    }
                    else
                    {
                        summaryCard.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating final summary: {ex.Message}");
            }
        }
    }
}
