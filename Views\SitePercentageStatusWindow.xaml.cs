#nullable enable
using System;
using System.Linq;
using System.Windows;
using FinancialTracker.Services;

namespace FinancialTracker.Views
{
    /// <summary>
    /// نافذة عرض حالة النسب للمواقع
    /// </summary>
    public partial class SitePercentageStatusWindow : Window
    {
        private readonly int _projectId;
        private readonly string _invoiceType;
        private readonly string _paymentTermDescription;

        public SitePercentageStatusWindow(int projectId, string invoiceType, string paymentTermDescription)
        {
            InitializeComponent();
            _projectId = projectId;
            _invoiceType = invoiceType;
            _paymentTermDescription = paymentTermDescription;
            
            LoadSitePercentageStatus();
        }

        /// <summary>
        /// تحميل حالة النسب للمواقع
        /// </summary>
        private async void LoadSitePercentageStatus()
        {
            try
            {
                // Update window title
                WindowTitle.Text = $"📊 حالة النسب - {_invoiceType}";
                WindowSubtitle.Text = $"شرط الدفع: {_paymentTermDescription}";

                // Load site percentage summary
                var summary = await SitePercentageCalculationService.GetSitePercentageSummary(
                    _projectId, _invoiceType, _paymentTermDescription);

                SitesStatusDataGrid.ItemsSource = summary;

                // Update statistics
                UpdateStatistics(summary);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading percentage status: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Update statistics
        /// </summary>
        private void UpdateStatistics(System.Collections.Generic.List<SitePercentageSummary> summary)
        {
            try
            {
                // Total sites
                TotalSitesText.Text = summary.Count.ToString();

                // Available sites (not complete)
                var availableSites = summary.Count(s => !s.IsComplete);
                AvailableSitesText.Text = availableSites.ToString();

                // Complete sites
                var completeSites = summary.Count(s => s.IsComplete);
                CompleteSitesText.Text = completeSites.ToString();

                // Average completion
                var averageCompletion = summary.Any() ? summary.Average(s => s.CompletedPercentage) : 0;
                AverageCompletionText.Text = $"{averageCompletion:F1}%";
            }
            catch (Exception)
            {
                // Silently handle statistics update errors
            }
        }
    }
}
