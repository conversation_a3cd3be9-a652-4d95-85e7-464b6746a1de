#nullable enable
using System;

namespace FinancialTracker.Helpers
{
    /// <summary>
    /// Helper class for managing category names throughout the application
    /// </summary>
    public static class CategoryHelper
    {
        /// <summary>
        /// Gets the customizable name for the Extra category
        /// </summary>
        public static string GetExtraCategoryName()
        {
            try
            {
                return Config.UI.ExtraCategoryName ?? "Extra";
            }
            catch
            {
                return "Extra";
            }
        }

        /// <summary>
        /// Sets the customizable name for the Extra category
        /// </summary>
        public static async System.Threading.Tasks.Task SetExtraCategoryNameAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Category name cannot be empty");
            }

            Config.UI.ExtraCategoryName = name;
            await Config.SaveAsync();
        }

        /// <summary>
        /// Checks if a given category name matches the Extra category
        /// </summary>
        public static bool IsExtraCategory(string? categoryName)
        {
            if (string.IsNullOrWhiteSpace(categoryName))
                return false;

            var extraName = GetExtraCategoryName();
            return categoryName.Equals(extraName, StringComparison.OrdinalIgnoreCase) ||
                   categoryName.Equals("Extra", StringComparison.OrdinalIgnoreCase);
        }
    }
}

