using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Status { get; set; } = "Active";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // PO (Purchase Order) Information
        public DateTime? PODate { get; set; }

        private decimal _poAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal POAmount
        {
            get => _poAmount;
            set => _poAmount = value;
        }

        // PO File attachment properties
        [MaxLength(500)]
        public string? POFilePath { get; set; }

        [MaxLength(100)]
        public string? POFileName { get; set; }

        // Site Configuration - New flexible approach
        public int NumberOfSites { get; set; } = 1; // عدد المواقع

        // Tasks breakdown configuration (keeping old names for compatibility)
        public bool SplitTasksIntoHardwareAndSoftware { get; set; } = false;

        // Manual Financial Input (combined)
        private decimal _manualTasksAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualTasksAmount
        {
            get => _manualTasksAmount;
            set => _manualTasksAmount = value;
        }

        // Manual Financial Input (split)
        private decimal _manualSoftwareTasksAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualSoftwareTasksAmount
        {
            get => _manualSoftwareTasksAmount;
            set => _manualSoftwareTasksAmount = value;
        }

        private decimal _manualHardwareTasksAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualHardwareTasksAmount
        {
            get => _manualHardwareTasksAmount;
            set => _manualHardwareTasksAmount = value;
        }

        private decimal _manualServicesAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualServicesAmount
        {
            get => _manualServicesAmount;
            set => _manualServicesAmount = value;
        }

        // Spare Parts (optional)
        public bool HasSpareParts { get; set; } = false;

        private decimal _manualSparePartsAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualSparePartsAmount
        {
            get => _manualSparePartsAmount;
            set => _manualSparePartsAmount = value;
        }

        // Extra (optional) - منفصل عن المهمات والخدمات
        public bool HasExtra { get; set; } = false;

        [MaxLength(100)]
        public string? ExtraCategoryName { get; set; } // Custom name for extra category (e.g., "Training", "Support")

        private decimal _manualExtraAmount = 0;
        [Column(TypeName = "decimal(18,2)")]
        public decimal ManualExtraAmount
        {
            get => _manualExtraAmount;
            set => _manualExtraAmount = value;
        }

        // Down Payment for Tasks (keeping old name for compatibility)
        public bool HasDownPayment { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DownPaymentPercentage { get; set; } = 0; // Percentage (0-100)

        [NotMapped]
        public decimal DownPaymentAmount => HasDownPayment ? (BaseTasksAmount * DownPaymentPercentage / 100) : 0;

        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Commitment> Commitments { get; set; } = new List<Commitment>();
        public virtual ICollection<ProjectSite> Sites { get; set; } = new List<ProjectSite>();
        public virtual ICollection<ProjectInvoiceType> InvoiceTypes { get; set; } = new List<ProjectInvoiceType>();
        public virtual ICollection<ProjectPaymentTerm> PaymentTerms { get; set; } = new List<ProjectPaymentTerm>();
        public virtual ICollection<ProjectFile> ProjectFiles { get; set; } = new List<ProjectFile>();

        // Computed properties
        [NotMapped]
        public decimal TotalAmount => GetTotalAmount();

        [NotMapped]
        public decimal PaidAmount => GetPaidAmount();

        [NotMapped]
        public decimal RemainingAmount => TotalAmount - PaidAmount;

        // Detailed financial breakdown (keeping old names for compatibility)
        [NotMapped]
        public decimal SoftwareTasksAmount => ManualSoftwareTasksAmount > 0 ? ManualSoftwareTasksAmount : GetSoftwareTasksAmount();

        [NotMapped]
        public decimal HardwareTasksAmount => ManualHardwareTasksAmount > 0 ? ManualHardwareTasksAmount : GetHardwareTasksAmount();

        // Base tasks amount without spare parts and down payment (to avoid circular reference)
        [NotMapped]
        public decimal BaseTasksAmount => SplitTasksIntoHardwareAndSoftware ? (SoftwareTasksAmount + HardwareTasksAmount)
                                                                            : (ManualTasksAmount > 0 ? ManualTasksAmount : GetTasksAmount());

        [NotMapped]
        public decimal TasksAmount
        {
            get
            {
                // Use base amount and add spare parts (down payment is separate)
                decimal sparePartsAmount = HasSpareParts ? ManualSparePartsAmount : 0;
                return BaseTasksAmount + sparePartsAmount;
            }
        }

        [NotMapped]
        public decimal ServicesAmount => ManualServicesAmount > 0 ? ManualServicesAmount : GetServicesAmount();

        [NotMapped]
        public decimal SparePartsAmount => HasSpareParts ? ManualSparePartsAmount : 0;

        [NotMapped]
        public decimal ExtraAmount => HasExtra ? ManualExtraAmount : 0;

        // Amount available for payment terms distribution for equipment/tasks ONLY (excludes down payment and spare parts)
        [NotMapped]
        public decimal DistributableTasksAmount => BaseTasksAmount;

        [NotMapped]
        public decimal SpentAmount => PaidAmount;

        [NotMapped]
        public decimal RemainingFromPO => POAmount - PaidAmount;

        [NotMapped]
        public decimal TasksSpentAmount
        {
            get
            {
                // Equipment/Tasks only (exclude spare parts)
                return GetTasksSpentAmount();
            }
        }

        [NotMapped]
        public decimal SparePartsSpentAmount => GetSparePartsSpentAmount();

        [NotMapped]
        public decimal ServicesSpentAmount => GetServicesSpentAmount();

        private decimal GetTotalAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.AmountUSD;
            }
            return total;
        }

        private decimal GetPaidAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                total += invoice.PaidAmount;
            }
            return total;
        }

        private decimal GetTasksAmount()
        {
            // Commitments are just for tracking - return 0 to force use of manual amounts
            return 0;
        }

        private decimal GetSoftwareTasksAmount()
        {
            // Commitments are just for tracking - return 0 to force use of manual amounts
            return 0;
        }

        private decimal GetHardwareTasksAmount()
        {
            // Commitments are just for tracking - return 0 to force use of manual amounts
            return 0;
        }

        private decimal GetServicesAmount()
        {
            // Commitments are just for tracking - return 0 to force use of manual amounts
            return 0;
        }

        private decimal GetTasksSpentAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                var type = invoice.Type?.ToLower() ?? "";
                if (type.Contains("equipment") || type.Contains("hardware") || type.Contains("software") ||
                    type.Contains("hw") || type.Contains("sw"))
                {
                    total += invoice.PaidAmount;
                }
            }
            return total;
        }

        private decimal GetSparePartsSpentAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                var type = invoice.Type?.ToLower() ?? "";
                var description = invoice.Description?.ToLower() ?? "";
                if (type.Contains("spare") || type.Contains("part") ||
                    description.Contains("spare") || description.Contains("part"))
                {
                    total += invoice.PaidAmount;
                }
            }
            return total;
        }

        private decimal GetServicesSpentAmount()
        {
            decimal total = 0;
            foreach (var invoice in Invoices)
            {
                var type = invoice.Type?.ToLower() ?? "";
                if (type.Contains("services") || type.Contains("service"))
                {
                    total += invoice.PaidAmount;
                }
            }
            return total;
        }
    }
}
