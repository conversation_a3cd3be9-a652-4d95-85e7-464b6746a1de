using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace FinancialTracker.Views
{
    public partial class ExportColumnsDialog : Window
    {
        public Dictionary<string, bool> SelectedColumns { get; private set; }

        public ExportColumnsDialog()
        {
            InitializeComponent();
            SelectedColumns = new Dictionary<string, bool>();
        }

        private void SelectAllButton_Click(object sender, RoutedEventArgs e)
        {
            SetAllCheckBoxes(true);
        }

        private void SelectNoneButton_Click(object sender, RoutedEventArgs e)
        {
            SetAllCheckBoxes(false);
        }

        private void SetAllCheckBoxes(bool isChecked)
        {
            foreach (CheckBox checkBox in FindVisualChildren<CheckBox>(ColumnsPanel))
            {
                checkBox.IsChecked = isChecked;
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // Collect selected columns
            SelectedColumns.Clear();
            SelectedColumns["InvoiceNumber"] = InvoiceNumberCheckBox.IsChecked == true;
            SelectedColumns["Type"] = TypeCheckBox.IsChecked == true;
            SelectedColumns["PaymentTerms"] = PaymentTermsCheckBox.IsChecked == true;
            SelectedColumns["Site"] = SiteCheckBox.IsChecked == true;
            SelectedColumns["Amount"] = AmountCheckBox.IsChecked == true;
            SelectedColumns["ExchangeRate"] = ExchangeRateCheckBox.IsChecked == true;
            SelectedColumns["Paid"] = PaidCheckBox.IsChecked == true;
            SelectedColumns["Remaining"] = RemainingCheckBox.IsChecked == true;
            SelectedColumns["DateStatus"] = DateStatusCheckBox.IsChecked == true;
            SelectedColumns["Commitment"] = CommitmentCheckBox.IsChecked == true;
            SelectedColumns["Description"] = DescriptionCheckBox.IsChecked == true;

            // Check if at least one column is selected
            bool hasSelection = false;
            foreach (var column in SelectedColumns.Values)
            {
                if (column)
                {
                    hasSelection = true;
                    break;
                }
            }

            if (!hasSelection)
            {
                MessageBox.Show("Please select at least one column to export.", "No Columns Selected",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Helper method to find visual children
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = System.Windows.Media.VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child!))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
    }
}
