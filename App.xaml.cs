using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using FinancialTracker.Data;
using FinancialTracker.Services;
using FinancialTracker.Helpers;
using FinancialTracker.Views;
using OfficeOpenXml;

namespace FinancialTracker
{
    public partial class App : Application
    {
        public static IDataService DataService { get; private set; } = null!;
        public static IFileService FileService { get; private set; } = null!;

        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Show splash screen
            Views.SplashScreen? splashScreen = null;
            Task? splashTask = null;

            try
            {
                // Initialize configuration first to get splash screen settings
                await Config.LoadAsync();

                // Check if splash screen should be shown
                var showSplash = Config.UI.ShowSplashScreen;
                var splashDuration = Config.UI.SplashScreenDuration;

                if (showSplash)
                {
                    splashScreen = new Views.SplashScreen();
                    splashTask = splashScreen.ShowSplashAsync(splashDuration);
                }

                // Configuration already loaded above

                // Initialize database and services
                var context = new FinancialContext();

                // Check database integrity before migration
                await CheckDatabaseIntegrityAsync(context);

                // Apply migrations (creates DB if missing) — do NOT delete user data on failure
                try
                {
                    await context.Database.MigrateAsync();

                    // Safety net: ensure new columns exist even if migration history is out-of-sync
                    await EnsureProjectColumnsAsync(context);
                }
                catch (Exception dbEx)
                {
                    // Close splash screen before showing error
                    if (splashScreen != null)
                    {
                        splashScreen.CloseSplash();
                        await Task.Delay(500); // Wait for splash to close
                    }

                    var msg = "Database migration failed. The app will exit to protect your data.\n\n" + dbEx.Message;

                    // إضافة معلومات إضافية للمساعدة في حل المشكلة
                    if (dbEx.Message.Contains("malformed") || dbEx.Message.Contains("corrupt"))
                    {
                        msg += "\n\nThe database file appears to be corrupted.";
                        msg += "\nSuggested solutions:";
                        msg += "\n1. Run 'fix_database.bat' to backup and recreate the database";
                        msg += "\n2. Or manually delete 'FinancialTracker.db' and restart the application";
                        msg += "\n3. If you have a backup, restore it from the 'backups' folder";
                    }

                    MessageBox.Show(msg, "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }

                DataService = new DataService(context);
                FileService = new FileService();

                // Wait for splash screen to complete if it was shown
                if (splashTask != null)
                {
                    await splashTask;
                }

                // Show main window
                var mainWindow = new MainWindow();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                // Close splash screen before showing error
                if (splashScreen != null)
                {
                    splashScreen.CloseSplash();
                    await Task.Delay(500); // Wait for splash to close
                }

                var errorMessage = $"Error starting application: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nInner Exception: {ex.InnerException.Message}";
                }

                errorMessage += "\n\nTry deleting the FinancialTracker.db file and restart the application.";

                MessageBox.Show(errorMessage, "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private static async System.Threading.Tasks.Task CheckDatabaseIntegrityAsync(FinancialContext context)
        {
            try
            {
                var conn = context.Database.GetDbConnection();
                await conn.OpenAsync();
                try
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = "PRAGMA integrity_check;";
                        var result = await cmd.ExecuteScalarAsync();
                        if (result?.ToString() != "ok")
                        {
                            throw new InvalidOperationException($"Database integrity check failed: {result}");
                        }
                    }
                }
                finally
                {
                    await conn.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                // If integrity check fails, suggest database recreation
                throw new InvalidOperationException("Database file is corrupted and needs to be recreated.", ex);
            }
        }

        private static async System.Threading.Tasks.Task EnsureProjectColumnsAsync(FinancialContext context)
        {
            try
            {
                var conn = context.Database.GetDbConnection();
                await conn.OpenAsync();
                try
                {
                    var existing = new System.Collections.Generic.HashSet<string>(StringComparer.OrdinalIgnoreCase);
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = "PRAGMA table_info('Projects');";
                        using var reader = await cmd.ExecuteReaderAsync();
                        while (await reader.ReadAsync())
                        {
                            // column name is at index 1
                            existing.Add(reader.GetString(1));
                        }
                    }

                    var adds = new System.Collections.Generic.List<string>();
                    if (!existing.Contains("SplitTasksIntoHardwareAndSoftware"))
                        adds.Add("ALTER TABLE Projects ADD COLUMN SplitTasksIntoHardwareAndSoftware INTEGER NOT NULL DEFAULT 0;");
                    if (!existing.Contains("ManualSoftwareTasksAmount"))
                        adds.Add("ALTER TABLE Projects ADD COLUMN ManualSoftwareTasksAmount decimal(18,2) NOT NULL DEFAULT 0;");
                    if (!existing.Contains("ManualHardwareTasksAmount"))
                        adds.Add("ALTER TABLE Projects ADD COLUMN ManualHardwareTasksAmount decimal(18,2) NOT NULL DEFAULT 0;");

                    foreach (var sql in adds)
                    {
                        await context.Database.ExecuteSqlRawAsync(sql);
                    }
                }
                finally
                {
                    await conn.CloseAsync();
                }
            }
            catch
            {
                // Best-effort safety net; ignore if provider not SQLite or PRAGMA fails
            }
        }
    }
}
