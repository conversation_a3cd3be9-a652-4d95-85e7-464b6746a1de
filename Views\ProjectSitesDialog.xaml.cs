using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker
{
    public partial class ProjectSitesDialog : Window
    {
        private Project _project;
        private List<ProjectSite> _sites;

        public ProjectSitesDialog(Project project)
        {
            InitializeComponent();
            _project = project;
            _sites = new List<ProjectSite>();
            LoadSites();
        }

        private async void LoadSites()
        {
            try
            {
                var existingSites = await App.DataService.GetProjectSitesAsync(_project.Id);
                _sites = existingSites.OrderBy(s => s.SiteOrder).ToList();

                // If no sites exist, create default sites based on project configuration
                if (!_sites.Any())
                {
                    for (int i = 1; i <= _project.NumberOfSites; i++)
                    {
                        _sites.Add(new ProjectSite
                        {
                            ProjectId = _project.Id,
                            SiteName = $"NN{i}",
                            SiteOrder = i,
                            IsActive = true
                        });
                    }
                }

                SitesList.ItemsSource = _sites;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sites: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddSiteButton_Click(object sender, RoutedEventArgs e)
        {
            var newSiteOrder = _sites.Any() ? _sites.Max(s => s.SiteOrder) + 1 : 1;
            var newSite = new ProjectSite
            {
                ProjectId = _project.Id,
                SiteName = $"NN{newSiteOrder}",
                SiteOrder = newSiteOrder,
                IsActive = true
            };

            _sites.Add(newSite);
            SitesList.ItemsSource = null;
            SitesList.ItemsSource = _sites;
        }

        private void RemoveSiteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ProjectSite site)
            {
                var result = MessageBox.Show($"Are you sure you want to remove '{site.SiteName}'?", 
                    "Confirm Removal", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _sites.Remove(site);
                    SitesList.ItemsSource = null;
                    SitesList.ItemsSource = _sites;
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate site names
                foreach (var site in _sites)
                {
                    if (string.IsNullOrWhiteSpace(site.SiteName))
                    {
                        MessageBox.Show("All sites must have a name.", "Validation Error", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Check for duplicate names
                var duplicateNames = _sites.GroupBy(s => s.SiteName.Trim().ToLower())
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key);

                if (duplicateNames.Any())
                {
                    MessageBox.Show("Site names must be unique.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Update existing sites and add new ones
                foreach (var site in _sites.Where(s => s.IsActive))
                {
                    site.SiteName = site.SiteName.Trim();

                    if (site.Id == 0) // New site
                    {
                        site.ProjectId = _project.Id;
                        await App.DataService.AddProjectSiteAsync(site);
                    }
                    else // Existing site
                    {
                        // Update site name in database
                        await App.DataService.UpdateProjectSiteAsync(site);

                        // Update site names in related invoices
                        await UpdateInvoiceSiteNames(site.Id, site.SiteName);
                    }
                }

                // Remove inactive sites
                var inactiveSites = _sites.Where(s => !s.IsActive && s.Id > 0);
                foreach (var site in inactiveSites)
                {
                    await App.DataService.DeleteProjectSiteAsync(site.Id);
                }

                // Update project's number of sites
                _project.NumberOfSites = _sites.Count(s => s.IsActive);
                await App.DataService.UpdateProjectAsync(_project);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving sites: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Update site names in associated invoices
        /// </summary>
        private async System.Threading.Tasks.Task UpdateInvoiceSiteNames(int siteId, string newSiteName)
        {
            try
            {
                var invoices = await App.DataService.GetInvoicesAsync();
                var siteInvoices = invoices.Where(i => i.ProjectSiteId == siteId).ToList();

                foreach (var invoice in siteInvoices)
                {
                    invoice.SiteName = newSiteName;
                    await App.DataService.UpdateInvoiceAsync(invoice);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating invoice site names: {ex.Message}");
                // Don't throw - this is a secondary operation
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
