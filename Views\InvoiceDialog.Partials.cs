#nullable enable
using System;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace FinancialTracker
{
    public partial class InvoiceDialog
    {
        private void CustomPctOfTermCheckBox_Click(object sender, RoutedEventArgs e)
        {
            // When toggled, re-trigger recalculation to respect interpretation
            CalculateAmountsFromMultiplePaymentTerms();
        }

        // Helper to find named child in a visual tree by name
        private T? FindVisualChildByName<T>(DependencyObject parent, string name) where T : FrameworkElement
        {
            if (parent == null) return null;
            int count = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < count; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T fe && fe.Name == name) return fe;
                var result = FindVisualChildByName<T>(child, name);
                if (result != null) return result;
            }
            return null;
        }
    }
}

