#nullable enable
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace FinancialTracker.Helpers
{
    public class AppConfiguration
    {
        public DatabaseSettings Database { get; set; } = new DatabaseSettings();
        public UISettings UI { get; set; } = new UISettings();
        public SecuritySettings Security { get; set; } = new SecuritySettings();
        public FileSettings Files { get; set; } = new FileSettings();
    }

    public class DatabaseSettings
    {
        public string ConnectionString { get; set; } = "Data Source=FinancialTracker.db";
        public int CommandTimeout { get; set; } = 30;
        public bool EnableSensitiveDataLogging { get; set; } = false;
        public bool AutoMigrate { get; set; } = true;
    }

    public class UISettings
    {
        public string Theme { get; set; } = "Light";
        public string PrimaryColor { get; set; } = "Indigo";
        public string SecondaryColor { get; set; } = "Teal";
        public double WindowOpacity { get; set; } = 1.0;
        public bool ShowLoadingIndicators { get; set; } = true;
        public int AutoSaveInterval { get; set; } = 300; // seconds
        public string DateFormat { get; set; } = "yyyy-MM-dd";
        public string CurrencyFormat { get; set; } = "F2";
        public string ExtraCategoryName { get; set; } = "Extra"; // Customizable name for Extra category
        public int SplashScreenDuration { get; set; } = 2500; // milliseconds
        public bool ShowSplashScreen { get; set; } = true;
    }

    public class SecuritySettings
    {
        public bool EncryptSensitiveData { get; set; } = true;
        public bool ValidateFileUploads { get; set; } = true;
        public string[] AllowedFileExtensions { get; set; } = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".jpg", ".png" };
        public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
        public bool LogSecurityEvents { get; set; } = true;
    }



    public class FileSettings
    {
        public string DataDirectory { get; set; } = "data";
        public string BackupDirectory { get; set; } = "backups";
        public bool AutoBackup { get; set; } = true;
        public int BackupRetentionDays { get; set; } = 30;
        public bool CompressBackups { get; set; } = true;
    }

    public interface IConfigurationManager
    {
        AppConfiguration Configuration { get; }
        Task LoadAsync();
        Task SaveAsync();
        Task ResetToDefaultsAsync();
        T GetSetting<T>(string key, T defaultValue);
        Task SetSettingAsync<T>(string key, T value);
    }

    public class ConfigurationManager : IConfigurationManager
    {
        private readonly string _configFilePath;
        private AppConfiguration _configuration;

        public AppConfiguration Configuration => _configuration;

        public ConfigurationManager()
        {
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
            _configuration = new AppConfiguration();
        }

        public async Task LoadAsync()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true
                    };
                    
                    var loadedConfig = JsonSerializer.Deserialize<AppConfiguration>(json, options);
                    if (loadedConfig != null)
                    {
                        _configuration = loadedConfig;
                    }
                }
                else
                {
                    // Create default configuration file
                    await SaveAsync();
                }

                // Configuration loaded successfully
            }
            catch (Exception ex)
            {
                // Log the error for debugging but continue with defaults
                System.Diagnostics.Debug.WriteLine($"Failed to load configuration: {ex.Message}");
                _configuration = new AppConfiguration();
            }
        }

        public async Task SaveAsync()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var json = JsonSerializer.Serialize(_configuration, options);
                await File.WriteAllTextAsync(_configFilePath, json);
                
                // Configuration saved successfully
            }
            catch (Exception)
            {
                // Failed to save configuration
                throw;
            }
        }

        public async Task ResetToDefaultsAsync()
        {
            _configuration = new AppConfiguration();
            await SaveAsync();
            // Configuration reset to defaults
        }

        public T GetSetting<T>(string key, T defaultValue)
        {
            try
            {
                // Simple key-based access using reflection
                var parts = key.Split('.');
                object current = _configuration;

                foreach (var part in parts)
                {
                    var property = current.GetType().GetProperty(part);
                    if (property == null)
                        return defaultValue;
                    
                    var value = property.GetValue(current);
                    if (value == null)
                        return defaultValue;
                    current = value;
                }

                return current is T result ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        public async Task SetSettingAsync<T>(string key, T value)
        {
            try
            {
                // Simple key-based setting using reflection
                var parts = key.Split('.');
                object current = _configuration;

                for (int i = 0; i < parts.Length - 1; i++)
                {
                    var property = current.GetType().GetProperty(parts[i]);
                    if (property == null)
                        throw new ArgumentException($"Invalid configuration key: {key}");
                    
                    current = property.GetValue(current) ?? throw new ArgumentException($"Invalid configuration key: {key}");
                }

                var finalProperty = current.GetType().GetProperty(parts[^1]);
                if (finalProperty == null)
                    throw new ArgumentException($"Invalid configuration key: {key}");

                finalProperty.SetValue(current, value);
                await SaveAsync();
            }
            catch (Exception)
            {
                // Failed to set configuration value
                throw;
            }
        }
    }

    /// <summary>
    /// Static configuration manager for easy access throughout the application
    /// </summary>
    public static class Config
    {
        private static IConfigurationManager? _instance;
        
        public static IConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ConfigurationManager();
                }
                return _instance;
            }
        }

        public static void Initialize(IConfigurationManager configManager)
        {
            _instance = configManager;
        }

        // Convenience properties
        public static AppConfiguration Current => Instance.Configuration;
        public static DatabaseSettings Database => Current.Database;
        public static UISettings UI => Current.UI;
        public static SecuritySettings Security => Current.Security;
        public static FileSettings Files => Current.Files;

        // Convenience methods
        public static async Task LoadAsync() => await Instance.LoadAsync();
        public static async Task SaveAsync() => await Instance.SaveAsync();
        public static T GetSetting<T>(string key, T defaultValue) => Instance.GetSetting(key, defaultValue);
        public static async Task SetSettingAsync<T>(string key, T value) => await Instance.SetSettingAsync(key, value);
    }
}
