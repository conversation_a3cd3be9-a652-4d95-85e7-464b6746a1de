<Window x:Class="FinancialTracker.Views.TextInputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Text Input" 
        Height="200" 
        Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Prompt Text -->
        <TextBlock Grid.Row="0" 
                   x:Name="PromptTextBlock"
                   Text="Enter text:"
                   FontSize="14"
                   Margin="0,0,0,15"
                   TextWrapping="Wrap"/>
        
        <!-- Input TextBox -->
        <TextBox Grid.Row="1"
                 x:Name="InputTextBox"
                 materialDesign:HintAssist.Hint="Type here..."
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 VerticalAlignment="Top"
                 FontSize="14"
                 Margin="0,0,0,20"/>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button x:Name="CancelButton"
                    Content="Cancel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="80"
                    Margin="0,0,10,0"
                    Click="CancelButton_Click"/>
            <Button x:Name="OkButton"
                    Content="OK"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="80"
                    Click="OkButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
