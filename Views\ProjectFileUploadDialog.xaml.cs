using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using FinancialTracker.Models;

namespace FinancialTracker.Views
{
    public partial class ProjectFileUploadDialog : Window
    {
        private readonly int _projectId;
        private string _selectedFilePath = string.Empty;
        private string _projectName = string.Empty;

        public string UploadedFileName { get; private set; } = string.Empty;

        public ProjectFileUploadDialog(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
            LoadProjectName();
        }

        private async void LoadProjectName()
        {
            try
            {
                var project = await App.DataService.GetProjectByIdAsync(_projectId);
                _projectName = project?.Name ?? $"Project_{_projectId}";

                // Update UI
                Dispatcher.Invoke(() =>
                {
                    ProjectNameText.Text = $"Project: {_projectName}";
                });
            }
            catch
            {
                _projectName = $"Project_{_projectId}";
                Dispatcher.Invoke(() =>
                {
                    ProjectNameText.Text = $"Project: {_projectName}";
                });
            }
        }

        private void SelectFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filter = "All Supported Files|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.txt;*.jpg;*.jpeg;*.png|" +
                           "PDF Files (*.pdf)|*.pdf|" +
                           "Word Documents (*.doc;*.docx)|*.doc;*.docx|" +
                           "Excel Files (*.xls;*.xlsx)|*.xls;*.xlsx|" +
                           "Text Files (*.txt)|*.txt|" +
                           "Image Files (*.jpg;*.jpeg;*.png)|*.jpg;*.jpeg;*.png|" +
                           "All Files (*.*)|*.*";

                _selectedFilePath = App.FileService.SelectFile(filter);

                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    // Update UI
                    FilePathTextBox.Text = _selectedFilePath;
                    FileNameTextBox.Text = Path.GetFileName(_selectedFilePath);
                    
                    // Show file info
                    ShowFileInfo(_selectedFilePath);
                    
                    // Enable upload button
                    UploadButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error selecting file: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowFileInfo(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                
                // Update file size
                FileSizeText.Text = $"File size: {fileInfo.Length / 1024.0:F1} KB";
                FileDetailSizeText.Text = $"{fileInfo.Length / 1024.0:F1} KB";
                
                // Update file type
                var extension = fileInfo.Extension.ToUpper().TrimStart('.');
                FileTypeText.Text = string.IsNullOrEmpty(extension) ? "Unknown" : extension;
                
                // Update last modified
                LastModifiedText.Text = fileInfo.LastWriteTime.ToString("dd/MM/yyyy HH:mm");
                
                // Show file info card
                FileInfoCard.Visibility = Visibility.Visible;
                
                // Auto-select category based on file type
                AutoSelectCategory(extension);
            }
            catch (Exception ex)
            {
                FileSizeText.Text = $"Error reading file info: {ex.Message}";
            }
        }

        private void AutoSelectCategory(string fileType)
        {
            var category = fileType.ToLower() switch
            {
                "pdf" => "Document",
                "doc" or "docx" => "Document",
                "xls" or "xlsx" => "Document",
                "txt" => "Document",
                "jpg" or "jpeg" or "png" => "Image",
                _ => "General"
            };

            // Find and select the category
            foreach (ComboBoxItem item in CategoryComboBox.Items)
            {
                if (item.Content.ToString() == category)
                {
                    CategoryComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void CategoryComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            // This method can be used to update folder preview when category changes
            // For now, it's just a placeholder for future enhancements
        }

        private async void UploadButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedFilePath))
                {
                    MessageBox.Show("Please select a file first.", "No File Selected",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(FileNameTextBox.Text))
                {
                    MessageBox.Show("Please enter a file name.", "Missing File Name",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Disable button to prevent double-click
                UploadButton.IsEnabled = false;
                UploadButton.Content = "Uploading...";

                // Get file info
                var fileInfo = new FileInfo(_selectedFilePath);
                var fileName = FileNameTextBox.Text.Trim();
                var description = DescriptionTextBox.Text?.Trim() ?? "";
                var category = ((ComboBoxItem)CategoryComboBox.SelectedItem)?.Content?.ToString() ?? "General";

                // Ensure file has extension
                if (!fileName.Contains('.'))
                {
                    fileName += fileInfo.Extension;
                }

                // Save file using FileService with project-specific directory
                var savedFilePath = await App.FileService.SaveProjectFileAsync(_selectedFilePath, _projectName, fileName, category);

                // Create ProjectFile record
                var projectFile = new ProjectFile
                {
                    ProjectId = _projectId,
                    FileName = fileName,
                    FilePath = savedFilePath,
                    Description = description,
                    Category = category,
                    FileSize = fileInfo.Length,
                    FileExtension = fileInfo.Extension,
                    CreatedDate = DateTime.Now,
                    UploadedBy = Environment.UserName,
                    IsActive = true
                };

                // Save to database
                await App.DataService.AddProjectFileAsync(projectFile);

                // Set result
                UploadedFileName = fileName;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error uploading file: {ex.Message}", "Upload Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                
                // Re-enable button
                UploadButton.IsEnabled = true;
                UploadButton.Content = "Upload File";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
