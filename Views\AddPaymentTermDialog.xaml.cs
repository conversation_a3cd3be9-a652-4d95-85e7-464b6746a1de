using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using FinancialTracker.Models;

namespace FinancialTracker
{
    public partial class AddPaymentTermDialog : Window
    {
        public ProjectPaymentTerm? PaymentTerm { get; private set; }
        public bool IgnoreSiteDivision { get; private set; }

        private readonly int _projectId;

        public AddPaymentTermDialog(int projectId)
        {
            InitializeComponent();
            _projectId = projectId;
        }

        private void PercentageTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            Regex regex = new Regex(@"^[0-9]*\.?[0-9]*$");
            e.Handled = !regex.IsMatch(e.Text);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
                {
                    MessageBox.Show("Please enter a description for the payment term.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    DescriptionTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PercentageTextBox.Text) || 
                    !decimal.TryParse(PercentageTextBox.Text, out decimal percentage) || 
                    percentage <= 0 || percentage > 100)
                {
                    MessageBox.Show("Please enter a valid percentage between 0.01 and 100.", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PercentageTextBox.Focus();
                    return;
                }

                var selectedCategory = ((ComboBoxItem)CategoryComboBox.SelectedItem)?.Content?.ToString() ?? "Equipment";

                // Create the payment term
                PaymentTerm = new ProjectPaymentTerm
                {
                    ProjectId = _projectId,
                    Category = selectedCategory,
                    Description = DescriptionTextBox.Text.Trim(),
                    Percentage = percentage,
                    TriggerCondition = string.IsNullOrWhiteSpace(TriggerConditionTextBox.Text) ?
                        null : TriggerConditionTextBox.Text.Trim(),
                    TermOrder = 1, // Will be updated when saved
                    IsActive = true,
                    IgnoreSiteDivision = IgnoreSiteDivisionCheckBox.IsChecked == true
                };

                // Store the site division preference
                IgnoreSiteDivision = IgnoreSiteDivisionCheckBox.IsChecked == true;

                // Save to database
                await App.DataService.AddProjectPaymentTermAsync(PaymentTerm);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving payment term: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
