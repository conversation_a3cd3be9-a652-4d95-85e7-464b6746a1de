using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace FinancialTracker.Data
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<FinancialContext>
    {
        public FinancialContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<FinancialContext>();
            optionsBuilder.UseSqlite("Data Source=FinancialTracker.db");

            return new FinancialContext(optionsBuilder.Options);
        }
    }
}
