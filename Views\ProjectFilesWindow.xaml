<Window x:Class="FinancialTracker.Views.ProjectFilesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Project Files Management"
        Height="850"
        Width="1300"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Background="#4A5568" Padding="24,20" CornerRadius="6" Margin="0,0,0,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FolderOpen" Width="36" Height="36" Margin="0,0,16,0" Foreground="White"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="ProjectNameText" Text="Project Files" FontSize="24" FontWeight="SemiBold" Foreground="White"/>
                        <TextBlock Text="Manage all project documents and attachments" FontSize="13" Foreground="#E5E7EB" Opacity="0.9" Margin="0,4,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Refresh Files" Margin="12,0" Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="22" Height="22" Foreground="White"/>
                    </Button>
                    <Button x:Name="AddLetterButton" Content="Add Letter" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" BorderBrush="White" Margin="12,0" Padding="16,8" FontWeight="Medium" Click="AddLetterButton_Click"/>
                    <Button x:Name="AddFileButton" Content="Add File" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" BorderBrush="White" Margin="12,0" Padding="16,8" FontWeight="Medium" Click="AddFileButton_Click"/>
                    <Button Content="Close" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="White" BorderBrush="White" Margin="12,0" Padding="16,8" FontWeight="Medium" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Files Summary - Compact Style like Dashboard -->
            <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Background="#F8F9FA" Padding="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="FileMultiple" Width="14" Height="14" Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Total Files:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                            <TextBlock x:Name="TotalFilesText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#1976D2" VerticalAlignment="Center" Margin="2,0,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="FileDocument" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Commitments:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                            <TextBlock x:Name="CommitmentFilesText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Receipt" Width="14" Height="14" Foreground="#388E3C" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Invoices:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                            <TextBlock x:Name="InvoiceFilesText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#388E3C" VerticalAlignment="Center" Margin="2,0,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Email" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Letters:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                            <TextBlock x:Name="LetterFilesText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="4" Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="HardDisk" Width="14" Height="14" Foreground="#7B1FA2" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="Size:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                            <TextBlock x:Name="TotalSizeText" Text="0 MB" FontSize="14" FontWeight="Bold" Foreground="#7B1FA2" VerticalAlignment="Center" Margin="2,0,0,0"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

            <!-- Files List -->
            <materialDesign:Card Grid.Row="1" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="FileMultiple" Width="20" Height="20"
                                                    Foreground="#1976D2" Margin="0,0,8,0"/>
                            <TextBlock Text="Project Files" FontSize="18" FontWeight="SemiBold"
                                      Foreground="#212529" VerticalAlignment="Center"/>
                        </StackPanel>



                        <ComboBox x:Name="FileTypeFilterComboBox" Grid.Column="1" Width="160"
                                  materialDesign:HintAssist.Hint="Filter by Type"
                                  SelectionChanged="FileTypeFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="All Files" IsSelected="True"/>
                            <ComboBoxItem Content="Invoice Files"/>
                            <ComboBoxItem Content="Commitment Files"/>
                            <ComboBoxItem Content="Letter Files"/>
                        </ComboBox>
                    </Grid>

                    <!-- Search Box - Compact Style -->
                    <materialDesign:Card Grid.Row="1" Padding="8" Margin="0,0,0,12" Background="#F8F9FA">
                        <StackPanel HorizontalAlignment="Center" MaxWidth="600">
                            <Grid>
                                <TextBox x:Name="FileSearchTextBox" materialDesign:HintAssist.Hint="Search files by name or source..."
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Padding="35,8,8,8" MinWidth="400" FontSize="12"
                                         TextChanged="FileSearchTextBox_TextChanged"/>
                                <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16"
                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                       Margin="10,0,0,0" Opacity="0.6"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <DataGrid Grid.Row="2" x:Name="FilesDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                              IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="Horizontal"
                              HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto"
                              Background="White" RowBackground="White" AlternatingRowBackground="#FAFBFC"
                              BorderBrush="#E1E5E9" BorderThickness="1" FontSize="13" FontFamily="Segoe UI"
                              ColumnWidth="*">

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#F8F9FA"/>
                                    <Setter Property="Foreground" Value="#495057"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Padding" Value="12,10"/>
                                    <Setter Property="BorderBrush" Value="#DEE2E6"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Height" Value="50"/>
                                    <Setter Property="BorderBrush" Value="#F1F3F4"/>
                                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F8F9FA"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Padding" Value="12,8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="DataGridCell">
                                                <Border Background="{TemplateBinding Background}"
                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </DataGrid.CellStyle>
                            <DataGrid.Columns>
                                <DataGridTemplateColumn Header="Type" Width="0.8*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="{Binding FileTypeIcon}" Width="16" Height="16"
                                                                       Foreground="{Binding FileTypeColor}" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding FileType}" FontSize="10" FontWeight="Medium" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="File Name" Width="2.5*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding FileName}" FontWeight="Medium" FontSize="11"
                                                      TextTrimming="CharacterEllipsis" ToolTip="{Binding FileName}" Margin="4,0"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Source" Width="1.2*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Source}" FontSize="10" Foreground="#6C757D"
                                                      TextTrimming="CharacterEllipsis" ToolTip="{Binding Source}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Size" Width="0.7*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding FileSizeFormatted}" FontSize="10"
                                                      HorizontalAlignment="Center" Foreground="#6C757D"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <DataGridTemplateColumn Header="Date" Width="0.8*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding DateAdded, StringFormat='{}{0:dd/MM/yyyy}'}"
                                                      FontSize="10" Foreground="#6C757D" HorizontalAlignment="Center"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="Actions" Width="1*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="26" Height="26" Margin="1"
                                                        ToolTip="Open File" Click="OpenFileButton_Click"
                                                        Background="#E3F2FD" Foreground="#1976D2">
                                                    <materialDesign:PackIcon Kind="OpenInNew" Width="12" Height="12"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="26" Height="26" Margin="1"
                                                        ToolTip="Copy Path" Click="CopyPathButton_Click"
                                                        Background="#F3E5F5" Foreground="#7B1FA2">
                                                    <materialDesign:PackIcon Kind="ContentCopy" Width="12" Height="12"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Width="26" Height="26" Margin="1"
                                                        ToolTip="Delete File" Click="DeleteFileButton_Click"
                                                        Background="#FFEBEE" Foreground="#D32F2F">
                                                    <materialDesign:PackIcon Kind="Delete" Width="12" Height="12"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,8">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" Width="16" Height="16" Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
