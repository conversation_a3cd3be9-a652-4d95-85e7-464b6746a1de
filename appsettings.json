{"database": {"connectionString": "Data Source=FinancialTracker.db", "commandTimeout": 30, "enableSensitiveDataLogging": false, "autoMigrate": true}, "ui": {"theme": "Light", "primaryColor": "Indigo", "secondaryColor": "<PERSON><PERSON>", "windowOpacity": 1, "showLoadingIndicators": true, "autoSaveInterval": 300, "dateFormat": "yyyy-MM-dd", "currencyFormat": "F2", "splashScreenDuration": 2500, "showSplashScreen": true}, "security": {"encryptSensitiveData": true, "validateFileUploads": true, "allowedFileExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".jpg", ".png"], "maxFileSize": 10485760, "logSecurityEvents": true}, "files": {"dataDirectory": "data", "backupDirectory": "backups", "autoBackup": true, "backupRetentionDays": 30, "compressBackups": true}}