#nullable enable
using FinancialTracker.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FinancialTracker.Services
{
    /// <summary>
    /// خدمة حساب النسب المكتملة للمواقع
    /// </summary>
    public static class SitePercentageCalculationService
    {
        /// <summary>
        /// حساب النسبة المكتملة لموقع معين في نوع فاتورة معين
        /// </summary>
        public static async Task<decimal> GetCompletedPercentageForSite(
            int projectId, 
            int siteId, 
            string invoiceType,
            string paymentTermDescription)
        {
            try
            {
                var dataService = App.DataService;
                var allInvoices = await dataService.GetInvoicesAsync();

                // Filter invoices for this project, type, and payment term
                // Include both site-specific invoices for this site AND "All Sites" invoices
                var relevantInvoices = allInvoices.Where(i =>
                    i.ProjectId == projectId &&
                    i.Type == invoiceType &&
                    i.InvoicePaymentTerms.Any(pt => pt.ProjectPaymentTerm?.Description == paymentTermDescription) &&
                    (i.ProjectSiteId == siteId || i.AppliesAllSites || i.SiteName.Contains("All Sites")) // Include site-specific OR All Sites invoices
                ).ToList();

                // Calculate total percentage used
                decimal totalPercentageUsed = 0;
                foreach (var invoice in relevantInvoices)
                {
                    var matchingPaymentTerm = invoice.InvoicePaymentTerms
                        .FirstOrDefault(pt => pt.ProjectPaymentTerm?.Description == paymentTermDescription);

                    if (matchingPaymentTerm != null)
                    {
                        bool isAllSitesInvoice = invoice.AppliesAllSites || invoice.SiteName.Contains("All Sites");

                        if (isAllSitesInvoice && invoice.IsFullyPaid)
                        {
                            // If this is a paid "All Sites" invoice, it consumes 100% for all sites
                            totalPercentageUsed = 100; // Mark as fully used for this site
                            break; // No need to check other invoices
                        }
                        else if (!isAllSitesInvoice)
                        {
                            // Only count site-specific invoices for this specific site
                            totalPercentageUsed += matchingPaymentTerm.AppliedPercentage;
                        }
                        // Note: Unpaid "All Sites" invoices don't consume percentage yet
                    }
                }

                return totalPercentageUsed;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Get available sites for specific payment terms (those that haven't reached 100%)
        /// </summary>
        public static async Task<List<ProjectSite>> GetAvailableSitesForPaymentTerms(
            int projectId,
            string invoiceType,
            List<string> paymentTermDescriptions)
        {
            try
            {
                var dataService = App.DataService;
                var allSites = await dataService.GetProjectSitesAsync(projectId);
                var availableSites = new List<ProjectSite>();

                foreach (var site in allSites)
                {
                    bool siteIsAvailable = true;

                    // Check each payment term for this site
                    foreach (var paymentTermDescription in paymentTermDescriptions)
                    {
                        var completedPercentage = await GetCompletedPercentageForSite(
                            projectId, site.Id, invoiceType, paymentTermDescription);

                        // If any payment term is complete (100%), site is not available
                        if (completedPercentage >= 100)
                        {
                            siteIsAvailable = false;
                            break;
                        }
                    }

                    if (siteIsAvailable)
                    {
                        availableSites.Add(site);
                    }
                }

                return availableSites;
            }
            catch
            {
                return new List<ProjectSite>();
            }
        }

        /// <summary>
        /// الحصول على المواقع المتاحة لشرط دفع معين (التي لم تكتمل 100%)
        /// </summary>
        public static async Task<List<ProjectSite>> GetAvailableSitesForPaymentTerm(
            int projectId,
            string invoiceType,
            string paymentTermDescription)
        {
            return await GetAvailableSitesForPaymentTerms(projectId, invoiceType, new List<string> { paymentTermDescription });
        }

        /// <summary>
        /// حساب النسبة المتبقية المتاحة لموقع معين
        /// </summary>
        public static async Task<decimal> GetRemainingPercentageForSite(
            int projectId,
            int siteId,
            string invoiceType,
            string paymentTermDescription)
        {
            var completedPercentage = await GetCompletedPercentageForSite(
                projectId, siteId, invoiceType, paymentTermDescription);
            
            return 100 - completedPercentage;
        }

        /// <summary>
        /// التحقق من إمكانية إضافة نسبة معينة لموقع
        /// </summary>
        public static async Task<bool> CanAddPercentageToSite(
            int projectId,
            int siteId,
            string invoiceType,
            string paymentTermDescription,
            decimal percentageToAdd)
        {
            var remainingPercentage = await GetRemainingPercentageForSite(
                projectId, siteId, invoiceType, paymentTermDescription);
            
            return percentageToAdd <= remainingPercentage;
        }

        /// <summary>
        /// الحصول على ملخص حالة جميع المواقع لشرط دفع معين
        /// </summary>
        public static async Task<List<SitePercentageSummary>> GetSitePercentageSummary(
            int projectId,
            string invoiceType,
            string paymentTermDescription)
        {
            try
            {
                var dataService = App.DataService;
                var allSites = await dataService.GetProjectSitesAsync(projectId);
                var summary = new List<SitePercentageSummary>();

                foreach (var site in allSites)
                {
                    var completedPercentage = await GetCompletedPercentageForSite(
                        projectId, site.Id, invoiceType, paymentTermDescription);

                    summary.Add(new SitePercentageSummary
                    {
                        SiteId = site.Id,
                        SiteName = site.SiteName,
                        CompletedPercentage = completedPercentage,
                        RemainingPercentage = 100 - completedPercentage,
                        IsComplete = completedPercentage >= 100
                    });
                }

                return summary.OrderBy(s => s.SiteName).ToList();
            }
            catch
            {
                return new List<SitePercentageSummary>();
            }
        }
    }

    /// <summary>
    /// ملخص حالة النسبة لموقع معين
    /// </summary>
    public class SitePercentageSummary
    {
        public int SiteId { get; set; }
        public string SiteName { get; set; } = string.Empty;
        public decimal CompletedPercentage { get; set; }
        public decimal RemainingPercentage { get; set; }
        public bool IsComplete { get; set; }
        public string StatusDisplay => IsComplete ? "مكتمل ✅" : $"متبقي {RemainingPercentage:F1}%";
    }
}
