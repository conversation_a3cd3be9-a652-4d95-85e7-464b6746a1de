<Window x:Class="FinancialTracker.InvoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Invoice Details"
        WindowState="Maximized"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterOwner"
        Background="#F7F7F7">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header - SAP Style -->
        <Border Grid.Row="0" Padding="24,20" Margin="0,0,0,24" Background="#4A5568" CornerRadius="4">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="Receipt" Width="32" Height="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock Text="Invoice Information" FontSize="24" FontWeight="SemiBold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                <TextBlock Text="Create and manage invoice details with payment terms"
                          FontSize="13" Foreground="#E5E7EB" HorizontalAlignment="Center" Opacity="0.9"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="12">
            <StackPanel Margin="0">

                <!-- Step 1: Basic Invoice Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="1" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Basic Invoice Information" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Enter invoice number" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Invoice Number -->
                            <TextBox x:Name="InvoiceNumberTextBox" Grid.Column="0"
                                     materialDesign:HintAssist.Hint="Invoice Number *"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     MaxLength="100" Height="56" Margin="0,0,8,0"
                                     FontSize="14" VerticalContentAlignment="Center"
                                     TextChanged="InvoiceNumberTextBox_TextChanged"/>

                            <!-- Invoice Type -->
                            <ComboBox x:Name="TypeComboBox" Grid.Column="1"
                                      materialDesign:HintAssist.Hint="Invoice Type *"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      Height="56" Margin="8,0,0,0" FontSize="14"
                                      SelectionChanged="TypeComboBox_SelectionChanged">
                            </ComboBox>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step1CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 1 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step1CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 2: Invoice Type & Commitment -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step2Card">
                    <StackPanel>
                        <!-- Step Header -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Number -->
                            <Border Grid.Column="0" Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="2" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <!-- Step Title -->
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Site Selection &amp; Commitment" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Select site and link to commitment (optional)" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>

                            <!-- Site Application Radio Buttons -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="Site Application:" FontWeight="Medium" FontSize="12" Foreground="#374151" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <RadioButton x:Name="SingleSiteRadio" Content="Single Site"
                                             IsChecked="True" FontSize="11" Margin="0,0,12,0"
                                             Checked="SiteRadio_Checked"/>
                                <RadioButton x:Name="AllSitesRadio" Content="All Sites"
                                             FontSize="11" Checked="SiteRadio_Checked"/>
                            </StackPanel>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left Column: Site Selection -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <!-- Site Selection Label -->
                                <TextBlock Text="Select Site:" FontWeight="Medium" FontSize="13" Margin="0,0,0,8" Foreground="#374151"/>

                                <!-- Site Selection ComboBox -->
                                <ComboBox x:Name="SiteSelectionComboBox"
                                          materialDesign:HintAssist.Hint="Select Site"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          Height="56" FontSize="14" SelectionChanged="SiteSelectionComboBox_SelectionChanged"/>

                                <!-- Site Info -->
                                <TextBlock Text="Choose the project site for this invoice"
                                          FontSize="11" Foreground="#6B7280" Margin="0,4,0,0" TextWrapping="Wrap"/>
                            </StackPanel>

                            <!-- Right Column: Commitment Selection -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <!-- Commitment Label -->
                                <TextBlock Text="Link to Commitment (Optional):" FontWeight="Medium" FontSize="13" Margin="0,0,0,8" Foreground="#374151"/>

                                <!-- Commitment ComboBox -->
                                <ComboBox x:Name="CommitmentComboBox"
                                          materialDesign:HintAssist.Hint="Select Commitment (Optional)"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          DisplayMemberPath="Title" SelectedValuePath="Id"
                                          Height="56" FontSize="14"/>

                                <!-- Commitment Info -->
                                <TextBlock Text="Link this invoice to an existing commitment for better tracking"
                                          FontSize="11" Foreground="#6B7280" Margin="0,4,0,0" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step2CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 2 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step2CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 3: Payment Settings -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step3Card">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="3" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Payment Settings" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Set payment type and amount calculation method" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- Payment Type Selection -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <RadioButton x:Name="FixedAmountRadio" Content="Fixed Amount" Grid.Column="0"
                                         IsChecked="True" FontWeight="Medium" FontSize="12" Margin="0,0,16,0"
                                         Checked="PaymentTypeRadio_Checked"/>
                            <RadioButton x:Name="PercentageRadio" Content="Multiple Payment Terms" Grid.Column="1"
                                         FontWeight="Medium" FontSize="12"
                                         Checked="PaymentTypeRadio_Checked"/>
                        </Grid>

                        <!-- Amount Settings -->
                        <StackPanel>
                            <!-- Fixed Amount Panel -->
                            <StackPanel x:Name="FixedAmountPanel">
                                <TextBox x:Name="AmountTextBox"
                                         materialDesign:HintAssist.Hint="Amount (USD) *"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="56" Margin="0,0,0,8" FontSize="14"
                                         VerticalContentAlignment="Center"
                                         TextChanged="AmountTextBox_TextChanged"/>

                                <!-- Fixed Amount Details -->
                                <TextBox x:Name="FixedAmountDetailsTextBox"
                                         materialDesign:HintAssist.Hint="Payment Details (will appear in project details)"
                                         materialDesign:HintAssist.IsFloating="True"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="80" Margin="0,0,0,8" FontSize="14"
                                         TextWrapping="Wrap" AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         TextChanged="FixedAmountDetailsTextBox_TextChanged"/>

                                <TextBlock x:Name="AmountCalculationText"
                                           Text="Enter the total amount and payment details for this invoice"
                                           FontSize="10" Foreground="#1976D2" FontStyle="Italic"/>
                            </StackPanel>

                            <!-- Percentage Panel - Split Layout -->
                            <Grid x:Name="PercentagePanel" Visibility="Collapsed">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Left Side - Payment Terms Selection -->
                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="Available Payment Terms:" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>

                                    <!-- Payment Mode Selection -->
                                    <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1" Padding="12" Margin="0,0,0,8">
                                        <StackPanel>
                                            <TextBlock Text="Payment Term Options:" FontWeight="SemiBold" FontSize="12" Foreground="#333333" Margin="0,0,0,8"/>

                                            <CheckBox x:Name="CustomPctOfTermCheckBox" Content="Use percentage of payment term"
                                                      FontSize="11" Margin="0,0,0,6"
                                                      Checked="CustomPctOfTermCheckBox_Checked" Unchecked="CustomPctOfTermCheckBox_Unchecked"/>
                                            <TextBlock Text="Example: Use 50% of a 15% payment term = 7.5% of total project"
                                                       FontSize="10" Foreground="#666666" Margin="20,0,0,8"/>

                                            <CheckBox x:Name="UseFullTermCheckBox" Content="Use full payment term (no site division)"
                                                      FontSize="11" Margin="0,0,0,6"
                                                      Checked="UseFullTermCheckBox_Checked" Unchecked="UseFullTermCheckBox_Unchecked"/>
                                            <TextBlock Text="Example: Use full 30% payment term regardless of number of sites"
                                                       FontSize="10" Foreground="#666666" Margin="20,0,0,8"/>

                                            <!-- Site Division Info -->
                                            <Border Background="#FFF3E0" BorderBrush="#FF9800" BorderThickness="1" Padding="8" CornerRadius="4">
                                                <StackPanel>
                                                    <TextBlock Text="💡 Site Division Info:" FontWeight="SemiBold" FontSize="11" Foreground="#E65100" Margin="0,0,0,4"/>
                                                    <TextBlock Text="🌐 = Applies to all sites combined (no division)" FontSize="10" Foreground="#BF360C" Margin="0,0,0,2"/>
                                                    <TextBlock Text="📍 = Divided by number of sites" FontSize="10" Foreground="#BF360C"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Border>

                                    <!-- Payment Terms List -->
                                    <materialDesign:Card Background="#F5F5F5" Padding="8" Margin="0,0,0,8">
                                        <ScrollViewer x:Name="PaymentTermsScrollViewer" MaxHeight="400" VerticalScrollBarVisibility="Auto">
                                            <StackPanel x:Name="PaymentTermsStackPanel"/>
                                        </ScrollViewer>
                                    </materialDesign:Card>

                                </StackPanel>

                                <!-- Right Side - Professional Payment Summary -->
                                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                    <!-- Professional Selected Terms Summary Panel -->
                                    <materialDesign:Card x:Name="RightSideSelectedTermsCard"
                                                         Background="White"
                                                         materialDesign:ShadowAssist.ShadowDepth="Depth2"
                                                         Padding="20" Margin="0,0,0,12"
                                                         Visibility="Collapsed">
                                        <StackPanel>
                                            <!-- Header with Icon -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                                <materialDesign:PackIcon Kind="CreditCardCheck"
                                                                       Width="24" Height="24"
                                                                       Foreground="#1976D2"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="Selected Payment Terms"
                                                          FontWeight="Bold"
                                                          FontSize="16"
                                                          Foreground="#1976D2"/>
                                            </StackPanel>

                                            <!-- Payment Terms Details -->
                                            <ScrollViewer x:Name="RightSideTermsScrollViewer"
                                                         MaxHeight="350"
                                                         VerticalScrollBarVisibility="Auto"
                                                         Margin="0,0,0,16">
                                                <StackPanel x:Name="RightSideTermsDetailsPanel"/>
                                            </ScrollViewer>

                                            <!-- Professional Total Summary -->
                                            <materialDesign:Card Background="#F8F9FA"
                                                               materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                                               Padding="16">
                                                <StackPanel>
                                                    <!-- Summary Header -->
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                        <materialDesign:PackIcon Kind="Calculator"
                                                                               Width="18" Height="18"
                                                                               Foreground="#4CAF50"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,6,0"/>
                                                        <TextBlock Text="Summary"
                                                                  FontWeight="SemiBold"
                                                                  FontSize="14"
                                                                  Foreground="#4CAF50"/>
                                                    </StackPanel>

                                                    <!-- Summary Details -->
                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- Terms Count -->
                                                        <materialDesign:PackIcon Kind="ListStatus" Grid.Row="0" Grid.Column="0"
                                                                               Width="14" Height="14"
                                                                               Foreground="#666666"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,4"/>
                                                        <TextBlock x:Name="TotalTermsCountText" Grid.Row="0" Grid.Column="1"
                                                                  Text="Total Terms: 0"
                                                                  FontSize="12"
                                                                  Foreground="#333333"
                                                                  Margin="0,0,0,4"/>

                                                        <!-- Percentage -->
                                                        <materialDesign:PackIcon Kind="Percent" Grid.Row="1" Grid.Column="0"
                                                                               Width="14" Height="14"
                                                                               Foreground="#666666"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,4"/>
                                                        <TextBlock x:Name="TotalPercentageText" Grid.Row="1" Grid.Column="1"
                                                                  Text="Total Percentage: 0%"
                                                                  FontSize="12"
                                                                  Foreground="#FF9800"
                                                                  FontWeight="Medium"
                                                                  Margin="0,0,0,4"/>

                                                        <!-- Amount -->
                                                        <materialDesign:PackIcon Kind="CurrencyUsd" Grid.Row="2" Grid.Column="0"
                                                                               Width="14" Height="14"
                                                                               Foreground="#666666"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,4"/>
                                                        <TextBlock x:Name="TotalAmountText" Grid.Row="2" Grid.Column="1"
                                                                  Text="Total Amount: $0.00"
                                                                  FontSize="14"
                                                                  FontWeight="Bold"
                                                                  Foreground="#E91E63"
                                                                  Margin="0,0,0,8"/>

                                                        <!-- Site Info -->
                                                        <materialDesign:PackIcon Kind="MapMarker" Grid.Row="3" Grid.Column="0"
                                                                               Width="14" Height="14"
                                                                               Foreground="#666666"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,0"/>
                                                        <TextBlock x:Name="PaymentSiteInfoText" Grid.Row="3" Grid.Column="1"
                                                                  Text=""
                                                                  FontSize="11"
                                                                  Foreground="#666666"
                                                                  FontStyle="Italic"/>
                                                    </Grid>
                                                </StackPanel>
                                            </materialDesign:Card>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step3CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 3 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step3CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 4: Dates & Financial Details -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step4Card">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="4" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Dates &amp; Financial Details" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Set important dates and exchange rate" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <DatePicker x:Name="ArrivalDatePicker" Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Arrival Date"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        Margin="0,0,4,0" Height="56" FontSize="14"/>

                            <DatePicker x:Name="SignatureDatePicker" Grid.Column="1"
                                        materialDesign:HintAssist.Hint="Signature Date"
                                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                        Margin="4,0,4,0" Height="56" FontSize="14"/>

                            <TextBox x:Name="ExchangeRateTextBox" Grid.Column="2"
                                     materialDesign:HintAssist.Hint="Exchange Rate"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     Height="56" Margin="4,0,4,0" FontSize="14"
                                     VerticalContentAlignment="Center"
                                     TextChanged="ExchangeRateTextBox_TextChanged"/>

                            <TextBox x:Name="PaidAmountTextBox" Grid.Column="3"
                                     materialDesign:HintAssist.Hint="Paid Amount"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     Height="56" Margin="4,0,0,0" FontSize="14"
                                     VerticalContentAlignment="Center"
                                     TextChanged="PaidAmountTextBox_TextChanged"/>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step4CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 4 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step4CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Step 5: Additional Information -->
                <Border Background="White" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="6" Padding="20" Margin="0,0,0,16" x:Name="Step5Card">
                    <StackPanel>
                        <!-- Step Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <Border Width="32" Height="32" Background="#4A5568" CornerRadius="16" Margin="0,0,12,0">
                                <TextBlock Text="5" FontSize="16" FontWeight="Bold" Foreground="White"
                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="Additional Information" FontSize="18" FontWeight="SemiBold" Foreground="#374151"/>
                                <TextBlock Text="Add description and attach files (optional)" FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left Column: Description -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="DescriptionTextBox"
                                         materialDesign:HintAssist.Hint="Description (Optional)"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Height="120" FontSize="14" TextWrapping="Wrap"
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>

                            <!-- Right Column: File Attachments -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <!-- Invoice File -->
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="AttachedFilePathTextBox" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="Invoice File"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Height="56" IsReadOnly="True" Margin="0,0,8,0" FontSize="14"
                                             VerticalContentAlignment="Center"/>

                                    <Button x:Name="BrowseFileButton" Grid.Column="1" Content="Browse"
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Height="56" Padding="16,0" FontSize="14"
                                            Click="BrowseFileButton_Click"/>
                                </Grid>

                                <!-- Letter File -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="LetterFilePathTextBox" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="Letter File"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Height="56" IsReadOnly="True" Margin="0,0,8,0" FontSize="14"
                                             VerticalContentAlignment="Center"/>

                                    <Button x:Name="BrowseLetterButton" Grid.Column="1" Content="Browse"
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Height="56" Padding="16,0" FontSize="14"
                                            Click="SelectLetterButton_Click"/>
                                </Grid>
                            </StackPanel>
                        </Grid>

                        <!-- Progress Indicator -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14"
                                                   Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,4,0"
                                                   x:Name="Step5CompleteIcon" Visibility="Collapsed"/>
                            <TextBlock Text="Step 5 Complete" FontSize="11" Foreground="#4CAF50"
                                     x:Name="Step5CompleteText" Visibility="Collapsed"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0" Padding="24,20" Margin="0,16,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Final Summary -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock x:Name="FinalValidationText"
                              Text="Complete all required fields to enable saving"
                              FontSize="13" Foreground="#6B7280" Margin="0,0,0,4"/>

                    <TextBlock x:Name="FinalSummaryDisplay"
                              Text="Invoice summary will appear here"
                              FontSize="12" Foreground="#059669" FontWeight="Medium"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Padding="20,12" MinWidth="120" Margin="0,0,16,0"
                            BorderBrush="#6B7280" Foreground="#374151"
                            Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Cancel" VerticalAlignment="Center" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SaveButton" Style="{StaticResource MaterialDesignRaisedButton}"
                            Padding="20,12" MinWidth="140" Background="#4A5568"
                            Click="SaveButton_Click" IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="Save Invoice" VerticalAlignment="Center" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
