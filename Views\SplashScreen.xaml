<Window x:Class="FinancialTracker.Views.SplashScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Financial Tracker"
        Height="400" Width="600"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Topmost="True">
    
    <Grid>
        <!-- Background with gradient -->
        <Border CornerRadius="20" Effect="{DynamicResource MaterialDesignShadowDepth3}">
            <Border.Background>
                <RadialGradientBrush>
                    <GradientStop Color="#7B2CBF" Offset="0"/>
                    <GradientStop Color="#5A189A" Offset="1"/>
                </RadialGradientBrush>
            </Border.Background>
        </Border>
        
        <!-- Content -->
        <Grid Margin="40">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Logo Container -->
            <Viewbox Grid.Row="1" Height="120" Width="120" HorizontalAlignment="Center" Name="LogoContainer">
                <Viewbox.RenderTransform>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </Viewbox.RenderTransform>
                <Viewbox.RenderTransformOrigin>
                    <Point X="0.5" Y="0.5"/>
                </Viewbox.RenderTransformOrigin>
                <Grid Width="120" Height="120">
                    <!-- Circle background -->
                    <Ellipse Width="120" Height="120" Fill="#7B2CBF" Stroke="White" StrokeThickness="3">
                        <Ellipse.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                        </Ellipse.Effect>
                    </Ellipse>

                    <!-- "we" text -->
                    <TextBlock Text="we"
                               FontFamily="Segoe UI"
                               FontWeight="Bold"
                               FontSize="38"
                               Foreground="White"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.5" BlurRadius="2"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </Grid>
            </Viewbox>
            
            <!-- App Title -->
            <TextBlock Grid.Row="2" 
                       Text="Financial Tracker" 
                       FontFamily="Segoe UI Light" 
                       FontSize="28" 
                       FontWeight="Light" 
                       Foreground="White" 
                       HorizontalAlignment="Center" 
                       Margin="0,20,0,10"/>
            
            <!-- Developer Credit and Last Modified Date -->
            <StackPanel Grid.Row="3" HorizontalAlignment="Center" Margin="0,0,0,20">
                <TextBlock Text="Developed by Mostafa Yassin"
                           FontFamily="Segoe UI"
                           FontSize="14"
                           FontWeight="Normal"
                           Foreground="#E0E0E0"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,5"/>
                <TextBlock x:Name="LastModifiedText"
                           Text="Last Modified: 26 Oct 2025"
                           FontFamily="Segoe UI"
                           FontSize="11"
                           FontWeight="Light"
                           Foreground="#B0B0B0"
                           HorizontalAlignment="Center"/>
            </StackPanel>
            
            <!-- Loading Animation -->
            <Grid Grid.Row="4" VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Loading Text -->
                <TextBlock Grid.Row="0" 
                           Text="Loading..." 
                           FontFamily="Segoe UI" 
                           FontSize="12" 
                           Foreground="#B0B0B0" 
                           HorizontalAlignment="Center" 
                           Margin="0,0,0,10"/>
                
                <!-- Progress Bar -->
                <ProgressBar Grid.Row="1"
                             Name="LoadingProgressBar"
                             Width="200"
                             Height="4"
                             IsIndeterminate="True"
                             Foreground="White"
                             Background="#40FFFFFF"
                             BorderThickness="0"/>
            </Grid>
        </Grid>
        
        <!-- Animations -->
        <Grid.Triggers>
            <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <!-- Fade in animation -->
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                         From="0" To="1" Duration="0:0:0.8"
                                         DecelerationRatio="0.3"/>

                        <!-- Logo scale animation -->
                        <DoubleAnimation Storyboard.TargetName="LogoContainer"
                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         From="0.5" To="1" Duration="0:0:1.2"
                                         DecelerationRatio="0.4"
                                         BeginTime="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetName="LogoContainer"
                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         From="0.5" To="1" Duration="0:0:1.2"
                                         DecelerationRatio="0.4"
                                         BeginTime="0:0:0.2"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Grid.Triggers>
    </Grid>
</Window>
