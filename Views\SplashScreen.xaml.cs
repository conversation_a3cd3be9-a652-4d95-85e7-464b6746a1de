using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Animation;

namespace FinancialTracker.Views
{
    public partial class SplashScreen : Window
    {
        public SplashScreen()
        {
            InitializeComponent();

            // Set last modified date to current date
            LastModifiedText.Text = $"Last Modified: {DateTime.Now:dd MMM yyyy}";
        }

        /// <summary>
        /// Shows the splash screen for a minimum duration and then closes it
        /// </summary>
        /// <param name="minimumDisplayTime">Minimum time to show splash screen in milliseconds</param>
        public async Task ShowSplashAsync(int minimumDisplayTime = 2000)
        {
            this.Show();
            
            // Wait for minimum display time
            await Task.Delay(minimumDisplayTime);
            
            // Fade out animation
            var fadeOut = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromMilliseconds(500),
                DecelerationRatio = 0.3
            };
            
            fadeOut.Completed += (s, e) => this.Close();
            this.BeginAnimation(UIElement.OpacityProperty, fadeOut);
        }

        /// <summary>
        /// Closes the splash screen with fade out animation
        /// </summary>
        public void CloseSplash()
        {
            var fadeOut = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromMilliseconds(500),
                DecelerationRatio = 0.3
            };
            
            fadeOut.Completed += (s, e) => this.Close();
            this.BeginAnimation(UIElement.OpacityProperty, fadeOut);
        }
    }
}
