using Microsoft.EntityFrameworkCore;
using FinancialTracker.Models;
using FinancialTracker.Helpers;
using System;
using System.IO;

namespace FinancialTracker.Data
{
    public class FinancialContext : DbContext
    {
        public FinancialContext() { }

        public FinancialContext(DbContextOptions<FinancialContext> options) : base(options) { }

        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<Commitment> Commitments { get; set; } = null!;
        public DbSet<Reply> Replies { get; set; } = null!;
        public DbSet<ProjectSite> ProjectSites { get; set; } = null!;
        public DbSet<ProjectInvoiceType> ProjectInvoiceTypes { get; set; } = null!;
        public DbSet<ProjectPaymentTerm> ProjectPaymentTerms { get; set; } = null!;
        public DbSet<InvoicePaymentTerm> InvoicePaymentTerms { get; set; } = null!;
        public DbSet<ProjectFile> ProjectFiles { get; set; } = null!;
        public DbSet<Activity> Activities { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var connectionString = Config.Database.ConnectionString;
                optionsBuilder.UseSqlite(connectionString);
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Project)
                .WithMany(p => p.Invoices)
                .HasForeignKey(i => i.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Commitment)
                .WithMany(c => c.Invoices)
                .HasForeignKey(i => i.CommitmentId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Commitment>()
                .HasOne(c => c.Project)
                .WithMany(p => p.Commitments)
                .HasForeignKey(c => c.ProjectId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Reply>()
                .HasOne(r => r.Invoice)
                .WithMany(i => i.Replies)
                .HasForeignKey(r => r.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Reply>()
                .HasOne(r => r.Commitment)
                .WithMany(c => c.Replies)
                .HasForeignKey(r => r.CommitmentId)
                .OnDelete(DeleteBehavior.Cascade);

            // Project Sites
            modelBuilder.Entity<ProjectSite>()
                .HasOne(ps => ps.Project)
                .WithMany(p => p.Sites)
                .HasForeignKey(ps => ps.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.ProjectSite)
                .WithMany(ps => ps.Invoices)
                .HasForeignKey(i => i.ProjectSiteId)
                .OnDelete(DeleteBehavior.SetNull);

            // Project Invoice Types
            modelBuilder.Entity<ProjectInvoiceType>()
                .HasOne(pit => pit.Project)
                .WithMany(p => p.InvoiceTypes)
                .HasForeignKey(pit => pit.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            // Project Payment Terms
            modelBuilder.Entity<ProjectPaymentTerm>()
                .HasOne(ppt => ppt.Project)
                .WithMany(p => p.PaymentTerms)
                .HasForeignKey(ppt => ppt.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            // Invoice Payment Terms
            modelBuilder.Entity<InvoicePaymentTerm>()
                .HasOne(ipt => ipt.Invoice)
                .WithMany(i => i.InvoicePaymentTerms)
                .HasForeignKey(ipt => ipt.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<InvoicePaymentTerm>()
                .HasOne(ipt => ipt.ProjectPaymentTerm)
                .WithMany()
                .HasForeignKey(ipt => ipt.ProjectPaymentTermId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InvoicePaymentTerm>()
                .HasOne(ipt => ipt.ProjectSite)
                .WithMany()
                .HasForeignKey(ipt => ipt.ProjectSiteId);

            // Activity relationships
            modelBuilder.Entity<Activity>()
                .HasOne(a => a.Project)
                .WithMany()
                .HasForeignKey(a => a.ProjectId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Activity>()
                .HasOne(a => a.Invoice)
                .WithMany()
                .HasForeignKey(a => a.InvoiceId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Activity>()
                .HasOne(a => a.Commitment)
                .WithMany()
                .HasForeignKey(a => a.CommitmentId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure default values
            modelBuilder.Entity<Invoice>()
                .Property(i => i.PaidAmount)
                .HasDefaultValue(0m);

            modelBuilder.Entity<Invoice>()
                .Property(i => i.Type)
                .HasDefaultValue("Task");

            modelBuilder.Entity<Project>()
                .Property(p => p.Status)
                .HasDefaultValue("Active");

            modelBuilder.Entity<Project>()
                .Property(p => p.NumberOfSites)
                .HasDefaultValue(1);

            modelBuilder.Entity<Invoice>()
                .Property(i => i.SiteName)
                .HasDefaultValue("");

            // No seed data - user will add their own data
        }
    }
}
